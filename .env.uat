# 测试环境：只在打包时使用
NODE_ENV=production

VITE_DEV=false

# 请求路径
# VITE_BASE_URL='http://172.18.135.34:28080'
VITE_BASE_URL=''

# 文件上传类型：server - 后端上传， client - 前端直连上传，仅支持S3服务
VITE_UPLOAD_TYPE=server

# 接口地址
VITE_API_URL=/admin-api

# olp一期接口地址
VITE_PHASE_API_URL=/admin-api/adapter/v1

# 文件上传接口地址
VITE_APP_BASE_API=/admin-api/adapter/v1

# 是否删除debugger
VITE_DROP_DEBUGGER=true

# 是否删除console.log
VITE_DROP_CONSOLE=true

# 是否sourcemap
VITE_SOURCEMAP=false

# 打包路径
VITE_BASE_PATH=/admin

# 输出路径
VITE_OUT_DIR=dist/olp-admin

# 商城H5会员端域名
VITE_MALL_H5_DOMAIN='http://mall.yudao.iocoder.cn'

# 默认账户密码
VITE_APP_DEFAULT_LOGIN_USERNAME=''
VITE_APP_DEFAULT_LOGIN_PASSWORD=''