# 本地开发环境：本地启动所有项目（前端、后端、APP）时使用，不依赖外部环境
NODE_ENV=development

VITE_DEV=true

# 请求路径
VITE_BASE_URL='http://10.248.18.22:48080'
# 测试环境
# VITE_BASE_URL='https://test.admin.v2.olp.credat.com.cn'


#VITE_BASE_URL='http://10.248.18.22:18080'
# 刘佳琪
#VITE_BASE_URL='http://10.248.17.40:48080'
# 暴晓琪
# VITE_BASE_URL='http://10.248.17.23:48080'

# 文件上传类型：server - 后端上传， client - 前端直连上传，仅支持 S3 服务
VITE_UPLOAD_TYPE=server

# 接口地址
VITE_API_URL=/admin-api

# olp一期接口地址
VITE_PHASE_API_URL=/admin-api/adapter/v1

# 文件上传接口地址
VITE_APP_BASE_API=/admin-api/adapter/v1

# 是否删除debugger
VITE_DROP_DEBUGGER=false

# 是否删除console.log
VITE_DROP_CONSOLE=false

# 是否sourcemap
VITE_SOURCEMAP=false

# 打包路径
VITE_BASE_PATH=/

VITE_OUT_DIR=dist/olp-admin

# 商城H5会员端域名
VITE_MALL_H5_DOMAIN='http://localhost:3000'

# 验证码的开关
VITE_APP_CAPTCHA_ENABLE=false

#对象存储器编码
VITE_STORAGE_CODE=s3_minio_1

# 默认账户密码
VITE_APP_DEFAULT_LOGIN_USERNAME='admin'
VITE_APP_DEFAULT_LOGIN_PASSWORD='admin123'
