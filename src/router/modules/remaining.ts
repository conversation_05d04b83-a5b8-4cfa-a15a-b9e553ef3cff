import { Layout } from '@/utils/routerHelper'

const { t } = useI18n()
/**
 * redirect: noredirect        当设置 noredirect 的时候该路由在面包屑导航中不可被点击
 * name:'router-name'          设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题
 * meta : {
 hidden: true              当设置 true 的时候该路由不会再侧边栏出现 如404，login等页面(默认 false)

 alwaysShow: true          当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式，
 只有一个时，会将那个子路由当做根路由显示在侧边栏，
 若你想不管路由下面的 children 声明的个数都显示你的根路由，
 你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，
 一直显示根路由(默认 false)

 title: 'title'            设置该路由在侧边栏和面包屑中展示的名字

 icon: 'svg-name'          设置该路由的图标

 noCache: true             如果设置为true，则不会被 <keep-alive> 缓存(默认 false)

 breadcrumb: false         如果设置为false，则不会在breadcrumb面包屑中显示(默认 true)

 affix: true               如果设置为true，则会一直固定在tag项中(默认 false)

 noTagsView: true          如果设置为true，则不会出现在tag中(默认 false)

 activeMenu: '/dashboard'  显示高亮的路由路径

 followAuth: '/dashboard'  跟随哪个路由进行权限过滤

 canTo: true               设置为true即使hidden为true，也依然可以进行路由跳转(默认 false)
 }
 **/
const remainingRouter: AppRouteRecordRaw[] = [
  {
    path: '/redirect',
    component: Layout,
    name: 'RedirectLayout',
    children: [
      {
        path: '/redirect/:path(.*)',
        name: 'Redirect',
        component: () => import('@/views/Redirect/Redirect.vue'),
        meta: {}
      }
    ],
    meta: {
      hidden: true,
      noTagsView: true
    }
  },
  {
    path: '/',
    component: Layout,
    redirect: '/index',
    name: 'Home',
    meta: {},
    children: [
      {
        path: 'index',
        component: () => import('@/views/Home/Index.vue'),
        name: 'Index',
        meta: {
          title: t('router.home'),
          icon: 'ep:home-filled',
          noCache: false,
          affix: true
        }
      },
    ]
  },
  {
    path: '/v3/callback',
    name: 'authing',
    meta: {
      hidden: true,
      noCache: true
    },
    component: () => import('@/views/Login/components/Authing.vue')
  },
  {
    path: '/user',
    component: Layout,
    name: 'UserInfo',
    meta: {
      hidden: true
    },
    children: [
      {
        path: 'profile',
        component: () => import('@/views/Profile/Index.vue'),
        name: 'Profile',
        meta: {
          canTo: true,
          hidden: true,
          noTagsView: false,
          icon: 'ep:user',
          title: t('common.profile')
        }
      },
      {
        path: 'notify-message',
        component: () => import('@/views/system/notify/my/index.vue'),
        name: 'MyNotifyMessage',
        meta: {
          canTo: true,
          hidden: true,
          noTagsView: false,
          icon: 'ep:message',
          title: '我的站内信'
        }
      },
      {
        path: '/version',
        component: () => import('@/views/version/index.vue'),
        name: 'Version',
        meta: {
          canTo: true,
          hidden: true,
          noTagsView: false,
          icon: 'ep:info-filled',
          title: t('router.versionInfo')
        }
      }
    ]
  },
  {
    path: '/dict',
    component: Layout,
    name: 'dict',
    meta: {
      hidden: true
    },
    children: [
      {
        path: 'type/data/:dictType',
        component: () => import('@/views/system/dict/data/index.vue'),
        name: 'SystemDictData',
        meta: {
          title: t('router.dictName'),
          noCache: true,
          hidden: true,
          canTo: true,
          icon: '',
          activeMenu: '/system/dict'
        }
      }
    ]
  },

  {
    path: '/codegen',
    component: Layout,
    name: 'CodegenEdit',
    meta: {
      hidden: true
    },
    children: [
      {
        path: 'edit',
        component: () => import('@/views/infra/codegen/EditTable.vue'),
        name: 'InfraCodegenEditTable',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          icon: 'ep:edit',
          title: '修改生成配置',
          activeMenu: 'infra/codegen/index'
        }
      }
    ]
  },
  {
    path: '/job',
    component: Layout,
    name: 'JobL',
    meta: {
      hidden: true
    },
    children: [
      {
        path: 'job-log',
        component: () => import('@/views/infra/job/logger/index.vue'),
        name: 'InfraJobLog',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          icon: 'ep:edit',
          title: '调度日志',
          activeMenu: 'infra/job/index'
        }
      }
    ]
  },
  {
    path: '/login',
    component: () => import('@/views/Login/Login.vue'),
    name: 'Login',
    meta: {
      hidden: true,
      title: t('router.login'),
      noTagsView: true
    }
  },
  {
    path: '/sso',
    component: () => import('@/views/Login/Login.vue'),
    name: 'SSOLogin',
    meta: {
      hidden: true,
      title: t('router.login'),
      noTagsView: true
    }
  },
  {
    path: '/social-login',
    component: () => import('@/views/Login/SocialLogin.vue'),
    name: 'SocialLogin',
    meta: {
      hidden: true,
      title: t('router.socialLogin'),
      noTagsView: true
    }
  },
  {
    path: '/403',
    component: () => import('@/views/Error/403.vue'),
    name: 'NoAccess',
    meta: {
      hidden: true,
      title: '403',
      noTagsView: true
    }
  },
  {
    path: '/404',
    component: () => import('@/views/Error/404.vue'),
    name: 'NoFound',
    meta: {
      hidden: true,
      title: '404',
      noTagsView: true
    }
  },
  {
    path: '/500',
    component: () => import('@/views/Error/500.vue'),
    name: 'Error',
    meta: {
      hidden: true,
      title: '500',
      noTagsView: true
    }
  },
  // 扫描工卡路由
  {
    path: '/scan',
    component: () => import('@/views/scan/index.vue'),
    name: 'ScanQrCode',
    meta: {
      hidden: true,
      title: 'Scan',
      noTagsView: true
    }
  },
  /*{
    path: '/bpm',
    component: Layout,
    name: 'bpm',
    meta: {
      hidden: true
    },
    children: [
      {
        path: 'manager/form/edit',
        component: () => import('@/views/bpm/form/editor/index.vue'),
        name: 'BpmFormEditor',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '设计流程表单',
          activeMenu: '/bpm/manager/form'
        }
      },
      {
        path: 'manager/model/edit',
        component: () => import('@/views/bpm/model/editor/index.vue'),
        name: 'BpmModelEditor',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '设计流程',
          activeMenu: '/bpm/manager/model'
        }
      },
      // {
      //   path: 'manager/simple/model',
      //   component: () => import('@/views/bpm/simple/SimpleModelDesign.vue'),
      //   name: 'SimpleModelDesign',
      //   meta: {
      //     noCache: true,
      //     hidden: true,
      //     canTo: true,
      //     title: '仿钉钉设计流程',
      //     activeMenu: '/bpm/manager/model'
      //   }
      // },
      {
        path: 'manager/definition',
        component: () => import('@/views/bpm/definition/index.vue'),
        name: 'BpmProcessDefinition',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '流程定义',
          activeMenu: '/bpm/manager/model'
        }
      },
      {
        path: 'process-instance/detail',
        component: () => import('@/views/bpm/processInstance/detail/index.vue'),
        name: 'BpmProcessInstanceDetail',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '流程详情',
          activeMenu: '/bpm/task/my'
        },
        props: (route) => ({
          id: route.query.id,
          taskId: route.query.taskId,
          activityId: route.query.activityId
        })
      },
      {
        path: 'oa/leave/create',
        component: () => import('@/views/bpm/oa/leave/create.vue'),
        name: 'OALeaveCreate',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '发起 OA 请假',
          activeMenu: '/bpm/oa/leave'
        }
      },
      {
        path: 'oa/leave/detail',
        component: () => import('@/views/bpm/oa/leave/detail.vue'),
        name: 'OALeaveDetail',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '查看 OA 请假',
          activeMenu: '/bpm/oa/leave'
        }
      }
    ]
  },*/

  {
    path: '/bpm',
    component: Layout,
    name: 'bpm',
    meta: {
      hidden: true
    },
    children: [
      {
        path: 'manager/form/edit',
        component: () => import('@/views/bpm/form/editor/index.vue'),
        name: 'BpmFormEditor',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '设计流程表单',
          activeMenu: '/bpm/manager/form'
        }
      },
      {
        path: 'manager/definition',
        component: () => import('@/views/bpm/model/definition/index.vue'),
        name: 'BpmProcessDefinition',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '流程定义',
          activeMenu: '/bpm/manager/model'
        }
      },
      {
        path: 'process-instance/detail',
        component: () => import('@/views/bpm/processInstance/detail/index.vue'),
        name: 'BpmProcessInstanceDetail',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          icon: 'ep:view',
          title: t('router.processDetail'),
          activeMenu: '/bpm/task/my'
        },
        props: (route) => ({
          id: route.query.id,
          taskId: route.query.taskId,
          activityId: route.query.activityId
        })
      },
      {
        path: 'process-instance/report',
        component: () => import('@/views/bpm/processInstance/report/index.vue'),
        name: 'BpmProcessInstanceReport',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '数据报表',
          activeMenu: '/bpm/manager/model'
        }
      },
      {
        path: 'oa/leave/create',
        component: () => import('@/views/bpm/oa/leave/create.vue'),
        name: 'OALeaveCreate',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '发起 OA 请假',
          activeMenu: '/bpm/oa/leave'
        }
      },
      {
        path: 'oa/leave/detail',
        component: () => import('@/views/bpm/oa/leave/detail.vue'),
        name: 'OALeaveDetail',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '查看 OA 请假',
          activeMenu: '/bpm/oa/leave'
        }
      },
      {
        path: 'manager/model/create',
        component: () => import('@/views/bpm/model/form/index.vue'),
        name: 'BpmModelCreate',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '创建流程',
          activeMenu: '/bpm/manager/model'
        }
      },
      {
        path: 'manager/model/:type/:id',
        component: () => import('@/views/bpm/model/form/index.vue'),
        name: 'BpmModelUpdate',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '修改流程',
          activeMenu: '/bpm/manager/model'
        }
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    component: () => import('@/views/Error/404.vue'),
    name: '',
    meta: {
      title: '404',
      hidden: true,
      breadcrumb: false
    }
  },
  // 添加banner页面
  {
    path: '/setting',
    component: Layout,
    name: 'Setting',
    meta: {
      hidden: true
    },
    children: [
      {
        path: '/banner/add',
        component: () => import('@/views/setting/banner/action.vue'),
        name: 'BannerAdd',
        meta: {
          noCache: false,
          hidden: true,
          canTo: true,
          icon: 'ep:plus',
          title: t('router.addBanner'),
          activeMenu: '/setting/banner'
        }
      },
      {
        path: '/banner/edit',
        component: () => import('@/views/setting/banner/action.vue'),
        name: 'BannerEdit',
        meta: {
          noCache: false,
          hidden: true,
          canTo: true,
          icon: 'ep:plus',
          title: t('router.editBanner'),
          activeMenu: '/setting/banner'
        }
      }
    ]
  },
  // 课程管理
  {
    path: '/learning-center',
    component: Layout,
    name: 'LearningCenter',
    meta: {
      hidden: true
    },
    children: [
      {
        path: '/course',
        component: () => import('@/views/learning-center/course/index.vue'),
        name: 'CourseCenter',
        meta: {
          noCache: false,
          hidden: true,
          canTo: true,
          activeMenu: '/learning-center/course'
        }
      },
      {
        path: '/importing-list',
        component: () => import('@/views/learning-center/course/components/ImportingList.vue'),
        name: 'CourseImportingList',
        meta: {
          noCache: false,
          hidden: true,
          canTo: true,
          title: t('router.importList'),
          icon: 'ep:view',
          activeMenu: '/learning-center/course'
        }
      },
      {
        path: '/course-form/index/:id?',
        component: () => import('@/views/learning-center/course/course-form/index.vue'),
        name: 'CourseForm',
        meta: {
          noCache: false,
          hidden: true,
          canTo: true,
          icon: 'ep:plus',
          title: t('router.contentCourse'),
          activeMenu: '/learning-center/course'
        }
      },
      // 学习地图
      {
        path: '/journey-form/index/:id?',
        component: () => import('@/views/learning-center/journey/journey-form/index.vue'),
        name: 'JourneyForm',
        meta: {
          noCache: false,
          hidden: true,
          canTo: true,
          icon: 'ep:plus',
          title: t('router.addJourney'),
          activeMenu: '/learning-center/journey'
        },
      },
      // 学习中心onboarding页面 添加
      {
        path: '/onboarding/addOnboarding/:id?',
        component: () => import('@/views/learning-center/onboarding/components/add-onboarding.vue'),
        name: 'AddOnboarding',
        meta: {
          noCache: false,
          hidden: true,
          canTo: true,
          icon: 'ep:plus',
          title: t('router.addOnBoarding'),
          activeMenu: '/learning-center/onboarding'
        }
      },
      // 学习中心onboarding页面  学习记录
      {
        path: '/onboarding/learning-records/:id?',
        component: () =>
          import('@/views/learning-center/onboarding/components/learning-records.vue'),
        name: 'LearningRecords',
        meta: {
          noCache: false,
          hidden: true,
          canTo: true,
          icon: 'ep:view',
          title: t('router.learningRecords'),
          activeMenu: '/learning-center/onboarding'
        },
      },
      // 学习中心orientation页面 添加
      {
        path: '/orientation/addOrientation/:id?',
        component: () =>
          import('@/views/learning-center/orientation/components/add-orientation.vue'),
        name: 'AddOrientation',
        meta: {
          noCache: false,
          hidden: true,
          canTo: true,
          icon: 'ep:plus',
          title: t('router.addOrientation'),
          activeMenu: '/learning-center/orientation'
        },
      },
      // 学习中心company页面 添加
      {
        path: '/company-policy/add-companyPolicy/:id?',
        name: 'AddCompanyPolicy',
        component: () =>
          import('@/views/learning-center/company-policy/components/add-companyPolicy.vue'),
        meta: {
          noCache: false,
          hidden: true,
          canTo: true,
          icon: 'ep:plus',
          title: t('router.addCompanyPolicy'),
          activeMenu: '/learning-center/companyPolicy'
        },
      },
      // 学习中心company页面  学习记录
      {
        path: '/company-policy/learning-records/:id?',
        name: 'CompanyLearningRecords',
        component: () =>
          import('@/views/learning-center/onboarding/components/learning-records.vue'),
        meta: {
          noCache: false,
          hidden: true,
          canTo: true,
          icon: 'ep:view',
          title: t('router.learningRecords'),
          activeMenu: '/learning-center/companyPolicy'
        },
      },
    ],
  },

  // exam 配置路由
  {
    path: '/exam-mgt',
    component: Layout,
    name: 'ExamMgt',
    meta: {
      hidden: true
    },
    children: [
      {
        path: '/exam-add/index/:id?',
        name: 'CreateExam',
        component: () => import('@/views/exam/exam-list/exam-add/index.vue'),
        meta: {
          noCache: false,
          hidden: true,
          canTo: true,
          icon: 'ep:plus',
          title: t('router.createExam'),
          activeMenu: '/exam-mgt/exam',
        },
      },
      {
        path: '/exam-edit/:id?',
        name: 'EditeExam',
        component: () => import('@/views/exam/exam-list/exam-edit/index.vue'),
        meta: {
          noCache: false,
          hidden: true,
          canTo: true,
          icon: 'ep:plus',
          title: t('router.editExam'),
          activeMenu: '/exam-mgt/exam',
        },
      },
      {
        path: '/exam-view/:id?',
        name: 'ExamView',
        component: () => import('@/views/exam/exam-list/exam-view/index.vue'),
        meta: {
          noCache: false,
          hidden: true,
          canTo: true,
          icon: 'ep:view',
          title: t('router.viewExam'),
          activeMenu: '/exam-mgt/exam',
        },
      },
      {
        path: '/exam-view/record/:id?',
        name: 'ExamRecord',
        component: () => import('@/views/exam/exam-list/exam-view/recordView.vue'),
        meta: {
          noCache: false,
          hidden: true,
          canTo: true,
          icon: 'ep:view',
          title: t('router.examRecord'),
          activeMenu: '/exam-mgt/exam'
        },
      },
      {
        path: '/paper-add/customPaper',
        name: 'CustomPaper',
        component: () => import('@/views/topicMgt/exam/exam-paper/paper-add/customPaper.vue'),
        meta: {
          noCache: false,
          hidden: true,
          canTo: true,
          icon: 'ep:plus',
          title: t('router.addCustomizedPaper'),
          activeMenu: '/exam-mgt/exampaper',
        },
      },
      {
        path: '/paper-add/autoPaper',
        name: 'AutoPaper',
        component: () => import('@/views/topicMgt/exam/exam-paper/paper-add/autoPaper.vue'),
        meta: {
          noCache: false,
          hidden: true,
          canTo: true,
          icon: 'ep:plus',
          title: t('router.addAutoPaper'),
          activeMenu: '/exam-mgt/exampaper',
        },
      },
      {
        path: '/paper-edite/:id?',
        name: 'PaperEdite',
        component: () => import('@/views/topicMgt/exam/exam-paper/paper-edite/index.vue'),
        meta: {
          noCache: false,
          hidden: true,
          canTo: true,
          icon: 'ep:plus',
          title: t('router.editPaper'),
          activeMenu: '/exam-mgt/exampaper',
        },
      },
      {
        path: '/question/management/:id?',
        component: () => import('@/views/topicMgt/question/question-management/index.vue'),
        name: 'QuestionManagement',
        meta: {
          noCache: false,
          hidden: true,
          canTo: true,
          icon: 'ep:plus',
          title: t('router.questionMgt'),
          activeMenu: '/exam-mgt/question-pool'
        }
      },
      {
        path: '/question-mgt/add/:id?',
        name: 'QuestionAdd',
        component: () => import('@/views/topicMgt/question/question-add/index.vue'),
        meta: {
          noCache: false,
          hidden: true,
          canTo: true,
          icon: 'ep:plus',
          title: t('router.addQuestion'),
          activeMenu: '/exam-mgt/question-pool',
        },
      },
      {
        path: '/question-mgt/edite/:id?',
        name: 'QuestionEdite',
        component: () => import('@/views/topicMgt/question/question-edite/index.vue'),
        meta: {
          noCache: false,
          hidden: true,
          canTo: true,
          icon: 'ep:plus',
          title: t('router.editQuestion'),
          activeMenu: '/exam-mgt/question-pool',
        },
      },
    ],
  },
  {
    path: '/statistics/exam-details/:examId?',
    component: Layout,
    name: 'ExamDetails',
    meta: {
      hidden: true
    },
    children: [
      {
        path: '/index/:id?',
        component: () => import('@/views/statistics/course/detail/index.vue'),
        name: 'CourseStatisticsDetail',
        meta: {
          noCache: false,
          hidden: true,
          canTo: true,
          title: t('router.courseDetail'),
          icon: 'ep:view',
          activeMenu: '/statistics/course-statistics'
        },
      },
    ],
  },
  {
    path: '/statistics',
    component: Layout,
    name: 'Statistics',
    meta: {
      hidden: true
    },
    children: [
      // 统计-exam详情页面
      {
        path: '/exam-details/:examId?',
        component: () => import('@/views/statistics/exam/detail.vue'),
        name: 'ExamStatisticsDetail',
        meta: {
          noCache: false,
          hidden: true,
          canTo: true,
          title: t('router.examDetail'),
          icon: 'ep:view',
          activeMenu: '/statistics/exam-statistics'
        },
      },
      // 统计-studet详情页面
      {
        path: '/student-detail',
        component: () => import('@/views/statistics/student/Detail/index.vue'),
        name: 'StudentStatisticsDetail',
        meta: {
          noCache: false,
          hidden: true,
          canTo: true,
          title: t('router.studentDetail'),
          icon: 'ep:view',
          activeMenu: '/statistics/student'
        },
      },
      {
        path: '/f1/index/:id?',
        component: () => import('@/views/statistics/course/detail/index.vue'),
        name: 'CourseStatisticsDetail',
        meta: {
          noCache: false,
          hidden: true,
          canTo: true,
          title: t('router.courseDetail'),
          icon: 'ep:view',
          activeMenu: '/statistics/course-statistics'
        },
      },
      // 统计-onboarding详情页面
      {
        path: '/f3/index/:id?',
        component: () => import('@/views/statistics/onboard/Detail/index.vue'),
        name: 'OnboardDetailStatis',
        meta: {
          noCache: false,
          hidden: true,
          canTo: true,
          title: t('router.onboardingDetail'),
          icon: 'ep:view',
          activeMenu: '/statistics/onboard-statistics'
        },
      },
      // 统计-companyPolicy详情页面
      {
        path: '/f4/index/:id?',
        component: () => import('@/views/statistics/cmp/detail/index.vue'),
        name: 'CompanyPolicyStatisticsDetail',
        meta: {
          noCache: false,
          hidden: true,
          canTo: true,
          title: t('router.companyPolicyDetail'),
          icon: 'ep:view',
          activeMenu: '/statistics/company-statistics'
        },
      },
      // 统计 MLC Training ----> 详情
      {
        path: '/statistics/mlcTraining-Info',
        component: () => import('@/views/statistics/mlcTraining/detail/index.vue'),
        name: 'MlcTrainingStatisticsDetail',
        meta: {
          noCache: false,
          hidden: true,
          canTo: true,
          title: t('router.courseDetail'),
          icon: 'ep:view',
          activeMenu: '/statistics/mlcTraining'
        },
      },
      // 统计 Live ----> 详情
      {
        path: '/statistics/liveStream-Info',
        component: () => import('@/views/statistics/liveStream/Detail/index.vue'),
        name: 'LiveStatisticsDetail',
        meta: {
          noCache: false,
          hidden: true,
          canTo: true,
          title: t('router.liveDetail'),
          icon: 'ep:view',
          activeMenu: '/statistics/liveStream'
        },
      },
    ],
  },
  {
    path: '/survey',
    component: Layout,
    name: 'SystemSurvey',
    meta: {
      hidden: true
    },
    children: [
      {
        path: '/template-list',
        component: () => import('@/views/system/survey/template/index.vue'),
        name: 'SurveyTemplateList',
        meta: {
          title: 'Template Management',
          hidden: true,
          canTo: true,
          activeMenu: '/survey/template-list',
        }
      },
      {
        path: '/instance-list',
        component: () => import('@/views/system/survey/instance/index.vue'),
        name: 'SurveyInstanceList',
        meta: {
          title: 'Instance Management',
          hidden: true,
          canTo: true,
          activeMenu: '/survey/instance-list',
        }
      },
      {
        path: 'instance',
        component: () => import('@/views/system/survey/instance/Form.vue'),
        name: 'SurveyInstanceForm',
        meta: {
          title: 'Instance Editor',
          hidden: true,
          canTo: true,
          noTagsView: true,
          noCache: true,
          activeMenu: '/survey/instance-list',
        }
      },
      {
        path: 'instance/statistics',
        component: () => import('@/views/system/survey/instance/Statistics.vue'),
        name: 'SurveyInstanceStatistics',
        meta: {
          title: 'Survey Statistics',
          hidden: true,
          canTo: true,
          noTagsView: true,
          noCache: true,
          activeMenu: '/survey/instance-list',
        }
      }
    ]
  },
  {
    path: '/class', // 课堂管理
    component: Layout,
    name: 'ClassInfo',
    meta: { hidden: true },
    children: [
      {
        path: '/class/manage', // 课堂管理信息
        component: () => import('@/views/academy/class/manage/index.vue'),
        name: 'Manage',
        meta: {
          noCache: false,
          hidden: true,
          canTo: true,
          icon: 'ep:plus',
          title: t('router.manage'),
          activeMenu: '/academy/class'
        }
      },
    ]
  }
]

export default remainingRouter
