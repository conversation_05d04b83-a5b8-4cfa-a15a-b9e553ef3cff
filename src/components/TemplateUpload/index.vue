<script setup lang="ts">
import type { ComponentInternalInstance } from 'vue'
import { onMounted } from 'vue'
import type { UploadProps } from 'element-plus'
import { formatImgUrl } from '@/utils'
import { getAccessToken } from '@/utils/auth'
import { useI18n } from "vue-i18n"
import { useDraggable } from '@vueuse/core'
import { Icon } from '@/components/Icon'

const { t } = useI18n()
const message = useMessage() // 消息弹窗
const props = withDefaults(defineProps<{
  modelValue: string | object | Array<any>
  /** 图片数量限制 */
  limit: number
  /** 图片大小限制(MB/KB) */
  fileSize: number
  /** 文件类型 */
  fileType?: string[]
  /** 图片比例 */
  fileRatio?: [number, number]
  /** 是否显示提示 */
  isShowTip?: boolean
  /** 提示文字 */
  tipText?: string
  /** 上传模块  1.course subject模块上传使用(因为使用的上传文件大小限制不一样) */
  uploadModule?: number
  coordinateDetails: any
}>(), {
  limit: 5,
  fileSize: 5,
  fileType: () => ['png', 'jpg', 'jpeg', 'GIF'],
  isShowTip: true,
  tipText: '750*442 pixels or 16:9,PNG、JPG、GIF format,under 5M',
  uploadModule: 0,
})

const emit = defineEmits(['update:modelValue','confirmName','confirmLogo','confirmNumber','confirmSeal','confirmUser','confirmTime','imageLoaded'])

const number = ref(0)
const uploadList = ref<any[]>([])
const dialogImageUrl = ref('')
const dialogVisible = ref(false)
const baseUrl = import.meta.env.VITE_APP_BASE_API
// const uploadImgUrl = ref(`${import.meta.env.VITE_APP_BASE_API}/common/upload`) // 上传的图片服务器地址
const uploadImgUrl = ref(`${import.meta.env.VITE_BASE_URL + import.meta.env.VITE_API_URL}/infra/file/direct/upload`) // 上传的图片服务器地址
const uploadData = ref({
  code: import.meta.env.VITE_STORAGE_CODE,
  uuid: 1
})
const headers = ref({ Authorization: `Bearer ${getAccessToken()}` })
const fileList = ref<any[]>([])

// 定义初始位置常量（使用计算属性以支持国际化）
const getInitialPositions = () => [
  { id: 1, label: t('common.certificateName'), x: 20, y: 60, width: 200, height: 40, elementRef: null, type: 'text' },
  { id: 2, label: t('common.certificateNumber'), x: 20, y: 100, width: 180, height: 30, elementRef: null, type: 'text' },
  { id: 3, label: t('common.certificateLogo'), x: 20, y: 220, width: 100, height: 100, elementRef: null, type: 'image' },
  { id: 4, label: t('common.certificateOfficialSeal'), x: 20, y: 340, width: 100, height: 100, elementRef: null, type: 'image' },
  { id: 5, label: t('common.certificateUser'), x: 20, y: 180, width: 150, height: 35, elementRef: null, type: 'text' },
  { id: 6, label: t('common.certificateTime'), x: 20, y: 140, width: 160, height: 30, elementRef: null, type: 'text' }
]

// 定义可拖动元素
// 1. 名称 2. 编号 3. 图标 4. 公章 5. 用户 6. 时间
const draggableItems = ref([...getInitialPositions()])


const activeItemId = ref(null)
const isResizing = ref(false)
let startPos = { x: 0, y: 0 }
let startSize = { width: 0, height: 0 }

// 图片尺寸信息
const imageInfo = reactive({
  naturalWidth: 0,
  naturalHeight: 0,
  displayWidth: 0,
  displayHeight: 0,
  scaleRatio: 1
})

// 调试开关：显示原图像素坐标
const showDebugCoords = ref(false)

// 获取元素的原图像素坐标（用于调试显示）
const getDebugCoords = (item: any) => {
  const imgCoords = viewToImgBox({ x: item.x, y: item.y, width: item.width, height: item.height })
  return `img: ${imgCoords.x},${imgCoords.y} ${imgCoords.width}x${imgCoords.height}`
}


interface Position {
  x: number
  y: number
}

interface DraggableItem {
  id: string
  label: string
  x: number
  y: number
  width?: number
  height?: number
  elementRef: HTMLElement | null
  type: 'text' | 'image'
}

interface ElementPosition extends Position {
  width?: number
  height?: number
}

interface ImagePosition extends Position {
  width: number
  height: number
}

interface CoordinateDetails {
  nameDetails: ElementPosition
  numberDetails: ElementPosition
  logoDetails: ImagePosition
  officialSealDetails: ImagePosition
  userDetails: ElementPosition
  timeDetails: ElementPosition
}

const showTip = computed(
  () => props.isShowTip && (props.fileType || props.fileSize),
)
const previewList = computed(() => {
  return fileList.value.map((fileInfo: { name: string, url: string }) => {
    fileInfo = {
      name: fileInfo.name,
      url: formatImgUrl(fileInfo.url.replace(baseUrl, '')),
    }
    return fileInfo
    // url = url.replace(baseUrl, '')
    // return formatImgUrl(url)
  })
})
watch(() => props.modelValue, (val) => {
  if (val) {
    // 首先将值转为数组
    const list: any[] = Array.isArray(val) ? val : (props.modelValue as any).split(',')
    // 然后将数组转为对象数组
    fileList.value = list.map((item) => {
      if (typeof item === 'string') {
        if (!item.includes(baseUrl))
          item = { name: baseUrl + item, url: item }
        else
          item = { name: item, url: item }
      }
      return item
    })
  }
  else {
    fileList.value = []
    return []
  }
}, { deep: true, immediate: true })

const container = ref(null);


//
// 上传前loading加载
async function handleBeforeUpload(file: any) {
  let isImg = false
  if (props.fileType.length) {
    let fileExtension = ''
    if (file.name.includes('.'))
      fileExtension = file.name.slice(file.name.lastIndexOf('.') + 1)

    isImg = props.fileType.some((type) => {
      if (file.type.includes(type))
        return true
      if (fileExtension && fileExtension.includes(type))
        return true
      return false
    })
  }
  else {
    isImg = file.type.includes('image')
  }
  if (!isImg) {
    message.error(`${t('error.fileFormatError')} ${props.fileType.join('/')} ${t('error.imageFormatFile')}`)
    return false
  }
  if (props.fileSize) {
    // 1代表是course subject模块使用上传
    const isLt = props.uploadModule === 1 ? file.size / 1024 < props.fileSize : file.size / 1024 / 1024 < props.fileSize
    if (!isLt) {
      message.error(`${t('error.imageSizeError')} ${props.fileType.join('/')} ${props.fileSize} ${props.uploadModule === 1 ? 'KB' : 'MB'}!`)

      return false
    }
  }
  if (props.fileRatio) {
    await new Promise((resolve, reject) => {
      let url = ''
      try {
        url = window.URL ? window.URL.createObjectURL(file) : window.webkitURL.createObjectURL(file)
      }
      catch (e) {
        if (window.console) {
        // window.console.error(e);
          window.console.dir(e)
        }
        const binaryData = [].concat(file)
        const blobFile = new Blob(binaryData)
        url = window.URL ? window.URL.createObjectURL(blobFile) : window.webkitURL.createObjectURL(blobFile)
      }
      const image = new Image()
      image.src = url
      image.onload = () => {
        const width = image.width
        const height = image.height

        if (width !== undefined && height !== undefined && width > 0 && height > 0) {
          if (width / height === props.fileRatio[0] / props.fileRatio[1]) {
            resolve(true)
          // return true
          }
          else {

            message.error(t('error.imageSizeErrorLength'))
            // eslint-disable-next-line prefer-promise-reject-errors
            reject(false)

          // return false
          }
        }
        else {
          message.error(t('error.imageLoading'))

          // eslint-disable-next-line prefer-promise-reject-errors
          reject(false)
        // return false
        }
      }
    })
  }
  message.loading(t('loading.upLoading'))
  number.value++
}

// 文件个数超出
function handleExceed() {
  message.error(`${t('error.fileLengthError')} ${props.limit} !`)
}
const imageUpload = ref()
// 上传成功回调
function handleUploadSuccess(res: any, file: any) {
  if (res.code === 0) {
    uploadList.value.push({ name: file.name, url: res.data.url })
    uploadedSuccessfully()
    // 上传成功时重置元素位置
    resetElementPositions()
  }
  else {
    number.value--
    message.closeLoading()
    message.error(res.msg)
    imageUpload.value.handleRemove(file)
    uploadedSuccessfully()
  }
}
// 删除图片
function handleDelete(file: File): UploadProps['beforeRemove'] {
  const findex = fileList.value.map(f => f.name).indexOf(file.name)
  if (findex > -1 && uploadList.value.length === number.value) {
    fileList.value.splice(findex, 1)
    emit('update:modelValue', listToString(fileList.value))
    // 删除图片时重置元素位置
    resetElementPositions()
    return () => false
    // return false
  }
  return () => true
}

// 上传结束处理
function uploadedSuccessfully() {
  if (number.value > 0 && uploadList.value.length === number.value) {
    fileList.value = fileList.value.filter(f => f.url !== undefined).concat(uploadList.value)
    uploadList.value = []
    number.value = 0
    emit('update:modelValue', listToString(fileList.value))
    message.closeLoading()
  }
}

// 上传失败
function handleUploadError() {
  message.error(t('error.uploadErrorMessage'))
  message.closeLoading()
}

// 预览
function handlePictureCardPreview(file: any) {
  dialogImageUrl.value = file.url
  dialogVisible.value = true
}

// 对象转成指定字符串分隔
function listToString(list: any[], separator?: string) {
  let strs = ''
  separator = separator || ','
  for (const i in list) {
    if (undefined !== list[i].url && list[i].url.indexOf('blob:') !== 0)
      strs += list[i].url.replace(baseUrl, '') + separator
  }
  return strs !== '' ? strs.substr(0, strs.length - 1) : ''
}


// 设置元素引用
const setItemRef = (el) => {
  if (!el) return
  const item = draggableItems.value.find(i => i.id === el.dataset.id)
  if (item) item.elementRef = el
}
// 获取元素样式
const getItemStyle = (item) => {
  const baseStyle = {
    left: `${item.x}px`,
    top: `${item.y}px`,
    backgroundColor: activeItemId.value === item.id ? '#ff7f50' : getColorById(item.id),
    zIndex: activeItemId.value === item.id ? 100 : 10,
    width: `${item.width || 120}px`,
    height: `${item.height || 80}px`
  }

  return baseStyle
}
// 根据ID获取不同颜色
const getColorById = (id) => {
  const colors = {
    1: '#42b983',
    2: '#3498db',
    3: '#9b59b6',
    4: '#e74c3c',
    5: '#f39c12'
  }
  return colors[id] || '#42b983'
}
const startDrag = (id, e) => {
  activeItemId.value = id
  const item = draggableItems.value.find(i => i.id === id)
  if (!item || !container.value) return

  // 获取图片相对于页面的位置（现在容器尺寸=图片尺寸）
  const imgRect = container.value.getBoundingClientRect()

  startPos = {
    x: e.clientX - imgRect.left - item.x,
    y: e.clientY - imgRect.top - item.y
  }

  console.log(`开始拖拽 ${item.id}: 鼠标(${e.clientX},${e.clientY}), 图片位置(${imgRect.left},${imgRect.top}), 元素位置(${item.x},${item.y})`)

  // 添加事件监听
  window.addEventListener('mousemove', handleMove)
  window.addEventListener('touchmove', handleMove)
  window.addEventListener('mouseup', stopDrag)
  window.addEventListener('touchend', stopDrag)

  e.preventDefault()
}
// 处理移动
const handleMove = (e) => {
  if (!activeItemId.value || !container.value) return

  const clientX = e.touches ? e.touches[0].clientX : e.clientX
  const clientY = e.touches ? e.touches[0].clientY : e.clientY

  const item = draggableItems.value.find(i => i.id === activeItemId.value)
  if (!item) return

  // 获取图片相对于页面的位置（现在容器尺寸=图片尺寸）
  const imgRect = container.value.getBoundingClientRect()

  // 计算相对于图片的新位置并取整
  let newX = Math.floor(clientX - imgRect.left - startPos.x)
  let newY = Math.floor(clientY - imgRect.top - startPos.y)

  console.log(`移动中: 鼠标(${clientX},${clientY}), 图片位置(${imgRect.left},${imgRect.top}), 计算位置(${newX},${newY})`)

  // 限制在图片内（使用图片的显示尺寸）
  const itemWidth = item.width || 120
  const itemHeight = item.height || 80

  // 使用图片的显示尺寸作为边界，而不是容器尺寸
  item.x = Math.max(0, Math.min(newX, imageInfo.displayWidth - itemWidth))
  item.y = Math.max(0, Math.min(newY, imageInfo.displayHeight - itemHeight))

  // 转换为背景原图像素后发送事件（后端规范）
  const imgCoords = viewToImgBox({ x: item.x, y: item.y, width: item.width, height: item.height })

  console.log(`Drag ${item.id}: view(${item.x},${item.y},${item.width}x${item.height}) -> img(${imgCoords.x},${imgCoords.y},${imgCoords.width}x${imgCoords.height})`)
  console.log(`Image display size: ${imageInfo.displayWidth}x${imageInfo.displayHeight}, scale: ${imageInfo.displayWidth / imageInfo.naturalWidth}`)

  // 根据元素类型发送不同的事件，发送背景原图像素坐标
  switch (item.id) {
    case 1:
      emit('confirmName', imgCoords.x, imgCoords.y, imgCoords.width, imgCoords.height)
      break
    case 2:
      emit('confirmNumber', imgCoords.x, imgCoords.y, imgCoords.width, imgCoords.height)
      break
    case 3:
      emit('confirmLogo', imgCoords.x, imgCoords.y, imgCoords.width, imgCoords.height)
      break
    case 4:
      emit('confirmSeal', imgCoords.x, imgCoords.y, imgCoords.width, imgCoords.height)
      break
    case 5:
      emit('confirmUser', imgCoords.x, imgCoords.y, imgCoords.width, imgCoords.height)
      break
    case 6:
      emit('confirmTime', imgCoords.x, imgCoords.y, imgCoords.width, imgCoords.height)
      break
    default:
      break
  }

}
// 停止拖动
const stopDrag = () => {
  activeItemId.value = null
  isResizing.value = false
  // 移除事件监听
  window.removeEventListener('mousemove', handleMove)
  window.removeEventListener('touchmove', handleMove)
  window.removeEventListener('mouseup', stopDrag)
  window.removeEventListener('touchend', stopDrag)
  window.removeEventListener('mousemove', handleResize)
  window.removeEventListener('touchmove', handleResize)
}

// 开始拉伸
const startResize = (id, e) => {
  e.stopPropagation() // 阻止触发拖拽
  activeItemId.value = id
  isResizing.value = true

  const item = draggableItems.value.find(i => i.id === id)
  if (!item) return

  startPos = {
    x: e.clientX,
    y: e.clientY
  }

  // 获取当前尺寸，文本元素和图片元素使用不同的默认值
  const defaultWidth = item.type === 'image' ? 100 : (item.type === 'text' ? 120 : 100)
  const defaultHeight = item.type === 'image' ? 100 : (item.type === 'text' ? 40 : 80)

  startSize = {
    width: item.width || defaultWidth,
    height: item.height || defaultHeight
  }

  // 添加事件监听
  window.addEventListener('mousemove', handleResize)
  window.addEventListener('touchmove', handleResize)
  window.addEventListener('mouseup', stopDrag)
  window.addEventListener('touchend', stopDrag)

  e.preventDefault()
}

// 处理拉伸
const handleResize = (e) => {
  if (!activeItemId.value || !isResizing.value) return

  const clientX = e.touches ? e.touches[0].clientX : e.clientX
  const clientY = e.touches ? e.touches[0].clientY : e.clientY

  const item = draggableItems.value.find(i => i.id === activeItemId.value)
  if (!item) return

  // 计算新尺寸
  const deltaX = clientX - startPos.x
  const deltaY = clientY - startPos.y

  // 根据元素类型设置不同的最小尺寸
  const minWidth = item.type === 'text' ? 80 : 50
  const minHeight = item.type === 'text' ? 20 : 50

  let newWidth = Math.max(minWidth, startSize.width + deltaX)
  let newHeight = Math.max(minHeight, startSize.height + deltaY)

  // 限制最大尺寸（不超过图片尺寸，考虑元素位置）
  if (imageInfo.displayWidth > 0 && imageInfo.displayHeight > 0) {
    // 确保元素不会超出图片边界
    newWidth = Math.min(newWidth, imageInfo.displayWidth - item.x)
    newHeight = Math.min(newHeight, imageInfo.displayHeight - item.y)
  }

  item.width = Math.floor(newWidth)
  item.height = Math.floor(newHeight)

  // 转换为背景原图像素后发送事件（后端规范）
  const imgCoords = viewToImgBox({ x: item.x, y: item.y, width: item.width, height: item.height })

  console.log(`Resize ${item.id}: view(${item.x},${item.y},${item.width}x${item.height}) -> img(${imgCoords.x},${imgCoords.y},${imgCoords.width}x${imgCoords.height})`)

  // 发送尺寸变化事件，发送背景原图像素坐标
  switch (item.id) {
    case 1:
      emit('confirmName', imgCoords.x, imgCoords.y, imgCoords.width, imgCoords.height)
      break
    case 2:
      emit('confirmNumber', imgCoords.x, imgCoords.y, imgCoords.width, imgCoords.height)
      break
    case 3:
      emit('confirmLogo', imgCoords.x, imgCoords.y, imgCoords.width, imgCoords.height)
      break
    case 4:
      emit('confirmSeal', imgCoords.x, imgCoords.y, imgCoords.width, imgCoords.height)
      break
    case 5:
      emit('confirmUser', imgCoords.x, imgCoords.y, imgCoords.width, imgCoords.height)
      break
    case 6:
      emit('confirmTime', imgCoords.x, imgCoords.y, imgCoords.width, imgCoords.height)
      break
  }
}
// 创建 ID 到 props 键名的映射
const idToDetailKeyMap: Record<string, keyof CoordinateDetails> = {
  '1': 'nameDetails',
  '2': 'numberDetails',
  '3': 'logoDetails',
  '4': 'officialSealDetails',
  '5': 'userDetails',
  '6': 'timeDetails'
}
// 坐标转换函数：视图像素 -> 背景原图像素（后端规范）
const viewToImgBox = (viewBox: { x: number, y: number, width?: number, height?: number }) => {
  // scale = viewWidth / bgWidth
  const scale = imageInfo.displayWidth / imageInfo.naturalWidth
  if (scale === 0) return viewBox

  return {
    x: Math.round(viewBox.x / scale),
    y: Math.round(viewBox.y / scale),
    width: viewBox.width != null ? Math.round(viewBox.width / scale) : undefined,
    height: viewBox.height != null ? Math.round(viewBox.height / scale) : undefined
  }
}

// 坐标转换函数：背景原图像素 -> 视图像素（前端显示）
const imgToViewBox = (imgBox: { x: number, y: number, width?: number, height?: number }) => {
  // scale = viewWidth / bgWidth
  const scale = imageInfo.displayWidth / imageInfo.naturalWidth
  if (scale === 0) return imgBox

  return {
    x: Math.round(imgBox.x * scale),
    y: Math.round(imgBox.y * scale),
    width: imgBox.width != null ? Math.round(imgBox.width * scale) : undefined,
    height: imgBox.height != null ? Math.round(imgBox.height * scale) : undefined
  }
}

// 同步坐标数据的函数（从后端接收的背景原图像素转换为视图像素）
const syncCoordinateData = () => {
  if (!props.coordinateDetails || imageInfo.naturalWidth === 0) return

  draggableItems.value.forEach(item => {
    const detailKey = idToDetailKeyMap[item.id]
    if (detailKey && props.coordinateDetails[detailKey]) {
      const imgCoords = props.coordinateDetails[detailKey]

      // 将背景原图像素转换为视图像素
      const viewCoords = imgToViewBox({
        x: imgCoords.x,
        y: imgCoords.y,
        width: imgCoords.width,
        height: imgCoords.height
      })

      item.x = viewCoords.x
      item.y = viewCoords.y

      // 同步width和height（后端强烈建议所有文本元素都提供）
      if (viewCoords.width !== undefined) {
        item.width = viewCoords.width
      }
      if (viewCoords.height !== undefined) {
        item.height = viewCoords.height
      }

      console.log(`Sync ${detailKey}: img(${imgCoords.x},${imgCoords.y},${imgCoords.width}x${imgCoords.height}) -> view(${viewCoords.x},${viewCoords.y},${viewCoords.width}x${viewCoords.height})`)
    }
  })
}

watch(
  () => props.coordinateDetails,
  () => {
    syncCoordinateData()
  },
  { immediate: true, deep: true }
)

// 监听语言变化，更新元素标签
watch(
  () => t('common.certificateName'), // 监听任意一个国际化文本的变化
  () => {
    // 语言切换时更新标签，但保持位置不变
    const currentPositions = draggableItems.value
    const newPositions = getInitialPositions()

    draggableItems.value = currentPositions.map((item, index) => ({
      ...item,
      label: newPositions[index].label
    }))
  }
)

// 组件挂载时初始化
onMounted(() => {
  initializeFromCoordinateDetails()
})

// 图片加载完成处理
const handleImageLoad = (event: Event) => {
  const img = event.target as HTMLImageElement
  if (img && container.value) {
    // 获取图片的原始尺寸
    const naturalWidth = img.naturalWidth
    const naturalHeight = img.naturalHeight

    // 计算合适的显示尺寸（保持宽高比，限制最大尺寸）
    const maxWidth = 800
    const maxHeight = 600

    let displayWidth = naturalWidth
    let displayHeight = naturalHeight
    let scaleRatio = 1

    // 如果图片太大，按比例缩放
    if (naturalWidth > maxWidth || naturalHeight > maxHeight) {
      const widthRatio = maxWidth / naturalWidth
      const heightRatio = maxHeight / naturalHeight
      scaleRatio = Math.min(widthRatio, heightRatio)

      displayWidth = naturalWidth * scaleRatio
      displayHeight = naturalHeight * scaleRatio
    }

    // 记录图片尺寸信息
    imageInfo.naturalWidth = naturalWidth
    imageInfo.naturalHeight = naturalHeight
    imageInfo.displayWidth = displayWidth
    imageInfo.displayHeight = displayHeight
    imageInfo.scaleRatio = scaleRatio

    // 设置图片显示尺寸 - 使用!important确保样式生效
    img.style.setProperty('width', `${displayWidth}px`, 'important')
    img.style.setProperty('height', `${displayHeight}px`, 'important')
    img.style.setProperty('object-fit', 'contain', 'important')

    // 🔧 关键修复：设置容器尺寸等于图片显示尺寸
    const wrapper = img.parentElement
    if (wrapper && wrapper.classList.contains('image-wrapper')) {
      wrapper.style.width = `${displayWidth}px`
      wrapper.style.height = `${displayHeight}px`
      console.log(`容器尺寸已设置为: ${displayWidth}x${displayHeight}`)
    }

    console.log(`=== 图片加载完成 ===`)
    console.log(`背景原图尺寸 (bgWidth/bgHeight): ${naturalWidth}x${naturalHeight}`)
    console.log(`视图显示尺寸 (viewWidth/viewHeight): ${displayWidth}x${displayHeight}`)
    console.log(`缩放比例 (scale = viewWidth/bgWidth): ${scaleRatio}`)
    console.log(`图片元素实际尺寸: ${img.offsetWidth}x${img.offsetHeight}`)
    console.log(`==================`)

    // 通知父组件图片已加载，传递图片尺寸信息
    emit('imageLoaded', {
      displayWidth,
      displayHeight,
      naturalWidth,
      naturalHeight
    })

    // 图片加载完成后，重新同步坐标数据
    nextTick(() => {
      syncCoordinateData()
    })
  }
}

// 重置元素位置到初始状态（强制重置到默认位置）
const resetElementPositions = () => {
  // 总是重置到默认初始位置
  const initialPositions = getInitialPositions()
  draggableItems.value = initialPositions.map(item => ({ ...item }))

  // 同时重置 coordinateDetails 数据，通知父组件更新坐标
  initialPositions.forEach(item => {
    const detailKey = idToDetailKeyMap[item.id]
    if (detailKey) {
      // 发射对应的坐标更新事件
      switch (item.id) {
        case 1: // Name
          emit('confirmName', item.x, item.y, item.width, item.height)
          break
        case 2: // NumberPrefix
          emit('confirmNumber', item.x, item.y, item.width, item.height)
          break
        case 3: // Logo
          emit('confirmLogo', item.x, item.y, item.width, item.height)
          break
        case 4: // OfficialSeal
          emit('confirmSeal', item.x, item.y, item.width, item.height)
          break
        case 5: // User
          emit('confirmUser', item.x, item.y, item.width, item.height)
          break
        case 6: // Time
          emit('confirmTime', item.x, item.y, item.width, item.height)
          break
      }
    }
  })

  console.log('元素位置和坐标数据已重置到默认初始状态')
}

// 从传入的坐标详情初始化元素位置（仅在组件初始化时使用）
const initializeFromCoordinateDetails = () => {
  if (props.coordinateDetails && Object.keys(props.coordinateDetails).length > 0) {
    // 使用传入的坐标详情
    draggableItems.value = getInitialPositions().map(item => ({ ...item }))
    // 然后同步传入的坐标数据
    nextTick(() => {
      syncCoordinateData()
    })
    console.log('元素位置已初始化为传入的坐标详情')
  } else {
    // 使用默认初始位置
    resetElementPositions()
  }
}

// 清空原有的封面文件进行重新上传封面
const clearFile = () => {
  fileList.value = []
  emit('update:modelValue', listToString(fileList.value))
  // 清空文件时重置元素位置
  resetElementPositions()
}


</script>

<template>
  <div class="component-upload-image">
    <el-upload
      ref="imageUpload" multiple :action="uploadImgUrl" list-type="picture-card"
      :on-success="handleUploadSuccess" :before-upload="handleBeforeUpload" :limit="limit" :on-error="handleUploadError"
      :on-exceed="handleExceed" :before-remove="handleDelete" :show-file-list="true" :headers="headers"
      :file-list="previewList" :on-preview="handlePictureCardPreview" :class="{ hide: fileList.length >= limit }"
      :data="uploadData"
    >
      <Icon icon="ep:plus" />

      <template #file="{ file }">
        <div class="drag-system">
          <div class="image-wrapper">
            <img
              :src="file.url"
              alt=""
              ref="container"
              class="drag-container"
              @load="handleImageLoad"
            />
            <div
              v-for="item in draggableItems"
              :key="item.id"
              :ref="setItemRef"
              class="draggable-item"
              :class="{
                'is-image': item.type === 'image',
                'is-text': item.type === 'text',
                'is-active': activeItemId === item.id
              }"
              :style="getItemStyle(item)"
              @mousedown="startDrag(item.id, $event)"
              @touchstart="startDrag(item.id, $event.touches[0])"
            >
              <div class="item-header">{{ item.label }}</div>

              <!-- 调试信息：显示原图像素坐标 -->
              <div v-if="showDebugCoords" class="debug-coords">
                {{ getDebugCoords(item) }}
              </div>

              <!-- 所有元素的拉伸手柄 -->
              <div
                class="resize-handle"
                @mousedown="startResize(item.id, $event)"
                @touchstart="startResize(item.id, $event.touches[0])"
              >
                <Icon icon="ep:bottom-right" />
              </div>
            </div>
          </div>
        </div>

      </template>
    </el-upload>
    <!-- 上传提示 -->
    <div v-if="showTip" class="el-upload__tip">
      {{ props.tipText }}
    </div>

    <!-- 控制按钮 -->
    <div class="control-buttons" v-show="fileList.length > 0">
      <el-button plain type="primary" @click="clearFile">
        {{ t('common.reUpload') }}
      </el-button>
<!--   调试用按钮   -->
<!--      <el-button plain type="info" @click="showDebugCoords = !showDebugCoords">-->
<!--        {{ showDebugCoords ? t('common.hideDebugCoords') : t('common.showDebugCoords') }}-->
<!--      </el-button>-->
    </div>

    <Dialog v-model="dialogVisible" :title="t('action.preview')" width="800px" append-to-body>
      <img :src="dialogImageUrl" style="display: block; max-width: 100%; margin: 0 auto" />
    </Dialog>
  </div>

</template>

<style scoped lang="scss">
// .el-upload--picture-card 控制加号部分
:deep(.hide .el-upload--picture-card) {
  display: none;
}

:deep(.el-upload-list__item) {
  display: block !important;
  width: auto !important;
  height: auto !important;
  max-width: 100%;
  border: none;
  border-radius: 0;
  overflow: visible;
}

// 确保图片容器不被Element Plus样式覆盖
:deep(.el-upload-list__item-thumbnail) {
  width: auto !important;
  height: auto !important;
  object-fit: contain !important;
}

:deep(.el-upload-list--picture-card .el-upload-list__item) {
  width: auto !important;
  height: auto !important;
}

.drag-system {
  position: relative;
  display: inline-block;
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  overflow: visible;
  background: #fff;
}

.image-wrapper {
  position: relative;
  display: inline-block;
  margin-bottom: 20px;
  /* 容器尺寸将通过JavaScript动态设置为图片显示尺寸 */
}

.drag-container {
  position: relative;
  display: block;
  /* 移除自动缩放，使用固定尺寸 */
  object-fit: contain;
  //border: 2px solid #ccc;
}

.draggable-item {
  position: absolute;
  border-radius: 8px;
  cursor: move;
  user-select: none;
  touch-action: none;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  transition: background-color 0.2s;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid transparent;
}

.draggable-item.is-active {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.draggable-item.is-image {
  border-style: dashed;
}

.draggable-item.is-text {
  border-style: solid;
  border-width: 1px;
}

.draggable-item .item-header {
  font-size: 12px;
  font-weight: bold;
  color: white;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
  pointer-events: none;
  // 使用与后端一致的字体族（后端默认：Palatino Linotype 加粗）
  font-family: "Palatino Linotype", "Palatino", "URW Palladio L", "Book Antiqua", serif;
}

// 文本元素预览样式（与后端保持一致）
.draggable-item.is-text {
  font-family: "Palatino Linotype", "Palatino", "URW Palladio L", "Book Antiqua", serif;
  font-weight: bold;
  text-align: left; // 后端规范：单行、左对齐
  display: flex;
  align-items: flex-start; // 基线对齐
  justify-content: flex-start;
  padding: 2px 4px;
  box-sizing: border-box;
}

// 调试坐标显示样式
.debug-coords {
  position: absolute;
  top: -20px;
  left: 0;
  font-size: 10px;
  color: #ff4444;
  background: rgba(255, 255, 255, 0.9);
  padding: 2px 4px;
  border-radius: 2px;
  white-space: nowrap;
  pointer-events: none;
  z-index: 1000;
}

// 控制按钮样式
.control-buttons {
  margin-top: 10px;
  display: flex;
  gap: 10px;
}

.resize-handle {
  position: absolute;
  bottom: -5px;
  right: -5px;
  width: 16px;
  height: 16px;
  background: #409eff;
  border: 2px solid white;
  border-radius: 50%;
  cursor: se-resize;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 8px;
  color: white;
  opacity: 0;
  transition: opacity 0.2s;
}

.draggable-item:hover .resize-handle,
.draggable-item.is-active .resize-handle {
  opacity: 1;
}
</style>
