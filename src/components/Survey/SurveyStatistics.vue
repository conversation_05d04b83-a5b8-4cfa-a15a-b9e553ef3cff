<template>
  <div ref="surveyStatisticsRef" class="survey-statistics" v-loading="loading" :element-loading-text="t('survey.loadingStatistics')">
    <!-- 统计标题 -->
    <div class="statistics-header">
      <h2>{{ statistics?.instanceName || t('survey.surveyStatistics') }}</h2>
      <div class="header-actions">
        <el-button @click="refreshStatistics" :loading="refreshing" size="small">
          <Icon icon="ep:refresh" class="mr-1" />
          {{ t('survey.refreshData') }}
        </el-button>
<!--        <el-button @click="exportStatistics" size="small">-->
<!--          <Icon icon="ep:download" class="mr-1" />-->
<!--          {{ t('survey.exportReport') }}-->
<!--        </el-button>-->
      </div>
    </div>

    <!-- 标签页内容 -->
    <div class="tabs-container">
      <el-tabs v-model="activeTab" class="statistics-tabs">
        <!-- 问题统计标签页 -->
        <el-tab-pane name="questions">
          <template #label>
            <div class="custom-tab-label">
              <Icon icon="ep:data-analysis" class="tab-icon" />
              <span>{{ t('survey.questionStatistics') }}</span>
            </div>
          </template>
          <div class="tab-content">
            <!-- 问题详细统计 -->
            <div v-if="statistics?.questionStatistics?.length" class="questions-section">
              <div
                ref="sectionHeaderRef"
                class="section-header"
                :class="{ 'sticky-header': questionsVisible }"
              >
                <div class="header-left">
                  <h3>📋 {{ t('survey.questionDetailedStatistics') }}</h3>
                </div>
                <div class="section-controls" v-show="questionsVisible">
                  <div class="search-container">
                    <el-input
                      v-model="questionSearchText"
                      :placeholder="t('survey.searchQuestions')"
                      size="small"
                      style="width: 200px"
                      clearable
                      @keyup.enter="handleSearch"
                      @clear="handleClearSearch"
                    >
                      <template #prefix>
                        <Icon icon="ep:search" />
                      </template>
                    </el-input>
                    <el-button
                      size="small"
                      @click="handleClearSearch"
                    >
                      {{ t('survey.clear') }}
                    </el-button>
                  </div>
                </div>
              </div>

              <div class="questions-list" v-show="questionsVisible">
                <QuestionStatistics
                  v-for="(question, index) in filteredQuestions"
                  :key="question.questionId"
                  :question="question"
                  :question-index="index"
                />
              </div>

              <!-- 空状态 -->
              <div v-if="!filteredQuestions.length" class="empty-questions">
                <el-empty :description="t('survey.noMatchingQuestions')" />
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 回答数据标签页 -->
        <el-tab-pane name="responses">
          <template #label>
            <div class="custom-tab-label">
              <Icon icon="ep:document-copy" class="tab-icon" />
              <span>{{ t('survey.responseData') }}</span>
            </div>
          </template>
          <div class="tab-content">
            <ResponseDataList :instance-id="instanceId" />
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 空状态 -->
    <div v-if="!loading && !statistics" class="empty-statistics">
      <el-empty
        :description="t('survey.noStatisticsData')"
        :image-size="120"
      >
        <template #description>
          <p>{{ t('survey.possibleReasons') }}</p>
          <ul>
            <li>{{ t('survey.noParticipants') }}</li>
            <li>{{ t('survey.dataGenerating') }}</li>
          </ul>
        </template>
        <el-button type="primary" @click="refreshStatistics">
          {{ t('survey.reload') }}
        </el-button>
      </el-empty>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from 'vue'
import { Icon } from '@/components/Icon'
import * as echarts from 'echarts'
import StatCard from './StatCard.vue'
import QuestionStatistics from './QuestionStatistics.vue'
import ResponseDataList from './ResponseDataList.vue'
import { ElMessage } from 'element-plus'
import { StatisticsApi } from '@/api/system/survey/statistics'
import { useI18n } from '@/hooks/web/useI18n'
import type {
  SurveyStatistics,
  SurveyTimeDistribution,
  SurveyDeptDistribution
} from '@/api/system/survey/types'

interface Props {
  instanceId: number
}

const props = defineProps<Props>()

// 国际化
const { t } = useI18n()

// 响应式数据
const loading = ref(false)
const refreshing = ref(false)
const statistics = ref<SurveyStatistics | null>(null)
const timeType = ref('day')
const questionSearchText = ref('')
const questionsVisible = ref(true) // 问题列表可见性状态
const activeTab = ref('questions') // 当前活跃的标签页

// 组件引用
const surveyStatisticsRef = ref()
const sectionHeaderRef = ref()

// 图表引用
const timeChartRef = ref()

// 计算属性
const filteredQuestions = computed(() => {
  if (!statistics.value?.questionStatistics || !questionSearchText.value) {
    return statistics.value?.questionStatistics || []
  }

  return statistics.value.questionStatistics.filter(question =>
    question.questionTitle.toLowerCase().includes(questionSearchText.value.toLowerCase())
  )
})

// 方法
const formatTime = (seconds: number) => {
  if (!seconds) return '0'
  const minutes = Math.round(seconds / 60)
  return minutes.toString()
}

const formatDateTime = (dateTime: string) => {
  if (!dateTime) return t('survey.noData')
  return new Date(dateTime).toLocaleString('zh-CN', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const loadStatistics = async () => {
  if (!props.instanceId) return

  loading.value = true
  try {
    // 调用真实的统计API，已包含时间分布和部门分布数据
    const data = await StatisticsApi.get({
      instanceId: props.instanceId,
      onlyLatest: true
    })

    // 统计数据加载成功，包含所有需要的数据
    statistics.value = data

    // 初始化图表（数据已在主接口中返回）
    nextTick(() => {
      initTimeChart()
    })

  } catch (error) {
    console.error('Failed to load statistics:', error)
    ElMessage.error(t('survey.loadStatisticsFailed'))
  } finally {
    loading.value = false
  }
}

// 处理时间类型变化
const handleTimeTypeChange = async () => {
  if (!props.instanceId || !statistics.value) return

  try {
    // 重新获取统计数据，包含新的时间类型参数
    const data = await StatisticsApi.get({
      instanceId: props.instanceId,
      onlyLatest: true,
      timeType: timeType.value
    })

    // 只更新时间分布数据，保持其他数据不变
    if (statistics.value) {
      statistics.value.timeDistribution = data.timeDistribution
    }

    // 重新初始化时间图表
    nextTick(() => {
      initTimeChart()
    })
  } catch (error) {
    console.error('Failed to load time distribution:', error)
    ElMessage.error(t('survey.loadStatisticsFailed'))
  }
}

const refreshStatistics = async () => {
  if (!props.instanceId) return

  refreshing.value = true
  try {
    // 先调用刷新缓存API
    await StatisticsApi.refresh(props.instanceId)
    // 然后重新加载数据
    await loadStatistics()
    ElMessage.success(t('survey.dataRefreshSuccess'))
  } catch (error) {
    console.error('Failed to refresh statistics:', error)
    ElMessage.error(t('survey.dataRefreshFailed'))
  } finally {
    refreshing.value = false
  }
}

const exportStatistics = () => {
  ElMessage.info(t('survey.exportFeatureInDevelopment'))
}

// 搜索功能
const handleSearch = () => {
  if (!questionSearchText.value.trim()) {
    ElMessage.warning(t('survey.pleaseEnterSearchKeyword'))
    return
  }
  // 搜索逻辑已在 filteredQuestions 计算属性中实现
  ElMessage.success(t('survey.foundMatchingQuestions', { count: filteredQuestions.value.length }))
}

const handleClearSearch = () => {
  questionSearchText.value = ''
  ElMessage.info(t('survey.searchConditionsCleared'))
}

// 切换问题列表可见性
const toggleQuestionsVisibility = () => {
  questionsVisible.value = !questionsVisible.value
}



// 图表初始化方法
const initTimeChart = () => {
  if (!timeChartRef.value || !statistics.value?.timeDistribution) return

  const chart = echarts.init(timeChartRef.value)
  const data = statistics.value.timeDistribution

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      }
    },
    legend: {
      data: [t('survey.dailySubmission'), t('survey.cumulativeSubmission')],
      top: 20
    },
    grid: {
      top: 60,
      bottom: 60,
      left: 60,
      right: 60
    },
    xAxis: {
      type: 'category',
      data: data.map(d => d.timePeriod),
      axisPointer: {
        type: 'shadow'
      }
    },
    yAxis: [
      {
        type: 'value',
        name: t('survey.dailySubmission'),
        position: 'left'
      },
      {
        type: 'value',
        name: t('survey.cumulativeSubmission'),
        position: 'right'
      }
    ],
    series: [
      {
        name: t('survey.dailySubmission'),
        type: 'bar',
        data: data.map(d => d.count),
        itemStyle: {
          color: '#1890ff'
        }
      },
      {
        name: t('survey.cumulativeSubmission'),
        type: 'line',
        yAxisIndex: 1,
        data: data.map(d => d.cumulativeCount),
        itemStyle: {
          color: '#52c41a'
        },
        lineStyle: {
          width: 3
        }
      }
    ]
  }

  chart.setOption(option)
}



// 生命周期
onMounted(() => {
  loadStatistics()
})

// 暴露方法供外部调用
defineExpose({
  refreshStatistics,
  loadStatistics
})
</script>

<style lang="scss" scoped>
.survey-statistics {
  padding: 24px;
  background: #f5f7fa;
  min-height: 100vh;

  .statistics-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    background: white;
    padding: 20px 24px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    h2 {
      font-size: 24px;
      font-weight: 600;
      color: #1f2937;
      margin: 0;
    }

    .header-actions {
      display: flex;
      gap: 12px;
    }
  }

  .stats-overview {
    margin-bottom: 32px;

    .stats-cards {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 24px;
    }
  }

  .charts-section {
    margin-bottom: 32px;

    .chart-card {
      background: white;
      border-radius: 12px;
      padding: 24px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      .chart-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;

        h3 {
          font-size: 18px;
          font-weight: 600;
          color: #1f2937;
          margin: 0;
        }

        .chart-controls {
          display: flex;
          gap: 12px;
        }
      }

      .chart-container {
        width: 100%;
        height: 300px;
      }
    }
  }

  // 标签页样式
  .tabs-container {
    margin-top: 24px;

    .statistics-tabs {
      :deep(.el-tabs__header) {
        margin-bottom: 0;
        background: #f8f9fa;
        border-bottom: 1px solid #e9ecef;
        border-radius: 8px 8px 0 0;
        padding: 0 20px;
      }

      :deep(.el-tabs__nav-wrap) {
        &::after {
          display: none;
        }
      }

      :deep(.el-tabs__nav) {
        border: none;
        padding: 8px 0;
      }

      :deep(.el-tabs__item) {
        border: none;
        background: transparent;
        color: #6c757d;
        padding: 12px 16px;
        margin-right: 8px;
        font-weight: 500;
        border-radius: 6px;
        transition: all 0.2s ease;

        &:hover {
          background: #e9ecef;
          color: #495057;
        }

        &.is-active {
          background: #409eff;
          color: white;
          font-weight: 600;
        }
      }

      :deep(.el-tabs__content) {
        padding: 0;
        background: white;
        border-radius: 0 0 8px 8px;
        border: 1px solid #e9ecef;
        border-top: none;
      }

      .tab-content {
        min-height: 400px;
        padding: 20px;
      }

      // 简洁的标签样式
      .custom-tab-label {
        display: flex;
        align-items: center;
        gap: 6px;
        font-size: 14px;

        .tab-icon {
          font-size: 14px;
        }
      }
    }
  }

  // 简化的过渡动画
  .fade-slide-enter-active,
  .fade-slide-leave-active {
    transition: opacity 0.2s ease;
  }

  .fade-slide-enter-from,
  .fade-slide-leave-to {
    opacity: 0;
  }

  .questions-section {
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;
      background: white;
      padding: 20px 24px;
      border-radius: 12px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;

      &.sticky-header {
        position: sticky;
        top: 0;
        z-index: 100;
        margin-bottom: 24px;
        border-radius: 8px 8px 0 0;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
        backdrop-filter: blur(8px);
        background: rgba(255, 255, 255, 0.95);
      }

      .header-left {
        display: flex;
        align-items: center;
        gap: 16px;

        h3 {
          font-size: 20px;
          font-weight: 600;
          color: #1f2937;
          margin: 0;
        }

        .collapse-btn {
          display: flex;
          align-items: center;
          gap: 4px;
          color: #6b7280;
          font-size: 14px;
          padding: 4px 8px;

          &:hover {
            color: #374151;
            background-color: #f3f4f6;
          }
        }
      }

      .section-controls {
        display: flex;
        gap: 12px;
        align-items: center;

        .search-container {
          display: flex;
          gap: 8px;
          align-items: center;
        }
      }
    }

    .questions-list {
      display: flex;
      flex-direction: column;
      gap: 24px;
    }

    .empty-questions {
      background: white;
      border-radius: 12px;
      padding: 40px;
      text-align: center;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }

  .empty-statistics {
    background: white;
    border-radius: 12px;
    padding: 60px;
    text-align: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    ul {
      text-align: left;
      display: inline-block;
      margin: 16px 0;
      color: #6b7280;
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .survey-statistics {
    .stats-overview .stats-cards {
      grid-template-columns: repeat(2, 1fr);
    }


  }
}

@media (max-width: 768px) {
  .survey-statistics {
    padding: 16px;

    .statistics-header {
      flex-direction: column;
      gap: 16px;
      align-items: flex-start;

      .header-actions {
        width: 100%;
        justify-content: flex-end;
      }
    }

    .stats-overview .stats-cards {
      grid-template-columns: 1fr;
    }

    .questions-section .section-header {
      flex-direction: column;
      gap: 16px;
      align-items: flex-start;

      .header-left {
        width: 100%;
        justify-content: space-between;
      }

      .section-controls {
        width: 100%;
      }
    }
  }
}
</style>
