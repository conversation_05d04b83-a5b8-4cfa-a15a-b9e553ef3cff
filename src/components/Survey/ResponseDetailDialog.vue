<template>
  <Dialog :title="t('survey.responseDetailTitle')" v-model="dialogVisible" width="900px">
    <div v-loading="loading" class="response-detail">
      <div v-if="detail" class="detail-content">
        <!-- 基本信息 -->
        <div class="response-header">
          <h3>{{ t('survey.basicInfo') }}</h3>
          <el-descriptions :column="2" border>
            <el-descriptions-item :label="t('survey.userName')">
              {{ detail.responseInfo.userName }}
            </el-descriptions-item>
            <el-descriptions-item :label="t('survey.deptName')">
              {{ detail.responseInfo.deptName || '-' }}
            </el-descriptions-item>
            <el-descriptions-item :label="t('examMgt.exam.submitTime')">
              {{ formatDateTime(detail.responseInfo.submitTime) }}
            </el-descriptions-item>
            <el-descriptions-item :label="t('survey.totalScore')">
              {{ detail.responseInfo.score || '-' }}
            </el-descriptions-item>
            <el-descriptions-item :label="t('survey.isValid')">
              <el-tag :type="detail.responseInfo.isValid ? 'success' : 'warning'">
                {{ detail.responseInfo.isValid ? t('survey.validResponse') : t('survey.invalidResponse') }}
              </el-tag>
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 统计信息 -->
        <div class="response-statistics">
          <h3>{{ t('survey.responseStatistics') }}</h3>
          <el-row :gutter="16">
            <el-col :span="6">
              <el-statistic :title="t('survey.totalQuestions')" :value="detail.statistics.totalQuestions" />
            </el-col>
            <el-col :span="6">
              <el-statistic :title="t('survey.answeredQuestions')" :value="detail.statistics.answeredQuestions" />
            </el-col>
            <el-col :span="6">
              <el-statistic :title="t('survey.skippedQuestions')" :value="detail.statistics.skippedQuestions" />
            </el-col>
            <el-col :span="6">
              <el-statistic
                :title="t('survey.completionRate')"
                :value="(detail.statistics.completionRate * 100).toFixed(1)"
                suffix="%"
              />
            </el-col>
          </el-row>
        </div>

        <!-- 答题详情 -->
        <div class="questions-preview">
          <div class="questions-header">
            <h3>{{ t('survey.answerDetails') }} ({{ detail.questionAnswers?.length || 0 }})</h3>
          </div>

          <div v-if="detail.questionAnswers && detail.questionAnswers.length > 0" class="questions-list">
            <div
              v-for="(qa, index) in detail.questionAnswers"
              :key="qa.questionId"
              class="question-preview"
            >
              <div class="question-header">
                <span class="question-number">{{ index + 1 }}.</span>
                <span class="question-title">{{ qa.questionTitle }}</span>
                <div class="question-tags">
                  <el-tag size="small" :type="SurveyUtils.getQuestionTypeColor(qa.questionType)">
                    {{ SurveyUtils.getQuestionTypeName(qa.questionType) }}
                  </el-tag>
                  <el-tag v-if="qa.required" size="small" type="danger">{{ t('survey.required') }}</el-tag>
                  <el-tag v-if="qa.answered" size="small" type="success">{{ t('survey.answered') }}</el-tag>
                  <el-tag v-else size="small" type="info">{{ t('survey.notAnswered') }}</el-tag>
                </div>
              </div>

              <div v-if="qa.questionDescription" class="question-description">
                {{ qa.questionDescription }}
              </div>

              <!-- Question Content with Answer -->
              <div class="question-content">
                <!-- Single Choice -->
                <div v-if="qa.questionType === 1" class="question-preview-content">
                  <el-radio-group :model-value="qa.answerValue" disabled>
                    <el-radio
                      v-for="(option, optionIndex) in getQuestionOptions(qa)"
                      :key="optionIndex"
                      :value="option.value"
                      class="option-item"
                      :class="{ 'selected-option': qa.answerValue === option.value }"
                    >
                      {{ option.text }}
                      <span v-if="option.score" class="option-score">({{ option.score }}{{ t('survey.points') }})</span>
                    </el-radio>
                  </el-radio-group>
                  <div v-if="qa.answered && qa.score" class="answer-score">
                    {{ t('survey.score') }}: {{ qa.score }}
                  </div>
                </div>

                <!-- Multiple Choice -->
                <div v-else-if="qa.questionType === 2" class="question-preview-content">
                  <el-checkbox-group :model-value="getAnswerValues(qa.answerValue)" disabled>
                    <el-checkbox
                      v-for="(option, optionIndex) in getQuestionOptions(qa)"
                      :key="optionIndex"
                      :value="option.value"
                      class="option-item"
                      :class="{ 'selected-option': isOptionSelected(qa.answerValue, option.value) }"
                    >
                      {{ option.text }}
                      <span v-if="option.score" class="option-score">({{ option.score }}{{ t('survey.points') }})</span>
                    </el-checkbox>
                  </el-checkbox-group>
                  <div v-if="qa.answered && qa.score" class="answer-score">
                    {{ t('survey.score') }}: {{ qa.score }}
                  </div>
                </div>

                <!-- True/False -->
                <div v-else-if="qa.questionType === 3" class="question-preview-content">
                  <el-radio-group :model-value="qa.answerValue" disabled>
                    <el-radio
                      value="true"
                      class="option-item"
                      :class="{ 'selected-option': qa.answerValue === 'true' }"
                    >
                      {{ t('survey.yes') }}
                    </el-radio>
                    <el-radio
                      value="false"
                      class="option-item"
                      :class="{ 'selected-option': qa.answerValue === 'false' }"
                    >
                      {{ t('survey.no') }}
                    </el-radio>
                  </el-radio-group>
                  <div v-if="qa.answered && qa.score" class="answer-score">
                    {{ t('survey.score') }}: {{ qa.score }}
                  </div>
                </div>

                <!-- Rating -->
                <div v-else-if="qa.questionType === 4" class="question-preview-content">
                  <el-rate
                    :model-value="parseFloat(qa.answerValue) || 0"
                    disabled
                    show-score
                    :score-template="`${qa.answerValue || 0} / 5`"
                  />
                  <div v-if="qa.answered && qa.score" class="answer-score">
                    {{ t('survey.score') }}: {{ qa.score }}
                  </div>
                </div>

                <!-- File Upload -->
                <div v-else-if="qa.questionType === 5" class="question-preview-content">
                  <div v-if="qa.answered && qa.answerValue" class="file-answer">
                    <div v-for="(file, fileIndex) in getUploadedFiles(qa.answerValue)" :key="fileIndex" class="file-item">
                      <el-link :href="file.url" target="_blank" type="primary" class="file-link">
                        <Icon icon="ep:document" class="mr-2" />
                        {{ file.name }}
                      </el-link>
                    </div>
                  </div>
                  <div v-else class="no-file">
                    <el-tag type="info" size="small">{{ t('survey.noFileUploaded') }}</el-tag>
                  </div>
                </div>

                <!-- Text Input -->
                <div v-else-if="qa.questionType === 6 || qa.questionType === 7" class="question-preview-content">
                  <div v-if="qa.answered" class="text-answer">
                    <div class="answer-content-text">{{ qa.answerText }}</div>
                  </div>
                  <div v-else class="no-text">
                    <el-tag type="info" size="small">{{ t('survey.notAnswered') }}</el-tag>
                  </div>
                  <div v-if="qa.answered && qa.score" class="answer-score">
                    {{ t('survey.score') }}: {{ qa.score }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <el-button @click="handleClose">{{ t('survey.close') }}</el-button>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { Icon } from '@/components/Icon'
import { Dialog } from '@/components/Dialog'
import { ElMessage } from 'element-plus'
import { useI18n } from '@/hooks/web/useI18n'
import { SurveyUtils } from '@/api/system/survey'
import { formatDate } from '@/utils/formatTime'
import { StatisticsApi } from '@/api/system/survey/statistics'

interface Props {
  visible: boolean
  responseId: number | null
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'update:visible': [value: boolean]
  close: []
}>()

// 国际化
const { t } = useI18n()

// 工具函数
const formatDateTime = (dateTime: string | Date) => {
  if (!dateTime) return '-'
  return formatDate(new Date(dateTime))
}

// 响应式数据
const dialogVisible = ref(false)
const loading = ref(false)
const detail = ref<any>(null)

// 监听visible变化
watch(
  () => props.visible,
  (newVal) => {
    dialogVisible.value = newVal
    if (newVal && props.responseId) {
      fetchDetail()
    }
  },
  { immediate: true }
)

// 监听dialogVisible变化
watch(dialogVisible, (newVal) => {
  if (!newVal) {
    emit('update:visible', false)
    emit('close')
  }
})

// 获取详情
const fetchDetail = async () => {
  if (!props.responseId) return

  loading.value = true
  try {
    // 调用实际的API
    const data = await StatisticsApi.getResponseDetail(props.responseId)
    detail.value = data

    console.log('获取回答详情成功', data)
  } catch (error) {
    console.error('获取回答详情失败:', error)
    ElMessage.error(t('survey.loadDetailFailed'))
    detail.value = null
  } finally {
    loading.value = false
  }
}

// 关闭弹窗
const handleClose = () => {
  dialogVisible.value = false
}

// 获取问题选项
const getQuestionOptions = (question: any) => {
  if (!question.config || !question.config.options) return []
  return question.config.options
}



// 获取多选答案值数组
const getAnswerValues = (answerValue: string) => {
  if (!answerValue) return []
  try {
    return JSON.parse(answerValue)
  } catch {
    return answerValue.split(',')
  }
}

// 检查选项是否被选中
const isOptionSelected = (answerValue: string, optionValue: string) => {
  const values = getAnswerValues(answerValue)
  return values.includes(optionValue)
}

// 获取上传的文件列表
const getUploadedFiles = (answerValue: string) => {
  if (!answerValue) return []
  try {
    const files = JSON.parse(answerValue)
    return Array.isArray(files) ? files : []
  } catch {
    return []
  }
}
</script>

<style lang="scss" scoped>
.response-detail {
  .detail-content {
    .response-header,
    .response-statistics,
    .questions-preview {
      margin-bottom: 24px;

      h3 {
        margin-bottom: 16px;
        font-size: 16px;
        font-weight: 600;
        color: #303133;
      }
    }

    .questions-preview {
      .questions-header {
        margin-bottom: 16px;

        h3 {
          margin: 0;
          font-size: 16px;
          font-weight: 600;
          color: #303133;
        }
      }

      .questions-list {
        .question-preview {
          border: 1px solid #e4e7ed;
          border-radius: 8px;
          padding: 20px;
          margin-bottom: 16px;
          background: #fafafa;

          &:last-child {
            margin-bottom: 0;
          }

          .question-header {
            display: flex;
            align-items: flex-start;
            margin-bottom: 12px;

            .question-number {
              font-weight: 600;
              color: #409eff;
              margin-right: 8px;
              flex-shrink: 0;
            }

            .question-title {
              font-weight: 500;
              color: #303133;
              flex: 1;
              margin-right: 12px;
            }

            .question-tags {
              display: flex;
              gap: 8px;
              flex-shrink: 0;
            }
          }

          .question-description {
            color: #606266;
            font-size: 14px;
            margin-bottom: 16px;
            padding-left: 24px;
          }

          .question-content {
            padding-left: 24px;

            .question-preview-content {
              .option-item {
                display: block;
                margin-bottom: 8px;
                padding: 8px 12px;
                border-radius: 4px;
                transition: all 0.3s;

                &.selected-option {
                  background-color: #e6f7ff;
                  border-color: #1890ff;
                  color: #1890ff;
                  font-weight: 500;
                }

                .option-score {
                  color: #52c41a;
                  font-weight: 500;
                  margin-left: 8px;
                }
              }

              .answer-score {
                margin-top: 12px;
                padding: 8px 12px;
                background: #f6ffed;
                border: 1px solid #b7eb8f;
                border-radius: 4px;
                color: #52c41a;
                font-weight: 500;
              }

              .text-answer {
                .answer-content-text {
                  padding: 12px;
                  background: white;
                  border: 1px solid #d9d9d9;
                  border-radius: 4px;
                  min-height: 40px;
                  color: #303133;
                  line-height: 1.5;
                }
              }

              .file-answer {
                padding: 12px;
                background: white;
                border: 1px solid #d9d9d9;
                border-radius: 4px;

                .file-item {
                  margin-bottom: 8px;

                  &:last-child {
                    margin-bottom: 0;
                  }

                  .file-link {
                    display: inline-flex;
                    align-items: center;
                    padding: 8px 12px;
                    background: #f5f7fa;
                    border: 1px solid #e4e7ed;
                    border-radius: 4px;
                    text-decoration: none;
                    transition: all 0.3s;

                    &:hover {
                      background: #ecf5ff;
                      border-color: #409eff;
                    }
                  }
                }
              }

              .no-file,
              .no-text {
                padding: 12px;
                text-align: center;
                color: #999;
              }
            }
          }
        }
      }
    }
  }
}
</style>
