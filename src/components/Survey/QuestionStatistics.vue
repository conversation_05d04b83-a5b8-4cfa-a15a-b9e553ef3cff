<template>
  <div class="question-statistics">
    <div class="question-header">
      <div class="question-title">
        <span class="question-number">{{ questionIndex + 1 }}</span>
        <h3>{{ question.questionTitle }}</h3>
      </div>
      <div class="question-meta">
        <el-tag :type="getQuestionTypeTagType(question.questionType)" size="small">
          {{ SurveyUtils.getQuestionTypeName(question.questionType) }}
        </el-tag>
        <el-tag v-if="question.required" type="danger" size="small">{{ t('survey.required') }}</el-tag>
        <span class="response-rate">{{ t('survey.responseRate') }}: {{ (question.responseRate * 100).toFixed(1) }}%</span>
      </div>
    </div>

    <div class="question-content">
      <!-- 单选题/多选题 -->
      <div v-if="isChoiceQuestion" class="choice-question">
        <div class="chart-container">
          <div ref="pieChartRef" class="pie-chart"></div>
        </div>
        <div class="options-list">
          <div
            v-for="option in displayedOptions"
            :key="option.optionValue || option.optionId"
            class="option-item"
          >
            <div class="option-text">{{ option.optionText }}</div>
            <div class="option-stats">
              <span class="option-count">{{ option.count || 0 }}{{ t('survey.people') }}</span>
              <span class="option-percentage">{{ ((option.percentage || 0) * 100).toFixed(1) }}%</span>
              <div class="option-progress">
                <el-progress
                  :percentage="Math.min((option.percentage || 0) * 100, 100)"
                  :show-text="false"
                  :stroke-width="8"
                  color="#1890ff"
                />
              </div>
            </div>
          </div>

          <!-- 如果没有选项统计数据，显示提示 -->
          <div v-if="!displayedOptions.length" class="no-options">
            <el-empty
              :description="t('survey.noOptionData')"
              :image-size="80"
            />
          </div>
        </div>
      </div>

      <!-- 判断题 -->
      <div v-else-if="question.questionType === QuestionTypeEnum.TRUE_FALSE" class="true-false-question">
        <div class="chart-container">
          <div ref="donutChartRef" class="donut-chart"></div>
        </div>
        <div class="options-list">
          <div
            v-for="option in displayedOptions"
            :key="option.optionValue || option.optionId"
            class="option-item"
          >
            <div class="option-info">
              <div class="option-indicator" :style="{ backgroundColor: getTFColor(option.optionValue) }"></div>
              <span class="option-text">{{ getTrueFalseText(option.optionValue) }}</span>
            </div>
            <div class="option-stats">
              <span class="option-count">{{ option.count || 0 }}{{ t('survey.people') }}</span>
              <span class="option-percent">{{ ((option.percentage || 0) * 100).toFixed(1) }}%</span>
            </div>
          </div>

          <!-- 如果没有选项数据，显示默认的真/假选项 -->
          <div v-if="!displayedOptions.length" class="default-options">
            <div class="option-item">
              <div class="option-info">
                <div class="option-indicator" :style="{ backgroundColor: getTFColor('true') }"></div>
                <span class="option-text">{{ t('survey.yes') }}</span>
              </div>
              <div class="option-stats">
                <span class="option-count">0{{ t('survey.people') }}</span>
                <span class="option-percent">0.0%</span>
              </div>
            </div>
            <div class="option-item">
              <div class="option-info">
                <div class="option-indicator" :style="{ backgroundColor: getTFColor('false') }"></div>
                <span class="option-text">{{ t('survey.no') }}</span>
              </div>
              <div class="option-stats">
                <span class="option-count">0{{ t('survey.people') }}</span>
                <span class="option-percent">0.0%</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 评分题 -->
      <div v-else-if="question.questionType === QuestionTypeEnum.RATING" class="rating-question">
<!--        <div class="rating-summary">-->
<!--          <div class="avg-rating">-->
<!--            <el-rate-->
<!--              :model-value="question.averageScore || 0"-->
<!--              disabled-->
<!--              show-score-->
<!--              text-color="#ff9900"-->
<!--            />-->
<!--            <span class="avg-score">{{ question.averageScore?.toFixed(1) }}</span>-->
<!--          </div>-->
<!--        </div>-->
        <div class="rating-distribution">
          <div
            v-for="option in displayedRatingOptions"
            :key="option.optionValue || option.score"
            class="rating-bar"
          >
            <span class="rating-label">{{ option.score }}{{ t('survey.star') }}</span>
            <div class="rating-progress">
              <el-progress
                :percentage="Math.min((option.percentage || 0) * 100, 100)"
                :show-text="false"
                :stroke-width="12"
                color="#faad14"
              />
            </div>
            <span class="rating-count">{{ option.count || 0 }}</span>
          </div>
        </div>
      </div>

      <!-- 文本题 -->
      <div v-else-if="question.questionType === QuestionTypeEnum.TEXT" class="text-question">
        <div class="text-stats">
          <el-statistic :title="t('survey.answerCount')" :value="question.textAnswers?.length || 0" />
        </div>
        <div class="text-answers">
          <h4>{{ t('survey.answerList') }}</h4>
          <div class="answers-list">
            <div
              v-for="(answer, index) in displayedAnswers"
              :key="index"
              class="answer-item"
            >
              <span class="answer-index">{{ index + 1 }}</span>
              <span class="answer-text">{{ answer }}</span>
            </div>
            <div v-if="hasMoreAnswers" class="show-more">
              <el-button type="text" @click="showAllAnswers = !showAllAnswers">
                {{ showAllAnswers ? t('survey.collapse') : t('survey.viewAllAnswers', { count: question.textAnswers?.length }) }}
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 文件上传题 -->
      <div v-else-if="question.questionType === QuestionTypeEnum.FILE_UPLOAD" class="file-question">
        <div class="file-stats-grid">
          <el-statistic :title="t('survey.uploadedPeople')" :value="question.responseCount" />
          <el-statistic :title="t('survey.uploadedFiles')" :value="question.fileCount || 0" />
        </div>
      </div>
    </div>

    <div class="question-footer">
      <div class="stat-item">
        <span class="stat-label">{{ t('survey.responseCount') }}</span>
        <span class="stat-value">{{ question.responseCount }}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">{{ t('survey.skipCount') }}</span>
        <span class="stat-value">{{ question.skipCount }}</span>
      </div>
      <div v-if="question.averageScore !== undefined" class="stat-item">
        <span class="stat-label">{{ t('survey.averageScore') }}</span>
        <span class="stat-value">{{ question.averageScore?.toFixed(2) }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import { useI18n } from '@/hooks/web/useI18n'
import type {
  SurveyQuestionStatistics,
  SurveyOptionStatistics
} from '@/api/system/survey/types'
import { QuestionTypeEnum } from '@/api/system/survey/types'
import { SurveyUtils } from '@/api/system/survey'

interface Props {
  question: SurveyQuestionStatistics
  questionIndex: number
}

const props = defineProps<Props>()

// 国际化
const { t } = useI18n()

// 图表引用
const pieChartRef = ref()
const donutChartRef = ref()

// 文本答案显示控制
const showAllAnswers = ref(false)

// 计算属性
const isChoiceQuestion = computed(() => {
  return props.question.questionType === QuestionTypeEnum.SINGLE_CHOICE || props.question.questionType === QuestionTypeEnum.MULTIPLE_CHOICE
})

const displayedAnswers = computed(() => {
  const answers = props.question.textAnswers || []
  return showAllAnswers.value ? answers : answers.slice(0, 10)
})

const hasMoreAnswers = computed(() => {
  return (props.question.textAnswers?.length || 0) > 10
})

// 显示的选项数据（确保总是有数据显示）
const displayedOptions = computed(() => {
  // 如果有统计数据，直接使用
  if (props.question.optionStatistics && props.question.optionStatistics.length > 0) {
    // 对于判断题，确保optionValue格式正确
    if (props.question.questionType === QuestionTypeEnum.TRUE_FALSE) {
      return props.question.optionStatistics.map(option => ({
        ...option,
        optionValue: normalizeOptionValue(option.optionValue),
        optionText: option.optionText || getDefaultTrueFalseText(option.optionValue)
      }))
    }
    return props.question.optionStatistics
  }

  // 如果没有统计数据但有选项定义，创建默认的统计数据
  if (props.question.options && props.question.options.length > 0) {
    return props.question.options.map(option => ({
      optionId: option.optionId,
      optionValue: option.optionValue || option.optionId?.toString(),
      optionText: option.optionText,
      count: 0,
      percentage: 0,
      score: option.score || 0
    }))
  }

  // 对于判断题，如果有回答数据但没有选项统计，创建默认的真/假选项
  if (props.question.questionType === QuestionTypeEnum.TRUE_FALSE && props.question.responseCount > 0) {
    return [
      { optionText: t('survey.yes'), count: 0, percentage: 0, optionValue: 'true', score: 0 },
      { optionText: t('survey.no'), count: 0, percentage: 0, optionValue: 'false', score: 0 }
    ]
  }

  // 都没有则返回空数组
  return []
})

// 评分题的选项数据（确保显示1-5星的完整评分）
const displayedRatingOptions = computed(() => {
  // 如果有统计数据，直接使用
  if (props.question.optionStatistics && props.question.optionStatistics.length > 0) {
    return props.question.optionStatistics
  }

  // 如果没有统计数据，创建默认的1-5星评分
  if (props.question.questionType === QuestionTypeEnum.RATING) {
    return [
      { score: 1, count: 0, percentage: 0, optionValue: '1', optionText: `1${t('survey.star')}` },
      { score: 2, count: 0, percentage: 0, optionValue: '2', optionText: `2${t('survey.star')}` },
      { score: 3, count: 0, percentage: 0, optionValue: '3', optionText: `3${t('survey.star')}` },
      { score: 4, count: 0, percentage: 0, optionValue: '4', optionText: `4${t('survey.star')}` },
      { score: 5, count: 0, percentage: 0, optionValue: '5', optionText: `5${t('survey.star')}` }
    ]
  }

  return []
})

// 标准化选项值（处理不同的true/false表示方式）
const normalizeOptionValue = (value: string): string => {
  if (!value) return 'false'
  const lowerValue = value.toString().toLowerCase()
  if (lowerValue === 'true' || lowerValue === '1' || lowerValue === 'yes' || lowerValue === '是') {
    return 'true'
  }
  if (lowerValue === 'false' || lowerValue === '0' || lowerValue === 'no' || lowerValue === '否') {
    return 'false'
  }
  return value
}

// 获取默认的真/假文本
const getDefaultTrueFalseText = (value: string): string => {
  const normalizedValue = normalizeOptionValue(value)
  return normalizedValue === 'true' ? t('survey.yes') : t('survey.no')
}

// 根据选项值获取判断题的国际化文本
const getTrueFalseText = (value: string): string => {
  const normalizedValue = normalizeOptionValue(value)
  return normalizedValue === 'true' ? t('survey.yes') : t('survey.no')
}



// 方法
const getQuestionTypeTagType = (type: QuestionTypeEnum) => {
  const typeMap = {
    [QuestionTypeEnum.SINGLE_CHOICE]: 'primary',    // 单选
    [QuestionTypeEnum.MULTIPLE_CHOICE]: 'success',  // 多选
    [QuestionTypeEnum.TRUE_FALSE]: 'warning',       // 判断
    [QuestionTypeEnum.RATING]: 'danger',            // 评分
    [QuestionTypeEnum.FILE_UPLOAD]: 'info',         // 文件
    [QuestionTypeEnum.TEXT]: 'info'                 // 文本
  }
  return typeMap[type] || 'info'
}



const getTFColor = (value: string) => {
  return normalizeOptionValue(value) === 'true' ? '#52c41a' : '#ff4d4f'
}

const downloadFile = (file: any) => {
  // 实现文件下载逻辑
  console.log('Download file:', file)
}

// 初始化图表
const initPieChart = () => {
  if (!pieChartRef.value || !displayedOptions.value?.length) return

  const chart = echarts.init(pieChartRef.value)

  // 检查是否有实际数据
  const hasData = displayedOptions.value.some(option => (option.count || 0) > 0)

  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    series: [{
      name: t('survey.optionDistribution'),
      type: 'pie',
      radius: ['40%', '70%'],
      avoidLabelOverlap: false,
      label: {
        show: !hasData,
        position: 'center',
        formatter: hasData ? '' : t('survey.noData'),
        fontSize: 14,
        color: '#999'
      },
      emphasis: {
        label: {
          show: true,
          fontSize: '18',
          fontWeight: 'bold'
        }
      },
      labelLine: {
        show: false
      },
      data: hasData ? displayedOptions.value.map(option => ({
        name: option.optionText,
        value: option.count || 0
      })) : [{
        name: t('survey.noData'),
        value: 1,
        itemStyle: {
          color: '#f0f0f0'
        }
      }]
    }]
  }
  chart.setOption(option)
}

const initDonutChart = () => {
  if (!donutChartRef.value) return

  const chart = echarts.init(donutChartRef.value)

  // 使用displayedOptions或默认的真/假选项
  const options = displayedOptions.value.length > 0 ? displayedOptions.value : [
    { optionText: t('survey.yes'), count: 0, optionValue: 'true' },
    { optionText: t('survey.no'), count: 0, optionValue: 'false' }
  ]

  // 改进数据判断逻辑：如果有回答数据但选项统计为0，仍然显示图表结构
  const hasData = options.some(option => (option.count || 0) > 0)
  const hasResponses = props.question.responseCount > 0

  // 如果有回答但统计数据为0，可能是数据同步问题，显示结构但提示数据加载中
  const shouldShowChart = hasData || hasResponses

  console.log('判断题图表数据调试:', {
    hasData,
    hasResponses,
    shouldShowChart,
    options,
    responseCount: props.question.responseCount
  })

  const option = {
    tooltip: {
      trigger: 'item',
      formatter: (params) => {
        if (!hasData && hasResponses) {
          return `${params.name}: 数据同步中...`
        }
        return `${params.name}: ${params.value} (${params.percent}%)`
      }
    },
    series: [{
      type: 'pie',
      radius: ['40%', '70%'],
      avoidLabelOverlap: false,
      label: {
        show: !shouldShowChart,
        position: 'center',
        formatter: shouldShowChart ? '' : t('survey.noData'),
        fontSize: 14,
        color: '#999'
      },
      labelLine: {
        show: false
      },
      data: shouldShowChart ? options.map((option, index) => ({
        name: getTrueFalseText(option.optionValue),
        value: option.count || 0,
        itemStyle: {
          color: normalizeOptionValue(option.optionValue) === 'true' ? '#52c41a' : '#ff4d4f'
        }
      })) : [{
        name: t('survey.noData'),
        value: 1,
        itemStyle: {
          color: '#f0f0f0'
        }
      }]
    }]
  }
  chart.setOption(option)
}

onMounted(() => {
  nextTick(() => {
    // 添加调试信息，特别是判断题
    if (props.question.questionType === QuestionTypeEnum.TRUE_FALSE) {
      console.log('判断题统计数据调试:', {
        questionId: props.question.questionId,
        questionTitle: props.question.questionTitle,
        responseCount: props.question.responseCount,
        skipCount: props.question.skipCount,
        optionStatistics: props.question.optionStatistics,
        displayedOptions: displayedOptions.value
      })
    }

    if (isChoiceQuestion.value) {
      initPieChart()
    } else if (props.question.questionType === QuestionTypeEnum.TRUE_FALSE) {
      initDonutChart()
    }
  })
})
</script>

<style lang="scss" scoped>
.question-statistics {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;

  .question-header {
    margin-bottom: 20px;

    .question-title {
      display: flex;
      align-items: center;
      gap: 12px;
      margin-bottom: 12px;

      .question-number {
        background: #f3f4f6;
        color: #374151;
        font-weight: 600;
        font-size: 14px;
        padding: 6px 12px;
        border-radius: 20px;
        min-width: 32px;
        text-align: center;
        flex-shrink: 0;
      }

      h3 {
        font-size: 18px;
        font-weight: 600;
        color: #1f2937;
        margin: 0;
        flex: 1;
      }
    }

    .question-meta {
      display: flex;
      align-items: center;
      gap: 12px;
      flex-wrap: wrap;

      .response-rate {
        font-size: 14px;
        color: #059669;
        font-weight: 500;
      }
    }
  }

  .question-content {
    margin-bottom: 20px;

    // 选择题样式
    .choice-question {
      display: grid;
      grid-template-columns: 300px 1fr;
      gap: 24px;

      .chart-container {
        .pie-chart {
          width: 300px;
          height: 250px;
        }
      }

      .options-list {
        .option-item {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 12px 0;
          border-bottom: 1px solid #f0f2f5;

          &:last-child {
            border-bottom: none;
          }

          .option-text {
            font-size: 15px;
            color: #1f2937;
            flex: 1;
            margin-right: 16px;
          }

          .option-stats {
            display: flex;
            align-items: center;
            gap: 12px;
            min-width: 200px;

            .option-count {
              font-weight: 600;
              color: #1f2937;
              min-width: 40px;
            }

            .option-percentage {
              font-size: 14px;
              color: #6b7280;
              min-width: 50px;
            }

            .option-progress {
              flex: 1;
              min-width: 80px;
            }
          }
        }
      }
    }

    // 判断题样式 - 使用与选择题相同的布局
    .true-false-question {
      display: grid;
      grid-template-columns: 300px 1fr;
      gap: 24px;

      .chart-container {
        .donut-chart {
          width: 300px;
          height: 250px;
        }
      }

      .options-list {
        .option-item {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 12px 16px;
          background: #f9fafb;
          border-radius: 8px;
          border: 1px solid #e5e7eb;
          margin-bottom: 12px;

          &:last-child {
            margin-bottom: 0;
          }

          .option-info {
            display: flex;
            align-items: center;
            gap: 12px;

            .option-indicator {
              width: 12px;
              height: 12px;
              border-radius: 50%;
              flex-shrink: 0;
            }

            .option-text {
              font-weight: 500;
              color: #374151;
            }
          }

          .option-stats {
            display: flex;
            align-items: center;
            gap: 16px;

            .option-count {
              font-weight: 600;
              color: #1f2937;
              min-width: 60px;
              text-align: right;
            }

            .option-percent {
              font-weight: 600;
              color: #6b7280;
              min-width: 50px;
              text-align: right;
            }
          }
        }
      }
    }

    // 评分题样式
    .rating-question {
      .rating-summary {
        margin-bottom: 24px;
        text-align: center;

        .avg-rating {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 12px;

          .avg-score {
            font-size: 24px;
            font-weight: bold;
            color: #faad14;
          }
        }
      }

      .rating-distribution {
        .rating-bar {
          display: flex;
          align-items: center;
          gap: 12px;
          margin-bottom: 12px;

          .rating-label {
            font-size: 14px;
            color: #6b7280;
            min-width: 40px;
          }

          .rating-progress {
            flex: 1;
          }

          .rating-count {
            font-weight: 600;
            color: #1f2937;
            min-width: 40px;
          }
        }
      }
    }

    // 文本题样式
    .text-question {
      .text-stats {
        margin-bottom: 20px;
      }

      .text-answers {
        h4 {
          font-size: 16px;
          font-weight: 600;
          color: #1f2937;
          margin: 0 0 16px 0;
        }

        .answers-list {
          .answer-item {
            display: flex;
            gap: 12px;
            padding: 12px;
            background: #f8fafc;
            border-radius: 8px;
            margin-bottom: 8px;

            .answer-index {
              background: #e5e7eb;
              color: #6b7280;
              font-size: 12px;
              padding: 2px 8px;
              border-radius: 12px;
              min-width: 24px;
              text-align: center;
              flex-shrink: 0;
            }

            .answer-text {
              color: #1f2937;
              line-height: 1.5;
            }
          }

          .show-more {
            text-align: center;
            margin-top: 16px;
          }
        }
      }
    }

    // 文件题样式
    .file-question {
      .file-stats-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 24px;
        margin-bottom: 24px;
      }
    }
  }

  .question-footer {
    display: flex;
    gap: 32px;
    padding-top: 16px;
    border-top: 1px solid #f0f2f5;

    .stat-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 4px;

      .stat-label {
        font-size: 14px;
        color: #6b7280;
      }

      .stat-value {
        font-size: 18px;
        font-weight: 600;
        color: #1f2937;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .question-statistics {
    padding: 16px;

    .question-content {
      .choice-question {
        grid-template-columns: 1fr;
        gap: 16px;

        .chart-container .pie-chart {
          height: 200px;
        }
      }

      .true-false-question {
        grid-template-columns: 1fr;
        gap: 16px;

        //.chart-container .donut-chart {
        //  height: 200px;
        //  height: 150px;
        //}
      }

      .file-question .file-stats-grid {
        grid-template-columns: 1fr;
        gap: 16px;
      }
    }

    .question-footer {
      gap: 16px;
    }
  }
}
</style>
