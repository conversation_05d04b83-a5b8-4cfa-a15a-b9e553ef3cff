<script setup lang="ts">
import { formatImgUrl } from '@/utils'
import { useI18n } from "vue-i18n"
import { useDraggable } from '@vueuse/core'

const { t } = useI18n()
const message = useMessage() // 消息弹窗
const props = withDefaults(defineProps<{
  coordinateDetails: any
  certificateDetails: any
}>(), {

})

const dialogVisible = ref(false)

const container = ref(null)

// 定义五个可拖动元素
// 1. 名称 2.  编号 3. 图标 4.公章 5.用户 6.时间
const draggableItems = ref([
  { id: 1, label: '', x: 20, y: 20, elementRef: null, type: 0 },
  { id: 2, label: '', x: 20, y: 100, elementRef: null,type: 0  },
  { id: 3, label: '', x: 20, y: 180, elementRef: null,type: 0  },
  { id: 4, label: '', x: 20, y: 260, elementRef: null,type: 0  },
  { id: 5, label: 'User', x: 20, y: 340, elementRef: null,type: 0  },
  { id: 6, label: 'Time', x: 20, y: 400, elementRef: null,type: 0  }
])


const activeItemId = ref(null)
let startPos = { x: 0, y: 0 }

interface Position {
  x: number
  y: number
}

interface DraggableItem {
  id: string
  label: string
  x: number
  y: number
  elementRef: HTMLElement | null
}

interface CoordinateDetails {
  nameDetails: Position
  numberDetails: Position
  logoDetails: Position
  officialSealDetails: Position
  userDetails: Position
  timeDetails: Position
}


// 设置元素引用
const setItemRef = (el) => {
  if (!el) return
  const item = draggableItems.value.find(i => i.id === el.dataset.id)
  if (item) item.elementRef = el
}
// 获取元素样式
const getItemStyle = (item) => ({
  left: `${item.x}px`,
  top: `${item.y}px`,
  backgroundColor: activeItemId.value === item.id ? '#ff7f50' : getColorById(item.id),
  zIndex: activeItemId.value === item.id ? 100 : 10
})
// 根据ID获取不同颜色
const getColorById = (id) => {
  const colors = {
    1: '#42b983',
    2: '#3498db',
    3: '#9b59b6',
    4: '#e74c3c',
    5: '#f39c12'
  }
  return colors[id] || '#42b983'
}
const startDrag = (id, e) => {
  activeItemId.value = id
  const item = draggableItems.value.find(i => i.id === id)
  if (!item) return

  startPos = {
    x: e.clientX - item.x,
    y: e.clientY - item.y
  }

  // 添加事件监听
  window.addEventListener('mousemove', handleMove)
  window.addEventListener('touchmove', handleMove)
  window.addEventListener('mouseup', stopDrag)
  window.addEventListener('touchend', stopDrag)

  e.preventDefault()
}
// 处理移动
const handleMove = (e) => {
  if (!activeItemId.value) return

  const clientX = e.touches ? e.touches[0].clientX : e.clientX
  const clientY = e.touches ? e.touches[0].clientY : e.clientY

  const item = draggableItems.value.find(i => i.id === activeItemId.value)
  if (!item) return

  // 计算新位置并取整
  let newX = Math.floor(clientX - startPos.x)
  let newY = Math.floor(clientY - startPos.y)

  // 限制在容器内
  const containerRect = container.value.getBoundingClientRect()
  const itemWidth = 120
  const itemHeight = 80

  item.x = Math.max(0, Math.min(newX, containerRect.width - itemWidth))
  item.y = Math.max(0, Math.min(newY, containerRect.height - itemHeight))
}
// 停止拖动
const stopDrag = () => {
  activeItemId.value = null
  // 移除事件监听
  window.removeEventListener('mousemove', handleMove)
  window.removeEventListener('touchmove', handleMove)
  window.removeEventListener('mouseup', stopDrag)
  window.removeEventListener('touchend', stopDrag)
}
// 创建 ID 到 props 键名的映射
const idToDetailKeyMap: Record<string, keyof CoordinateDetails> = {
  '1': 'nameDetails',
  '2': 'numberDetails',
  '3': 'logoDetails',
  '4': 'officialSealDetails',
  '5': 'userDetails',
  '6': 'timeDetails'
}
watch(
  () => props.coordinateDetails,
  (newVal) => {
    draggableItems.value.forEach(item => {
      const detailKey = idToDetailKeyMap[item.id]
      if (detailKey) {
        item.x = newVal[detailKey].x
        item.y = newVal[detailKey].y
      }
    })
  },
  { deep: true }
)
watch(
  () => props.certificateDetails,
  (newVal) => {
    // 可以直接根据下标替换要展示的label值
    draggableItems.value[0].label = newVal.name
    draggableItems.value[1].label = newVal.numberPrefix
    draggableItems.value[2].label = newVal.logo
    draggableItems.value[2].type = 1
    // 遇到公章和logo需要单独处理 要展示的是一个图片 type: 0.不是 1.是 （默认是0）
    // 公章
    draggableItems.value[3].label = newVal.officialSeal
    draggableItems.value[3].type = 1
  },
  { deep: true }
)


defineExpose({ dialogVisible }) // 提供 open 方法，用于打开弹窗
</script>

<template>

  <Dialog v-model="dialogVisible" :title="t('action.preview')" width="700px" append-to-body>
    <div class="drag-system">
      <div ref="container" class="drag-container">
        <img :src="formatImgUrl(props.certificateDetails.tempImageUrl)" alt=""  ref="imageRef" class="h-full w-full" />
        <div
          v-for="item in draggableItems"
          :key="item.id"
          :ref="setItemRef"
          class="draggable-item"
          :style="getItemStyle(item)"
          @touchstart="startDrag(item.id, $event.touches[0])"
        >
          <!--          图标和公章需要单独展示-->
          <div class="item-header" v-if="item.type === 1">
            <el-image :src="formatImgUrl(item.label)" alt="" class="w-full h-full" fit="cover" />
          </div>

          <div class="item-header" v-else>{{ item.label }}</div>

        </div>
      </div>
    </div>
  </Dialog>

</template>

<style scoped lang="scss">
// .el-upload--picture-card 控制加号部分
:deep(.hide .el-upload--picture-card) {
  display: none;
}
:deep(.el-upload-list__item) {
  display: block;
  //width: 500px;
  height: 500px;
}

.drag-container {
  position: relative;
  //width: 500px;
  height: 500px;
  //border: 2px solid #ccc;
  margin-bottom: 20px;
  overflow: hidden;
}

.draggable-item {
  position: absolute;
  width: 120px;
  height: 80px;
  border-radius: 8px;
  user-select: none;
  touch-action: none;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  transition: background-color 0.2s;
  text-align: center;

  display: flex;
  align-items: center;
  justify-content: center;

  word-wrap: break-word;
  overflow: hidden;
}
</style>
