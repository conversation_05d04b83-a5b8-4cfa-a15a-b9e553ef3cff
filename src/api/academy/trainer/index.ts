import request from '@/config/axios'
import { PageResult } from '../../../../types/global'

export interface TrainerSaveVO {
  id?: number // 主键id
  userId: string // 用户id
  type: number // 教师类型
  email: string // 邮箱
  companyName: string // 公司名称
  deptName: string // 部门名称
  positionName: string // 岗位名称
  badgeNumber: string // 徽章编号
}

export interface TrainerReqVO {
  pageNo: number
  pageSize: number
  name: string
  type: number
  userId: string | string[]
  createTime: string
  companyId?: number
  deptId?: number
  positionId?: string
  courseId: number
}

export interface TrainerRespVO {
  id?: number // 主键id
  name: string
  userId: number
  nickname: string
  email: string
  badgeNumber: string
  deptName: string
  companyName: string
  positionName: string
  type: number
  status: number
  createTime: number
  feedbackScore: number
  feedbackNumber: number
  feedbackAvg: number
}

// 教师信息 API
export const TrainerApi = {
  // 查询教师信息分页
  getTrainerPage: async (params: TrainerReqVO) => {
    return await request.get<PageResult<TrainerRespVO[]>>({ url: `/academy/trainer/page`, params })
  },

  // 查询教师信息不分页
  getTrainerList: async () => {
    return await request.get<PageResult<TrainerRespVO[]>>({ url: `/academy/trainer/simple-list` })
  },

  // 查询教师信息详情
  getTrainer: async (id: number) => {
    return await request.get({ url: `/academy/trainer/get?id=` + id })
  },

  // 新增教师信息
  createTrainer: async (data: TrainerSaveVO) => {
    return await request.post({ url: `/academy/trainer/create`, data })
  },

  // 修改教师信息
  updateTrainer: async (data: TrainerSaveVO) => {
    return await request.put({ url: `/academy/trainer/update`, data })
  },

  // 删除教师信息
  deleteTrainer: async (id: number) => {
    return await request.delete({ url: `/academy/trainer/delete?id=` + id })
  },

  // 导出教师信息 Excel
  exportTrainer: async (params: TrainerReqVO) => {
    return await request.download({ url: `/academy/trainer/export-excel`, params })
  }
}
