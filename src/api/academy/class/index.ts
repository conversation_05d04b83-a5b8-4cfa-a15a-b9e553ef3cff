import { ResourceSaveVO } from '@/api/resource/list'
import request from '@/config/axios'

// 课堂信息 VO
export interface ClassInfoRespVO {
  id: number // 主键id
  courseId: number // 课程id
  name: string // 课堂名称
  code: string // 课堂编号
  type: number // 课程类型(1. 线下 2.直播 3.混合)
  trainerId: number // 教师id
  classRoomId: number // 教室id
  scanStartTime: Date // 开始扫描时间
  startDate: Date // 开始日期(yyyy-MM-dd)
  startTime: string // 开始时间(HH:mm:ss)
  endTime: string // 结束时间(HH:mm:ss)
  trainingDays: number // 课堂持续天数
  translator: number // 是否翻译(0.未开启 1.英文 2.阿拉伯文 3.中文)
  language: number // 语言(1.英文 2.阿文 3.中文)
  publishStatus: number // 状态(0.未发布 1.发布)
  status: number // 状态(0. 草稿 1.未开始 2.进行中 3.结束 4.延期 5.取消)
  maxNum: number // 参与最大人数
  minNum: number // 最新参与人数
  liveLink: string // 直播链接
  feedbackQrCode: string // 课堂反馈二维码
  description: string // 描述
  isApprovals: boolean
}
export interface ClassInfoReqVO {
  pageNo: number
  pageSize: number
  name: string
  status: number
  courseId: number
  type: number
  createTime: string
  languages: number
  startDate: string
}

export interface ClassInfoSaveVO {
  id?: number
  courseId: number
  name: string
  code: string
  type: number
  trainerId: number
  classRoomId: number
  startDate: string
  trainingDays: number
  language: number
  translator: number
  maxNum: number
  minNum: number
  description: string
}

export interface BookingReqVO {
  pageNo: number
  pageSize: number
  classId: number
  status: number
  nickname?: string
  companyId?: number
  deptId?: number
  userIds: number[]
}

export interface FeedBackReqVO {
  pageNo: number
  pageSize: number
  classId: number
  comments: number
  userId: string[]
}

// 新的修改课程时间接口请求参数
export interface ClassTimeMergeReqVO {
  classId: number
  startDate: string
  startTime: string
  endTime: string
  notification: number
}
export interface FeedBackRespVO {
  id: number
  classId: number
  nickname: string
  userId: number
  deptName: string
  badgeNo: string
  companyName: string
  positionName: string
  courseEvaluation: number
  trainerEvaluation: number
  facilityEvaluation: number
  comments: string
  createTime: Date
}

export interface ClassAssignReqVO {
  pageNo: number
  pageSize: number
  userIds: string[]
  status: number
  attendanceStatus: number
}

export interface ClassMaterialsSaveVO {
  id?: number
  classId: number
  resourceId: number
}
export interface ClassAssignmentReqVO {
  pageNo: number
  pageSize: number
  classIds: number
  status: number
  userIds: string[]
  attendanceStatus: number
}
export interface ClassAssignmentRespVO {
  id: number
  classId: number
  nickname: string
  deptName: string
  badgeNo: string
  companyName: string
  positionName: string
  workType: string
  workTerms: string
  className: string
  classCode: string
  userId: number
  type: number
  status: number
  statusName: string
  checkInTime: Date
  checkOutTime: Date
  attendanceStatus: number
  ddtPermitNo: string
  dateOfBirth: Date
  drivingLicenceNumber: string
  issuingDate: Date
  expiryDate: Date
  vehicle: string
  eyeTest: string
  version: number
  createTime: Date
  attendanceStatusName: string
}


export interface PostPoneCompanyRespVO {
  companyId: number
  companyName: string
  studentNumber: number
  completionRate: number
  notPassed: number
  userIds: string[]
}

// 课堂状态(0.草稿 1.未开始 2.进行中 3.结束 4.延期 5.取消)
export enum ClassStatusEnum {
  DRAFT = 0,
  NOT_STARTED = 1,
  ONGOING = 2,
  ENDED = 3,
  POSTPONED = 4,
  CANCELLED = 5
}

// 课堂发布状态(0.未发布 1.发布)
export enum ClassPublishStatusEnum {
  PUBLISHED = 1,
  UNPUBLISHED = 0
}

/**
 * 课堂类型枚举 (1. 线下 2.直播 3.混合)
 */
export enum ClassTypeEnum {
  OFFLINE_CLASS = 1,
  VIRTUAL_CLASS = 2,
  HYBRID_CLASS = 3
}

/**
 * 课堂预定状态枚举 预定状态（1.approving 2.rejected 3.approved 4.异常 5.取消）
 */
export enum ClassBookingStatusEnum {
  APPROVING = 1,
  Rejected = 2,
  APPROVED = 3,
  ABNORMAL = 4,
  CANCELLED = 5
}

/**
 * 课堂是否签到枚举 	签到状态(0.未签到 1.已签到)
 */
export enum ClassAttendanceStatusEnum {
  NOT_SIGNED_IN = 0,
  SIGNED_IN = 1
}

/**
 * 课程/教室/设施枚举 (1.Postive, 2.Netural, 3.Negative)
 */
export enum ClassEvaluationEnum {
  POSITIVE = 1,
  NEUTRAL = 2,
  NEGATIVE = 3
}




export interface MaterialReqVO extends FeedBackReqVO {
  resourceId: number
}
// 课堂信息 API
export const ClassInfoApi = {
  // 查询课堂信息分页
  getClassInfoPage: async (params: ClassInfoReqVO) => {
    return await request.get<PageResult<ClassInfoRespVO[]>>({ url: `/academy/class-info/page`, params })
  },

  // 查询课堂信息详情
  getClassInfo: async (id: number) => {
    return await request.get<IResponse<ClassInfoRespVO>>({ url: `/academy/class-info/get?id=` + id })
  },

  // 加入直播
  joinLive: async (data: { classId: number }) => {
    return await request.post({ url: `/academy/class-info/join/live`, data })
  },

  // 新增课堂信息
  createClassInfo: async (data: ClassInfoSaveVO) => {
    return await request.post({ url: `/academy/class-info/create`, data })
  },

  // 修改课堂信息
  updateClassInfo: async (data: ClassInfoSaveVO) => {
    return await request.put({ url: `/academy/class-info/update`, data })
  },

  // 删除课堂信息
  deleteClassInfo: async (id: number) => {
    return await request.delete({ url: `/academy/class-info/delete?id=` + id })
  },
  // 导出课堂信息 Excel
  exportClassInfo: async (params: ClassInfoReqVO) => {
    return await request.download({ url: `/academy/class-info/export-excel`, params })
  },

  // 获取课堂编号
  getClassCode: async (courseId: number) => {
    return await request.get({ url: `/academy/class-info/total/courseId?courseId=` + courseId })
  },
  // 课堂发布
  publishClass: async (data: { ids: number | string[], publishStatus: number, notification: number}) => {
    return await request.put({ url: `/academy/class-info/update/status`, data })
  },
  // postpone Class
  postponeClass: async (data: {classId: number, targetClassId: number, notification: number}) => {
    return await request.post({ url: `/academy/class-info/merge`, data })
  },
  // 修改课程时间
  mergeClassTime: async (data: ClassTimeMergeReqVO) => {
    return await request.post({ url: `/academy/class-info/v2/merge`, data })
  },
  // 计算可用时间范围
  calculateAvailableTimeRange: async (params: {classId: number, startDate: string, intervalHours: number}) => {
    return await request.get({ url: `/academy/class-info/calculate-available-time-range`, params })
  },
  // cancel Class
    // cancel Class - use body parameters according to spec: { classId, reason, notification }
    cancelClass: async (data: { classId: number; reason: string; notification: number }) => {
      return await request.post({ url: `/academy/class-info/cancel`, data })
    },
  // Booking Management 模块下的列表查询
  getBookingList: async (params: BookingReqVO) => {
    return await request.get({ url: `/academy/class-booking/page`, params })
  },
  // Booking Management 模块下的按id查询详情
  getBookingDetail: async (id: number) => {
    return await request.get({ url: `/academy/class-booking/get?id=` + id })
  },
// Check-in/out  模块列表查询
  getResultList: async (params: ClassAssignReqVO) => {
    return await request.get<PageResult<ClassAssignmentRespVO[]>>({ url: `/academy/class-assignment/page`, params })
  },
  // 签到
  classCheckIn: async (data: { classId: number, userId: number}) => {
    return await request.put({ url: `/academy/class-assignment/check-in`, data })
  },

  // 签到
  classAdminCheckIn: async (data: { classId: number, userId: number}) => {
    return await request.put({ url: `/academy/class-assignment/admin/check-in`, data })
  },

  // 签出
  classCheckOut: async (data: { classId: number, userId: number}) => {
    return await request.put({ url: `/academy/class-assignment/check-out`, data })
  },

  // 更新课堂信息签到时间
  updateClassCheckInTime: async (id: number) => {
    return await request.put({ url: `/academy/class-info/update/check-time/${id}` })

  },

  // studentsMangagement
  getStudentList: async (params: ClassAssignmentReqVO) => {
    return await request.get<PageResult<ClassAssignmentRespVO[]>>({ url: `/academy/class-assignment/page`, params })
  },

  // 移除学员
  removeStudent: async (ids: number | string[]) => {
    return await request.delete({ url: `/academy/class-assignment/delete?ids=` + ids })
  },

  // Feedback Management
  getFeedbackPage: async (params: FeedBackReqVO) => {
    return await request.get<PageResult<FeedBackRespVO[]>>({ url: `/academy/class-feedback/page`, params })
  },

  // 课件分页
  getMaterialPage: async (params: MaterialReqVO) => {
    return await request.get({ url: `/academy/class-materials/page`, params })
  },

  // 创建课堂附件
  createMaterial: async (data: ClassMaterialsSaveVO) => {
    return await request.post({ url: `/academy/class-materials/create`, data })
  },

  // 新增课堂资源到服务器
  addResource: async (data: ResourceSaveVO | ResourceSaveVO[]) => {
    return request.post({ url: '/learning/resource', data})
  },

  // 删除课件
  deleteMaterial:  async (ids: string[] | number) => {
    return await request.delete({ url: `/academy/class-materials/delete?ids=` + ids })
  },

  // Class Roster
  getRosterStudentList: async (params: ClassAssignmentReqVO) => {
    return await request.get<PageResult<ClassAssignmentRespVO[]>>({ url: `/academy/class-assignment/page`, params })
  },

  // 批量修改状态 多个操作公用一个接口
  rejectStudent: async (data: {ids: string[],  status: number}) => {
    return await request.put({ url: `/academy/class-assignment/update/status`, data })
  },
  // 发送提醒 该功能延缓,先不做
  testReminder: async (id: number) => {
    return await request.post({ url: `/academy/class-info/get-status` })
  },
  // 外层详情列表导出
  exportPostponeCompany: async (params: { classId: number }) => {
    return await request.download({ url: `/academy/class-assignment/company-list/export-excel`, params })
  },
  // 里层详情列表导出
  exportPostponeCompanyDetail: async (params: ClassAssignmentReqVO) => {
    return await request.download({ url: `/academy/class-assignment/export-excel`, params })
  },

  postponeCompany: async (params: { classId: number,courseId: number }) => {
    return await request.get({ url: `/academy/class-assignment/company-list`, params })
  },

  postponeCompanyDetail: async (params: ClassAssignmentReqVO) => {
    return await request.get<PageResult<ClassAssignmentRespVO[]>>({ url: `/academy/class-assignment/page`, params })
  },
  // 删除日历中的课程 该功能不做
  deleteCalendar: async (id: number) => {
    return await request.delete({ url: `/academy/class-info/get-status?id=` + id })
  },

  // 课堂分配人员
  getClassAssign: async (data: any) => {
    return await request.post({ url: `/academy/class-assignment/create`, data })
  },

  // 修改学员的 eyeTest 信息
  updateEyeTest: async (data: { ids: number[], eyeTest: string }) => {
    return await request.put({ url: `/academy/class-assignment/update/eye-test`, data })
  }
}

