import request from '@/config/axios'

export interface WaitingReqVO {
  pageNo: number
  pageSize: number
  language: number
  courseId?: number
  companyId?: number
  deptId?: number
  nickname?: string
  userIds?: string
  createTime?: string[]
}

export interface WaitingCreateVO {
  userIds: number[]
  courseId: number
  startPreferredDate: string
  endPreferredDate: string
  firstStudy: boolean
  language: number
  translator: number
  type: number
}

// waiting信息 API
export const WaitingApi = {
  // 查询信息分页
  getWaitingPage: async (params: WaitingReqVO) => {
    return await request.get({ url: `/academy/waiting-list/page`, params })
  },

  // 管理端添加waiting list
  createWaitingList: async (data: WaitingCreateVO) => {
    return await request.post({ url: `/academy/waiting-list/create`, data })
  }
}
