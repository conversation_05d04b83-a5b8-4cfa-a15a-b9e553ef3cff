import request from '@/config/axios'

export interface ExamReqVO {
  pageNo: number
  pageSize: number
  name: string
  type: number
}

export interface ExamOfStudentReqVO extends ExamReqVO{
  userId: number
  onSchedule: boolean
  status: number
}

export interface ExamOfStudentRespVO {
  name: string
  subject: string
  type: number
  onSchedule: boolean
  status: number
  star: number
  operator: string
}

export interface ExamSaveVO {
  answerTime: number
  beginTime: string
  classifyId: number
  classifyName: string
  completedNum: number
  courseId: number
  createBy: string
  createId: number
  createTime: string
  deptId: number
  endTime: string
  examMax: number
  examNum: number
  id?: number
  instruction: string
  name: string
  paperId: number
  paperScore: number
  passRate: number
  passScore: number
  quesRandom: boolean
  questionNum: number
  remark: string
  status: number
  type: number
  usage: number
}

export interface ExamAssignVO {
  id?: number
  examId: number
  relevanceId?: number
  relevanceName?: string
  scope?: number
}

export interface ExamInfoReqVO {
  pageNo: number
  pageSize: number
  id: number
  email: string
  onSchedule: boolean
  status: number
  studentName: string
  badgeNo: string
}

export interface ExamRespVO {
  id: number
  name: string
  type: number
  usage: number
  courseld: number
  instruction: string
  paperld: number
  examTime: string
  beginTime: Date
  endTime: Date
  answerTime: number
  passScore: number
  examMax: number
  cutCount: number
  quesRandom: boolean
  optionRandom: boolean
  deptld: number
  delete: boolean
  createld: number
  updateld: number
  status: number
  classifyld: number
  classifyName: string
  questionNum: number
  paperScore: number
  myExamMax: number
  examNum: number
  totaINum: number
  completedNum: number
  notCompletedNum: number
  passRate: number
}

export interface ExamStaticsAllRespVO {
  examNum: number
  expiredNum: number
  inProgressNum: number
  notStartedNum: number
}
// 考试状态枚举 // 状态（0：未开始，1：进行中，2：已过期）
export const ExamStatusEnum = {
  NOT_STARTED: 0,
  IN_PROGRESS: 1,
  EXPIRED: 2
}
// 分页查询考试
export const listExam = (params: ExamReqVO) => {
  return request.get<PageResult<ExamRespVO[]>>({ url: '/learning/exam/page', params })
}
// 分页查询学生考试
export const listExamOfStudent = (params: ExamOfStudentReqVO) => {
  return request.get<PageResult<ExamOfStudentRespVO[]>>({ url: '/learning/exam/student/page', params })
}
// 根据id查询考试
export const getExam = (id: number) => {
  return request.get<IResponse<ExamRespVO>>({ url: `/learning/exam/get?id=` + id })
}

// 新增考试
export const addExam = (data: ExamSaveVO) => {
  return request.post({ url: '/learning/exam/create', data })
}

// 修改考试
export const updateExam = (data: ExamSaveVO) => {
  return request.put({ url: '/learning/exam/update', data })
}

// 删除考试
export const delExam = (ids: number) => {
  return request.delete({ url: `/learning/exam/delete?ids=` + ids })
}

// 分配考试
export const assignExam = (query: ExamAssignVO, data: any) => {
  return request.post({ url: '/learning/exam/assign',params: query, data })
}
// 批量删除分配考试
export const deleteAssignListExam = (ids: number[]) => {
  return request.delete({ url: `/learning/exam/unassign?ids=${ids}` })
}
// 查询考试分配情况
export const assignListExam = (params: { examId: number, scope: number }) => {
  return request.get({ url: '/learning/exam/assigned-list', params })
}
// 查询考试情况
export const infoExam = (params: ExamInfoReqVO) => {
  return request.get({ url: '/learning/exam/info', params })
}
// 重新分配考试次数
export const reassignExam = (query: { assignmentId: number, examMax: number }) => {
  return request.put({ url: '/learning/exam/reassign',params: query, })
}
// 查询考试答题结果
export const getExamPaperResult = (params: { examRecordId: number }) => {
  return request.get({ url: '/learning/exam/record/getExamPaperResult', params })
}
// 查询考试统计
export const staticsExam = (params: { id: number }) => {
  return request.get({ url: '/learning/exam/statics', params })
}
// 查询考试统计
export const staticsAllExam = () => {
  return request.get<ExamStaticsAllRespVO[]>({ url: '/learning/exam/statics/all' })
}

// 查询学生考试分数详情
export const studentRecordsExam = (params: { assignmentId: number }) => {
  return request.get({ url: '/learning/exam/records', params })
}

// 导出考试详情
export const exportExam = (params: ExamInfoReqVO) => {
  return request.download({ url: '/learning/exam/info/export', params })
}

// 导出考试信息
export const exportExamInfo = (params: ExamInfoReqVO) => {
  return request.download({ url: '/learning/exam/export-excel', params })
}
