import request from '@/config/axios'

export interface TopicReqVO {
  pageNum: number
  pageSize: number
  name: string
  existDdt: number
}
export interface TopicRespVO {
  createBy: string
  createTime: Date
  updateBy?: string | null
  updateTime?: string | null
  remark?: string | null
  pageNum: number
  pageSize: number
  id: number
  parentId?: number
  name: string
  keywords?: string | null
  description: string
  departmentId: string
  sort?: number | null
  level: number
  ddt: number
}
export interface TopicSaveVO {
  name: string
  sort: number
  description: string
  parentId?: number
  id?: number
  level: number
  type: 1 | 2 | 3 // (1.ddt 2.hse 3.others)
}

/**
 * 是否ddt课程枚举
 */
export enum TopicDdtEnum {
  YES = 1,
  NO = 0
}
// 查询专题列表
export const listTopic = (params?: TopicReqVO) => {
  return request.get<PageResult<TopicRespVO[]>>({ url: '/academy/category/list', params })
}

// 新增专题
export const addTopic = (data: TopicSaveVO) => {
  return request.post({ url: '/academy/category/create', data})
}

// 修改专题课程
export const updateTopic = (data: TopicSaveVO) => {
  return request.put({ url: '/academy/category/update', data})
}

// 删除专题课程
export const delTopic = (id: number) => {
  return request.delete({ url: `/academy/category/delete?id=` + id })
}

// 获得课程目录详情信息
export const getTopic = (id: number) => {
  return request.get<IResponse<TopicRespVO>>({ url: `/academy/category/get?id=` + id })
}
