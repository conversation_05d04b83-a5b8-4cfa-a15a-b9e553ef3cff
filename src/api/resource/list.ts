import request from '@/config/axios'

export interface ResourceReqVO {
  title: string
  identity: string
  duration: number
  enable: string
  reference: boolean
  lang: string
  createBy: string
  pageNo: number
  pageSize: number
  mediaType: number
}
export interface ResourceRespVO {
  pageNo: number // 页码
  pageSize: number // 每页大小
  createId: number // 创建人 ID
  createBy: string // 创建人
  createTime: string // 创建时间，格式为字符串
  updateId: number | null // 更新人 ID，可以为 null
  updateBy: string | null // 更新人，可以为 null
  updateTime: string // 更新时间，格式为字符串
  remark: string | null // 备注，可以为 null
  id: number // 记录 ID
  identity: string // 身份标识
  subjectId: number // 主题 ID
  deptId: number // 部门 ID
  type: string // 类型
  mediaType: number // 媒体类型
  format: string // 格式
  title: string // 标题
  lang: string // 语言
  duration: number // 时长
  size: number // 大小
  referenceCount: number // 参考计数
  enable: string // 是否启用
  scope: number // 范围
  delete: boolean // 是否删除
  deptIds: number[] | null // 部门 ID 数组，可以为 null
  address: string
  fileId: number
  scormVersion: string | undefined
  scormRunPath: string | undefined
  scormParseStatus: string | undefined
  aiccParseStatus: string | undefined
}

export interface ResourceSaveVO {
  address: string
  aiccParseStatus: number
  createBy: string
  createId: number
  createTime: string
  delete: boolean
  deptIds: number[] | null
  deptId: number
  duration: number
  enable: string
  fileId: number
  identity: string
  lang: string
  format: string
  id?: number
  mediaType: number
  pageNo: number
  pageSize: number
  referenceCount: number
  remark: string
  scormParseStatus: number
  scormRunPath: string
  scormVersion: string
  subjectId: number
  size: number
  title: string
  type: string
  updateId: number
  updateTime: string
  updateBy: string
}

// 媒体类型枚举 1：视频，2：音频，3：文档，4：SCORM, 6：AICC
export const MediaTypeEnum = {
  VIDEO: 1,
  AUDIO: 2,
  FILE: 3,
  SCORM: 4,
  AICC: 6
}

/**
 * 分页获取资源列表
 * @param params 查询参数
 * @returns 资源列表
 */
export const listResource = (params: ResourceReqVO) => {
  return request.get<PageResult<ResourceRespVO[]>>({ url: '/learning/resource', params })
}
/**
 * 新增资源
 * @param data 资源数据（支持单个对象或数组）
 * @returns 添加结果
 */
export const addResource = (data: ResourceSaveVO | ResourceSaveVO[]) => {
  return request.post({ url: '/learning/resource', data})
}

/**
 * 批量新增资源
 * @param data 资源数据数组
 * @returns 添加结果
 */
// export const addResourceBatch = (data: ResourceSaveVO[]) => {
//   return request.post({ url: '/learning/resource/batch', data})
// }
/**
 * 修改资源
 * @param data 要修改的资源信息
 * @returns 修改结果
 */
export const updateResource = (data: ResourceSaveVO) => {
  return request.put({ url: '/learning/resource', data})
}
/**
 * 删除资源
 * @param ids 要删除的资源IDS字符串
 * @returns 删除结果
 */
export const delResource = (ids: string[]) => {
  return request.delete({ url: `/learning/resource/${ids}` })
}
/**
 * 根据ID查询资源详情
 * @param id 资源ID
 * @returns 资源信息
 */
export const getResource = (id: number) => {
  return request.get({ url: `/learning/resource/${id}` })
}

/**
 * 根据ID查询AICC资源详情
 * @param id 资源ID
 * @returns 资源信息
 */
export const getAiccResource = (fileId: number) => {
  return request.post({ url: '/learning/aicc-session/getIndexUrl', data: { fileId }})
}
/**
 * 查询当前文件是否存在于资源库中
 * @param fileId 根据资源ID查找当前上传的文件是否存在于资源库中
 * @returns 结果
 */
export const getCheckResource = (fileId: number) => {
  return request.get({ url: `/learning/resource/fileId/${fileId}` })
}
/**
 * 改变当前资源状态
 * @param id 资源ID
 * @param fileId 文件ID
 * @param enable 要改变的状态
 * @returns 改变结果
 */
export const changeResourceStatus = (id: number, fileId: string, enable: string) => {
  return request.put({ url: '/learning/resource', data: { id, fileId, enable }})
}
/**
 * 查询正在解析的Scorm列表
 * @returns 正在解析的scorm文件列表
 */
export const listScorm = () => {
  return request.get({ url: '/learning/resource/unparsed-scorms' })
}
/**
 * 查询正在解析的Aicc列表
 * @returns 正在解析的aicc文件列表
 */
export const listAicc = () => {
  return request.get({ url: '/learning/resource/unparsed-aicc' })
}
/**
 * 再次解析scorm文件
 * @param id 资源ID
 * @returns 解析结果
 */
export const unzipScorm = (id: number) => {
  return request.post({ url: `/learning/resource/parse-scorm?id=${id}`})
}
/**
 * 查询修改记录
 * @param resourceId 资源ID
 * @returns 修改记录日志
 */
export const listRecords = (resourceId: number) => {
  return request.get({ url: '/learning/resource/log', params: { resourceId} })
}
