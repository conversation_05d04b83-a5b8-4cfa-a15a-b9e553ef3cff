import request from '@/config/axios'
import { get } from 'http'

export enum ContentTypeEnum {
  COURSE = 1,
  TRAINING = 2,
  KNOWLEDGE_DOC = 3
}

export enum RecommendContentTypeEnum {
  ONLINE_TRAINING = 10,
  ONBOARDING = 20,
  ORIENTATION = 30,
  COMPANY_POLICY = 40,
  MLC_TRAINING = 50
}

export enum DifficultyLevelEnum {
  ALL = 0,
  BEGINNER = 1,
  INTERMEDIATE = 2,
  ADVANCED = 3
}

// 学习内容推荐 VO
export interface LearningContentRecommendVO {
  id: number // 主键ID
  bizType?: number // 业务类型(RecommendContentTypeEnum)
  bizId?: number // 业务ID
  type: number // 内容类型
  typeId: number // 内容ID
  skillId: number // 技能ID
  level: number // 难度等级(Level，0：Suitable for all（默认），1：Beginner，2：Intermediate，3：Advanced)
  title: string // 内容标题
  keywords: string // 关键词
  introduction: string // 介绍
  matchDegree: number // 匹配度
  matchReason: string // 匹配原因
  status: number // 状态（1.新增，2保持，3待删除）
}

// 学习内容匹配 VO
export interface LearningContentMatchVO {
  bizType: number // 业务类型(RecommendContentTypeEnum)
  bizId: number // 业务ID
  isNew: boolean // 是否是新增
}

// 匹配确认 VO
export interface MatchConfirmVO {
  keepFlag: boolean // 是否保留
  isNew: boolean // 是否是新增
  uuid: string // 匹配返回的uuid
  bizType: number // 业务类型
  bizId: number // 业务ID
}

// 技能标签匹配结果
export interface SkillTag {
  skill_tag: string // 技能标签名称
  compatibility_score: number // 兼容度分数
  reason: string // 匹配原因
}

// 匹配结果响应
export interface MatchResultVO {
  bizType: number
  bizId: number
  level: number
  keywords: string
  introduction: string
  contentSkills: {
    content_name: string
    skill_tags: SkillTag[]
  }
  uuid: string
  isNew: boolean
}

// 学习内容推荐 API
export const LearningContentRecommendApi = {
  // 对学习内容进行匹配
  matchLearningContent: async (params: LearningContentMatchVO): Promise<MatchResultVO> => {
    return await request.post({ url: `/edp/learning-content-recommend/match`, data: params })
  },

  // 确认匹配结果
  confirmMatchResult: async (params: MatchConfirmVO) => {
    return await request.post({ url: `/edp/learning-content-recommend/confirm`, data: params })
  },

  // 获取新的学习内容列表
  getNewLearningContentList: async (params: any) => {
    return await request.get({ url: `/edp/learning-content-recommend/new-content-list`, params })
  },

  // 查询学习内容推荐分页
  getLearningContentRecommendPage: async (params: any) => {
    return await request.get({ url: `/edp/learning-content-recommend/page`, params })
  },

  // 查询学习内容推荐详情
  getLearningContentRecommend: async (id: number) => {
    return await request.get({ url: `/edp/learning-content-recommend/get?id=` + id })
  },

  // 新增学习内容推荐
  createLearningContentRecommend: async (data: LearningContentRecommendVO) => {
    return await request.post({ url: `/edp/learning-content-recommend/create`, data })
  },

  // 修改学习内容推荐
  updateLearningContentRecommend: async (data: LearningContentRecommendVO) => {
    return await request.put({ url: `/edp/learning-content-recommend/update`, data })
  },

  // 删除学习内容推荐
  deleteLearningContentRecommend: async (id: number) => {
    return await request.delete({ url: `/edp/learning-content-recommend/delete?id=` + id })
  },

  // 导出学习内容推荐 Excel
  exportLearningContentRecommend: async (params) => {
    return await request.download({ url: `/edp/learning-content-recommend/export-excel`, params })
  }
}
