import request from "@/config/axios";

/** ----- ENUM ----- */

/** ----- INTERFACE - Require ----- */
export interface DepartmentReportReqVO {
  year: number // 查询年份
}

export interface EmployeesReportReqVO extends PageParam {
  deptId: number // 部门ID
  date: number // 查询年份
  sectId?: number // 子部门ID
}

/** ----- INTERFACE - Response ----- */
export interface DepartmentReportVO {
  baseInfo: DeptBaseInfo
  sectStudyStatistics: DeptSkillStudy[]
  sectStudyCompleteRate: DeptLearningContentRate[]
  top10: DeptSkillRate[]
  bottom10: DeptSkillRate[]
  sectLearningContentDistribution: DeptLearningContentDistribution[]
  sectSkipContentAnalysis: DeptSkipContentAnalysis[]
}

export interface DeptBaseInfo {
  deptName: string // 部门名称
  deptShortName: string // 部门简称
  employeeCount: number // 员工数
  positionCount: number // 岗位数
  sectionCount: number // 子部门数
}

export interface DeptSkillStudy {
  sectName: string // 子部门名称
  employeeCount: number // 员工数
  completedCourseCount: number // 完成课程总数
  avgStudyTime: number // 平均学习时长
  avgSkillCoverage: number // 平均技能覆盖率
}

export interface DeptLearningContentRate {
  sectName: string // 子部门名称
  completionRate: number // 完成率
}

export interface DeptSkillRate {
  skillName: string // 技能名称
  completionRate: number // 完成率
}

export interface DeptLearningContentDistribution {
  courseName: string // 课程名称
  completedCount: number // 完成数
  score: number // 评分
  courseType: string // 课程类型
}

export interface DeptSkipContentAnalysis {
  skillName: string // 技能名称
  skipContentCount: number // 跳过内容数
  skipPersonCount: number // 跳过人数
}

export interface EmployeesReportVO {
  userId: number;              // 用户ID
  avatar: string;              // 用户头像地址
  nickname: string;            // 用户名称
  positionName: string;        // 岗位名称
  totalStudyTime: number;      // 总学习时长 (h)
  completedCourseCount: number;// 已完成课程数
  skillCoverage: string;       // 技能覆盖率
}

export interface PersonalCompletionStatusReqVO {
  year: number // 查询年份
}

export interface ContentStarTop5ReqVO {
  year: number // 查询年份
}

export interface ErrorRateTop5ReqVO {
  year: number // 查询年份
}

export interface SkillCoverageReqVO {
  year: number // 查询年份
}

export interface StudyTimeDistributionReqVO {
  year: number // 查询年份
}

export interface PersonBaseInfoReqVO {
  year: number // 查询年份
}

export interface CoreLearningMetricsReqVO {
  year: number // 查询年份
}

export interface AIGCVOReqVO {
  year: number // 查询年份
}

export interface completedCourses {
  total: number // 总数
  completedCount: number // 完成数
  completionRate: string // 完成率
}

export interface skippedStudyContent {
  total: number // 总数
  skippedCount: number // 跳过数
}

export interface PersonalCompletionStatusVO {
  requiredSkills: string[] // 所需技能
  earnedCertificates: string[] // 获得的证书
  completedCourses: completedCourses // 完成的课程
  skippedStudyContent: skippedStudyContent // 跳过的学习内容
}

export interface ContentStarTop5VO {
  skillName: string // 技能名称
  star: number // 内容评分
}

export interface ErrorRateTop5VO {
  skillName: string // 技能名称
  errorRate: number // 错误率
}

export interface PersonSkillCoverageVO {
  softSkillCoverage: Record<string, any> // 软技能掌握情况
  hardSkillCoverage: Record<string, any> // 硬技能掌握情况
  hseCoverage: Record<string, any> // HSE掌握情况
}

export interface PersonAnalysisStudyTimeVO {
  earlyMorningPercentage: string // 凌晨学习百分比
  amPercentage: string // 上午学习百分比
  pmPercentage: string // 下午学习百分比
  eveningPercentage: string // 晚上学习百分比
}

export interface PersonBaseInfoVO {
  nickname: string // 用户昵称
  badgeNumber: string // 工号
  positionName: string // 岗位名称
  deptName: string // 部门名称
}

export interface LearningEngagement {
  totalLearningDuration: string // 总学习时长
  avgHoursPerDay: string // 日均学习时长
  activeDays: number // 活跃天数
}

export interface LearningProgress {
  total: number // 总数
  completedCount: number // 完成数
  completionRate: string // 完成率
}

export interface SkillCoverage {
  total: number // 总数
  masteredCount: number // 掌握数
  masteryRate: string // 掌握率
}

export interface LearningEffectiveness {
  avgExamCorrectRate: number // 平均考试正确率
  gotCertificateCount: number // 获得证书数量
}

export interface CoreLearningMetricsVO {
  learningEngagement: LearningEngagement // 学习参与度
  learningProgress: LearningProgress // 学习进度
  skillCoverage: SkillCoverage // 技能覆盖度
  learningEffectiveness: LearningEffectiveness // 学习有效性
}

export interface SkillWeakness {
  skillName: string // 技能名称
  errorRate: number // 错误率
  completeRate: number // 完成率
  courseAccess: number // 课程点击次数
}

export interface LearningInterest {
  skillName: string // 技能名称
  rate: number // 内容评分
  tniRequest: boolean // 是否有相关TNI请求
  completeRate: number // 课程完成率
}

export interface AIGCVO {
  studyOverview: string // 学习概况
  skipAnalysis: string // 跳过分析
  skillWeakness: SkillWeakness[] // 识别技能短板——错误率、课程完成率、访问次数
  learningInterest: LearningInterest[] // 发现学习兴趣——评分、培训需求、完成率
  studyMethodSuggest: string // 学习方法分析——AI建议
  studyUpgradeSuggest: string // 学习提升建议——AI建议
}

/** ----- API ----- */
/** 部门学习报告 */
export const departmentReportApi = {
  // 获取部门报告
  getDepartmentReport: async (params: DepartmentReportReqVO) => {
    return await request.get<DepartmentReportVO>({ url: `/report/dept/get`, params })
  },

  // 通过部门ID获取员工学习报告
  getEmployeesReportByDeptId: async (params: EmployeesReportReqVO) => {
    return await request.get<DepartmentReportVO>({ url: `/report/personal/get-sect-post-user-list`, params })
  }
}

/** 员工学习报告 */
export const employeesReportApi = {
  // 获取个人基本信息
  getPersonBaseInfo : async (params: PersonBaseInfoReqVO) => {
    return await request.get<PersonBaseInfoVO>({
      url: '/report/personal/baseInfo',
      params
    })
  },

  // 获取核心学习指标
  getCoreLearningMetrics : async (params: CoreLearningMetricsReqVO) => {
    return await request.get<CoreLearningMetricsVO>({
      url: '/report/personal/coreLearningMetrics',
      params
    })
  },

  // 获取学习要求完成情况
  getPersonalCompletionStatus : async (params: PersonalCompletionStatusReqVO) => {
    return await request.get<PersonalCompletionStatusVO>({
      url: '/report/personal/analysis/completionStatus',
      params
    })
  },

  // 获取技能掌握分布
  getSkillCoverage : async (params: SkillCoverageReqVO) => {
    return await request.get<PersonSkillCoverageVO>({
      url: '/report/personal/analysis/skillCoverage',
      params
    })
  },

  // 获取短板分布分析 - 岗位技能错误率
  getErrorRateTop5 : async (params: ErrorRateTop5ReqVO) => {
    return await request.get<ErrorRateTop5VO[]>({
      url: '/report/personal/analysis/errorRateTop5',
      params
    })
  },

  // 获取发现学习兴趣 - 内容评分前五柱状图
  getContentStarTop5 : async (params: ContentStarTop5ReqVO) => {
    return await request.get<ContentStarTop5VO[]>({
      url: '/report/personal/analysis/contentStarTop5',
      params
    })
  },

  // 获取学习时间分布分析
  getStudyTimeDistribution : async (params: StudyTimeDistributionReqVO) => {
    return await request.get<PersonAnalysisStudyTimeVO>({
      url: '/report/personal/analysis/studyTimeDistribution',
      params
    })
  },

  // 获取报告中AI生成的内容
  getAiContent : async (params: AIGCVOReqVO) => {
    return await request.get<AIGCVO>({
      url: '/report/personal/getAiGc',
      params
    })
  }
}