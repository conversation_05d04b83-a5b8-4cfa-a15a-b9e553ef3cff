export default {
  common: {
    inputText: '请输入',
    selectText: '请选择',
    chooseText: '请选择',
    choose: '选择',
    noData: '无数据',
    startTimeText: '开始时间',
    endTimeText: '结束时间',
    login: '登录',
    required: '该项为必填项',
    loginOut: '退出系统',
    profile: '个人中心',
    reminder: '温馨提示',
    loginOutMessage: '是否退出本系统？',
    back: '返回',
    ok: '确定',
    save: '保存',
    cancel: '取消',
    close: '关闭',
    delete: '删除',
    remove: '移除',
    reload: '重新加载',
    success: '成功',
    closeTab: '关闭标签页',
    closeTheLeftTab: '关闭左侧标签页',
    closeTheRightTab: '关闭右侧标签页',
    closeOther: '关闭其他标签页',
    closeAll: '关闭全部标签页',
    prevLabel: '上一步',
    nextLabel: '下一步',
    skipLabel: '跳过',
    doneLabel: '结束',
    menu: '菜单',
    menuDes: '以路由的结构渲染的菜单栏',
    collapse: '展开缩收',
    collapseDes: '展开和缩放菜单栏',
    tagsView: '标签页',
    tagsViewDes: '用于记录路由历史记录',
    tool: '工具',
    toolDes: '用于设置定制系统',
    query: '查询',
    reset: '重置',
    shrink: '收起',
    expand: '展开',
    confirmTitle: '系统提示',
    exportMessage: '是否确认导出数据项？',
    importMessage: '是否确认导入数据项？',
    createSuccess: '新增成功',
    updateSuccess: '修改成功',
    delMessage: '是否删除所选中数据？',
    delDataMessage: '是否删除数据？',
    delNoData: '请选择需要删除的数据',
    delSuccess: '删除成功',
    delOption: '确认删除所选选项吗？',
    keyWords: '关键词不能重复。',
    keyWordsLength: '单个关键词最多包含300个字符。',
    isRecommend: '推荐：',
    uploadMessage: '正在上传文件，请稍候...',
    uploadFormat: '支持格式：',
    uploadError: '加载失败',
    noScormLink: '未获取到Scorm播放链接',
    uploadLoadingError: '加载失败，请检查网络是否正常。',
    fromRepository: '来自资源库',
    source: '来源：',
    language: '语言：',
    duration: '时长：',
    format: '格式：',
    size: '大小：',
    subject: '主题：',
    type: '类型：',
    uploadFormatMessage: '支持格式：Zip、音频、视频、PDF、PPT、Word',
    uploadFormatMessage2: '支持格式：Video,PDF',
    uploadFormatMessage3: '支持格式：doc/txt/pdf/csv/xls/ppt',
    noExist: '注意：该资源已存在，无需再次上传。',
    inputNumberText: '请输入数字',
    selectTeachingForm: '选择教学形式',
    selectTeachingContent: '选择教学内容',
    learningSetting: '学习设置',
    noSubject: '无主题',
    unzip: '解压中',
    failure: '失败',
    nonScorm: '非Scorm',
    updateFailure: '更新时请先下架。',
    incompatible: '不兼容',
    index: '序号',
    status: '状态',
    createTime: '创建时间',
    updateTime: '更新时间',
    copy: '复制',
    copySuccess: '复制成功',
    copyError: '复制失败',
    upload: '请上传文件',
    uploadSuccessLength: '上传成功数量：',
    updateSuccessLength: '更新成功数量：',
    updateFailLength: '更新失败数量：',
    selectAll: '全选/全不选',
    expandAll: '全部展开/折叠',
    yes: '是',
    no: '否',
    expandSwitch: '展开',
    collapseSwitch: '折叠',
    loading: '加载中，请稍候',
    linkage: '父子联动(选中父节点，自动选择子节点):',
    message: '访问的路由地址，如：`user`。如需外网地址时，则以 `http(s)://` 开头',
    menuPath: '路由地址',
    menuMessage:
      "Controller 方法上的权限字符，如：@PreAuthorize(`@ss.hasPermission('system:user:list')`)",
    menuShowStatusMessage: '选择隐藏时，路由将不会出现在侧边栏，但仍然可以访问',
    menuAlwaysShowMessage: '选择不是时，当该菜单只有一个子菜单时，不展示自己，直接展示子菜单',
    menuCacheStatusMessage: '选择缓存时，则会被 `keep-alive` 缓存，必须填写「组件名称」字段',
    data: '数据',
    default: '默认',
    primary: '主要',
    warning: '警告',
    info: '信息',
    danger: '危险',
    catalog: '目录',
    button: '按钮',
    pleaseInput: '请输入',
    parameter: '参数',
    cannotBeEmpty: '不能为空',
    searchMenuContent: '请输入菜单内容',
    adminMenuName: '管理端',
    studentMenuName: '用户端',
    scan: '扫描',
    result: '结果',
    batchAction: '批量操作',
    ddtPermitNo: 'DDT许可证号',
    dateOfBirth: '出生日期',
    projectAsset: '项目/资产',
    workType: '工作类型',
    workTerm: '工作期限',
    drivingLicenceNumber: '驾驶证号',
    issuingDate: '签发日期',
    expiryDate: '到期日期',
    vehicle: '车辆',
    eyeTest: '视力测试',
    testResult: '测试结果',
    reject: '拒绝',
    pass: '通过',
    fail: '失败',
    postpone: '延期',
    batchReject: '批量拒绝',
    batchPass: '批量通过',
    batchFail: '批量失败',
    batchPostpone: '批量延期',
    successfulOperation: '操作成功',
    projectQRCode: '投影二维码',
    downloadQRCode: '下载二维码',
    launch: '启动',
    uploadFile: '上传',
    reUpload: '重新上传',
    showDebugCoords: '显示原图像素坐标',
    hideDebugCoords: '隐藏调试坐标',
    certificateName: '姓名',
    certificateNumber: '编号',
    certificateLogo: '标志',
    certificateOfficialSeal: '公章',
    certificateUser: '用户',
    certificateTime: '时间',
    materialName: '资料名称',
    creationTime: '创建时间',
    uploadMaterials: '上传资料',
    video: '视频',
    pdf: 'PDF',
    email: '邮箱',
    pocEmail: 'POC邮箱',
    phoneNumber: '电话号码',
    pocPhoneNumber: 'POC电话号码',
    contractHolder: '合同持有人',
    ePassportNo: '电子护照号',
    bookingMode: '预订模式',
    batchRemove: '批量移除',
    cantQueryProcessInfo: '无法查询流程信息！',
    preferredDate: '首选日期',
    receivedDate: '接收日期',
    courseTitle: '课程标题',
    workTerms: '工作期限',
    assign: '分配',
    batchAssign: '批量分配',
    pleaseSelectSameCourse: '请选择相同课程的学员',
    startDate: '开始日期',
    endDate: '结束日期',
    publishStatus: '发布状态',
    classTitle: '课堂标题',
    classType: '课堂类型',
    trainer: '讲师',
    classroom: '教室',
    date: '日期',
    bookingNumber: '预订人数',
    thisWeek: '本周',
    thisMonth: '本月',
    nextWeek: '下周',
    thisYear: '今年',
    allocationSuccessful: '分配成功',
    start: '开始',
    end: '结束',
    advancedScreening: '高级筛选',
    clear: '清空',
    uploadSuccess: '文件上传成功'
  },
  lock: {
    lockScreen: '锁定屏幕',
    lock: '锁定',
    lockPassword: '锁屏密码',
    unlock: '点击解锁',
    backToLogin: '返回登录',
    entrySystem: '进入系统',
    placeholder: '请输入锁屏密码',
    message: '锁屏密码错误'
  },
  error: {
    noPermission: `抱歉，您无权访问此页面。`,
    pageError: '抱歉，您访问的页面不存在。',
    networkError: '抱歉，服务器报告错误。',
    returnToHome: '返回首页',
    uploadError: '上传失败，请您重新上传！',
    uploadErrorLength: '最多只能上传一个文件！',
    pathError: '路径必须以 / 开头',
    pathErrorStart: '路径不能以 / 开头',
    fileFormatError: '文件格式不正确，请重新上传',
    imageFormatFile: `图片格式文件！`,
    imageSizeError: '上传的头像图片大小不能超过',
    imageSizeErrorLength: '请按照指定尺寸上传图片',
    imageLoading: '图片加载失败',
    fileLengthError: '上传的文件数量不能超过',
    uploadErrorMessage: '上传图片失败',
    pleaseSetDuration: '请设置时长',
    fileTypeError: '上传的文件类型与指定类型不匹配',
    fileSizeError: '文件大小不能超过',
    m: 'M！',
    failedToUploadFile: '文件上传失败',
    dropFileHereOr: '将文件拖放到此处或',
    pleaseChooseExamPaper: '请选择试卷',
    fileFormatIsNotCorrect: '文件格式不正确，请上传',
    formatFile: '格式文件！',
    fileSizeIsNotCorrect: '图像格式错误',
    fileSizeIsNotCorrectLength: '上传文件大小不能超过',
    imageInsertionFailed: '图像插入失败',
    uploadTo: '您一次最多可以上传',
    resourcesAtATime: '个资源！',
    positionNotGenerated: '当前岗位未生成过技能，学习地图生成不可用',
    positionAlreadyGenerated: '当前岗位已生成过学习地图，是否直接跳转到学习内容？'
  },
  loading: {
    upLoading: '正在上传图片，请稍候。。。'
  },
  permission: {
    hasPermission: `请设置操作权限标签值`,
    hasRole: `请设置角色权限标签值`
  },
  setting: {
    projectSetting: '项目配置',
    theme: '主题',
    layout: '布局',
    systemTheme: '系统主题',
    menuTheme: '菜单主题',
    interfaceDisplay: '界面显示',
    breadcrumb: '面包屑',
    breadcrumbIcon: '面包屑图标',
    collapseMenu: '折叠菜单',
    hamburgerIcon: '折叠图标',
    screenfullIcon: '全屏图标',
    sizeIcon: '尺寸图标',
    localeIcon: '多语言图标',
    messageIcon: '消息图标',
    tagsView: '标签页',
    tagsViewImmerse: '标签页沉浸',
    logo: '标志',
    greyMode: '灰色模式',
    fixedHeader: '固定头部',
    headerTheme: '头部主题',
    cutMenu: '切割菜单',
    copy: '拷贝',
    clearAndReset: '清除缓存并且重置',
    copySuccess: '拷贝成功',
    copyFailed: '拷贝失败',
    footer: '页脚',
    uniqueOpened: '菜单手风琴',
    tagsViewIcon: '标签页图标',
    reExperienced: '请重新退出登录体验',
    fixedMenu: '固定菜单',
    myInternalMessage: '我的站内信',
    viewAll: '查看全部',
    // 轮播图
    banner: {
      status: '上架状态',
      title: '标题',
      no: '编号',
      updateTime: '更新时间',
      basicInfo: '基本信息',
      maxSize: '（“上架状态”最多4项）',
      titlePH: '请输入新闻标题，少于50字',
      cover: '封面',
      coverPH: '990*310像素，支持PNG、JPG、GIF，小于5M',
      summary: '摘要',
      summaryPH: '请输入新闻摘要，少于5000字',
      detail: '详细内容',
      titleRule: '标题不能为空',
      coverRule: '封面不能为空',
      sort: '排序',
      onShelfTime: '上架时间'
    }
  },
  size: {
    default: '默认',
    large: '大',
    small: '小'
  },
  login: {
    welcome: '欢迎使用本系统',
    message: '开箱即用的中后台管理系统',
    tenantname: '租户名称',
    username: '用户名',
    password: '密码',
    code: '验证码',
    login: '登录',
    relogin: '重新登录',
    otherLogin: '其他登录方式',
    register: '注册',
    checkPassword: '确认密码',
    remember: '记住我',
    hasUser: '已有账号？去登录',
    forgetPassword: '忘记密码?',
    tenantNamePlaceholder: '请输入租户名称',
    usernamePlaceholder: '请输入用户名',
    passwordPlaceholder: '请输入密码',
    codePlaceholder: '请输入验证码',
    mobileTitle: '手机登录',
    mobileNumber: '手机号码',
    mobileNumberPlaceholder: '请输入手机号码',
    backLogin: '返回',
    getSmsCode: '获取验证码',
    btnMobile: '手机登录',
    btnQRCode: '二维码登录',
    qrcode: '扫描二维码登录',
    btnRegister: '注册',
    SmsSendMsg: '验证码已发送'
  },
  captcha: {
    verification: '请完成安全验证',
    slide: '向右滑动完成验证',
    point: '请依次点击',
    success: '验证成功',
    fail: '验证失败'
  },
  router: {
    login: '登录',
    socialLogin: '社交登录',
    home: '仪表板',
    dictName: '字典数据',
    analysis: '分析页',
    workplace: '工作台',
    addBanner: '新增轮播图',
    editBanner: '编辑轮播图',
    contentCourse: '内容课程',
    importList: '导入列表',
    addJourney: '添加/编辑学习地图',
    addOnBoarding: '添加/编辑入职培训',
    learningRecords: '学习记录',
    addCompanyPolicy: '添加/编辑公司政策',
    addOrientation: '添加/编辑方向',
    createExam: '创建考试',
    editExam: '编辑考试',
    viewExam: '查看考试',
    examRecord: '考试记录',
    addCustomizedPaper: '添加自定义试卷',
    addAutoPaper: '添加自动组卷',
    editPaper: '编辑试卷',
    questionMgt: '问题管理',
    addQuestion: '添加问题',
    editQuestion: '编辑问题',
    courseDetail: '课程详情',
    onboardingDetail: '入职培训详情',
    companyPolicyDetail: '公司政策详情',
    examDetail: '考试详情',
    studentDetail: '学员详情',
    manage: '管理',
    versionInfo: '版本信息',
    processDetail: '流程详情'
  },
  analysis: {
    newUser: '新增用户',
    unreadInformation: '未读消息',
    transactionAmount: '成交金额',
    totalShopping: '购物总量',
    monthlySales: '每月销售额',
    userAccessSource: '用户访问来源',
    january: '一月',
    february: '二月',
    march: '三月',
    april: '四月',
    may: '五月',
    june: '六月',
    july: '七月',
    august: '八月',
    september: '九月',
    october: '十月',
    november: '十一月',
    december: '十二月',
    estimate: '预计',
    actual: '实际',
    directAccess: '直接访问',
    mailMarketing: '邮件营销',
    allianceAdvertising: '联盟广告',
    videoAdvertising: '视频广告',
    searchEngines: '搜索引擎',
    weeklyUserActivity: '每周用户活跃量',
    activeQuantity: '活跃量',
    monday: '周一',
    tuesday: '周二',
    wednesday: '周三',
    thursday: '周四',
    friday: '周五',
    saturday: '周六',
    sunday: '周日'
  },
  workplace: {
    welcome: '你好',
    happyDay: '祝你开心每一天!',
    toady: '今日晴',
    notice: '通知公告',
    project: '项目数',
    access: '项目访问',
    toDo: '待办',
    introduction: '一个正经的简介',
    shortcutOperation: '快捷入口',
    operation: '操作',
    index: '指数',
    personal: '个人',
    team: '团队',
    quote: '引用',
    contribution: '贡献',
    hot: '热度',
    yield: '产量',
    dynamic: '动态',
    push: '推送',
    follow: '关注'
  },
  watermark: {
    watermark: '水印'
  },
  dialog: {
    dialog: '弹窗',
    open: '打开',
    close: '关闭',
    menuPermission: '菜单权限',
    dataPermission: '数据权限',
    test: '测试',
    detail: '详情',
    searchMenu: '菜单搜索',
    // --------我的站内信----------
    messageDetails: '消息详情',
    import: '导入',
    resourceSelection: '资源选择',
    selectEmployees: '选择员工',
    selectCompany: '选择公司',
    selectDepartment: '选择部门',
    select: '选择',
    edit: '编辑',
    resourceModificationLog: '资源修改日志',
    selectClassroom: '选择教室',
    confirm: '确认',
    venue: '场地',
    companyDetail: '公司详情'
  },
  sys: {
    api: {
      operationFailed: '操作失败',
      errorTip: '错误提示',
      errorMessage: '操作失败,系统异常!',
      timeoutMessage: '登录超时,请重新登录!',
      apiTimeoutMessage: '接口请求超时,请刷新页面重试!',
      apiRequestFailed: '请求出错，请稍候重试',
      networkException: '网络异常',
      networkExceptionMsg: '网络异常，请检查您的网络连接是否正常!',
      errMsg401: '用户没有权限（令牌、用户名、密码错误）!',
      errMsg403: '用户得到授权，但是访问是被禁止的。!',
      errMsg404: '网络请求错误,未找到该资源!',
      errMsg405: '网络请求错误,请求方法未允许!',
      errMsg408: '网络请求超时!',
      errMsg500: '服务器错误,请联系管理员!',
      errMsg501: '网络未实现!',
      errMsg502: '网络错误!',
      errMsg503: '服务不可用，服务器暂时过载或维护!',
      errMsg504: '网络超时!',
      errMsg505: 'http版本不支持该请求!',
      errMsg901: '演示模式，无法进行写操作!'
    },
    app: {
      logoutTip: '温馨提醒',
      logoutMessage: '是否确认退出系统?',
      menuLoading: '菜单加载中...'
    },
    exception: {
      backLogin: '返回登录',
      backHome: '返回首页',
      subTitle403: '抱歉，您无权访问此页面。',
      subTitle404: '抱歉，您访问的页面不存在。',
      subTitle500: '抱歉，服务器报告错误。',
      noDataTitle: '当前页无数据',
      networkErrorTitle: '网络错误',
      networkErrorSubTitle: '抱歉，您的网络连接已断开，请检查您的网络！'
    },
    lock: {
      unlock: '点击解锁',
      alert: '锁屏密码错误',
      backToLogin: '返回登录',
      entry: '进入系统',
      placeholder: '请输入锁屏密码或者用户密码'
    },
    login: {
      backSignIn: '返回',
      signInFormTitle: '登录',
      ssoFormTitle: '三方授权',
      mobileSignInFormTitle: '手机登录',
      qrSignInFormTitle: '二维码登录',
      signUpFormTitle: '注册',
      forgetFormTitle: '重置密码',
      signInTitle: '开箱即用的中后台管理系统',
      signInDesc: '输入您的个人详细信息开始使用！',
      policy: '我同意xxx隐私政策',
      scanSign: `扫码后点击"确认"，即可完成登录`,
      loginButton: '登录',
      registerButton: '注册',
      rememberMe: '记住我',
      forgetPassword: '忘记密码?',
      otherSignIn: '其他登录方式',
      // notify
      loginSuccessTitle: '登录成功',
      loginSuccessDesc: '欢迎回来',
      // placeholder
      accountPlaceholder: '请输入账号',
      passwordPlaceholder: '请输入密码',
      smsPlaceholder: '请输入验证码',
      mobilePlaceholder: '请输入手机号码',
      policyPlaceholder: '勾选后才能注册',
      diffPwd: '两次输入密码不一致',
      userName: '账号',
      password: '密码',
      confirmPassword: '确认密码',
      email: '邮箱',
      smsCode: '短信验证码',
      mobile: '手机号码'
    },
    user: {
      userName: '用户名',
      nickName: '昵称',
      userNumber: '用户编号',
      userNickname: '用户昵称',
      department: '部门',
      company: '公司',
      phoneNumber: '电话号码',
      userNameRule: '用户名不能为空',
      userNameLengthRule: '长度必须在5到50之间',
      nickNameLengthRule: '长度必须在1到128之间',
      userNicknameRule: '名称不能为空',
      passwordRule: '用户密码不能为空',
      passwordLengthRule: '用户密码长度必须在5到20之间',
      emailRule: '请输入正确的电子邮件地址',
      phoneNumberRule: '请输入正确的手机号码',
      deleteTip: '你确定要删除用户吗？',
      resetPasswordTip: '请输入新的密码给',
      resetPasswordConfirm: '重置后，员工的登录密码将被重置，并发送邮件通知员工。确认或取消？',
      resetPwdSuccess: '更新成功',
      import: '用户导入',
      importRes: '导入结果',
      addUser: '添加用户',
      editUser: '编辑用户',
      deptNamePH: '请输入',
      userNamePH: '请输入用户名',
      nickNamePH: '请输入昵称',
      phoneNumberPH: '请输入电话号码',
      resetPassword: '重置密码',
      assignRoles: '分配角色',
      departmentPH: '选择部门',
      departmentRule: '部门不能为空',
      companyPH: '选择公司',
      companyRule: '公司不能为空',
      sectionPH: '选择子部门',
      sectionRule: '子部门不能为空',
      positionPH: '选择岗位',
      positionRule: '岗位不能为空',
      userNicknamePH: '请输入用户昵称',
      email: '电子邮件',
      emailPH: '请输入您的电子邮件',
      password: '密码',
      passwordPH: '请输入密码',
      gender: '性别',
      genderPH: '选择性别',
      lineManagerPH: '选择直线经理',
      nationality: '国籍',
      workTerms: '工作条件',
      nationalityPH: '选择国籍',
      workTermPH: '选择工作条件',
      position: '岗位',
      role: '角色',
      rolePH: '选择角色',
      roleRule: '角色不能为空',
      updateData: '是否更新现有用户数据',
      importTip: '只能导入xls和xlsx文件',
      downloadTemplate: '下载模板',
      section: '子部门',
      badgePH: '请输入徽章号',
      badgeNo: '工号',
      badgeNoPH: '请输入您的徽章号',
      badgeNoRule: '徽章号不能为空',
      lineManager: '直线经理',
      status: '状态',
      statusPH: '选择状态',
      statusRule: '状态不能为空',
      onboardingDate: '入职日期',
      onboardingDatePH: '选择入职日期',
      createTime: '创建时间',
      userStatus: '用户状态',
      userStatusRule: '用户状态不能为空',
      userStatusPH: '选择用户状态',
      workTypeRule: '工作类型不能为空',
      workType: '工作类型',
      workTypePH: '选择工作类型'
    },
    // 公司模块
    company: {
      addCompany: '添加公司',
      editCompany: '编辑公司',
      companyName: '公司名称',
      abbreviation: '缩写',
      type: '类型',
      uniqueCode: '唯一编码',
      dataSource: '数据来源',
      superiorCompany: '上级公司',
      sort: '排序',
      createTime: '创建时间',
      creator: '创建者',
      status: '状态',
      yes: '是',
      no: '不',
      orderNum: '公司排序',
      companyCode: '公司编码',
      companyNamePH: '请输入公司名称',
      typePH: '请选择承包商类型',
      uniqueCodePH: '请输入唯一编码',
      dataSourcePH: '请输入数据来源',
      superiorCompanyPH: '请选择上级公司',
      abbreviationPH: '请输入缩写',
      companyCodePH: '请输入公司编码',
      serviceCompanyIdRule: '请选择上级公司',
      deptNameRule: '请输入公司名称',
      shortNameRule: '请输入缩写',
      orderNumRule: '显示顺序不能为空',
      deptCodeRule: '请输入公司代码',
      typeRule: '请选择承包商类型'
    },
    // 部门模块
    dept: {
      addDept: '添加部门',
      editDept: '编辑部门',
      expand: '折叠/展开',
      totalTip: '部门总数',
      deptName: '部门名称',
      superiorDept: '上级部门',
      superiorDeptPH: '请选择上级部门',
      deptSort: '部门排序',
      deptCode: '部门代码',
      deptCodePH: '请输入部门代码',
      delTip: '您确定要删除部门名称为',
      refuseDelTip: '当前部门包含子部门，无法删除',
      deptNamePH: '请输入部门名称',
      parentIdRule: '请选择上级部门',
      deptNameRule: '请输入部门名称',
      shortNameRule: '请输入部门简称',
      orderNumRule: '显示顺序不能为空',
      deptCodeRule: '请输入部门代码',
      typeRule: '请选择类型',
      parentPH: '请选择上级部门',
      abbrPH: '请输入部门简称'
    },
    // 子部门模块
    section: {
      addSection: '添加子部门',
      editSection: '编辑子部门',
      sectionName: '子部门名称',
      sectionNamePH: '请输入子部门名称',
      superiorSection: '上级子部门',
      superiorSectionRule: '请选择上级子部门',
      sectionSort: '子部门排序',
      sectionCode: '子部门代码',
      sectionCodePH: '请输入子部门代码',
      sectionNameRule: '请输入子部门名称',
      sectionCodeRule: '请输入子部门代码',
      orderNumRule: '请输入子部门排序'
    },
    // 岗位模块
    post: {
      addPost: '添加岗位',
      editPost: '编辑岗位',
      postName: '岗位名称',
      deptName: '自动生成',
      section: '部门',
      sectionPH: '请选择部门',
      parentPosition: '上级岗位',
      parentPositionPH: '请选择上级岗位',
      positionName: '岗位名称',
      positionPH: '请输入岗位名称',
      positionSort: '岗位排序',
      positionCode: '岗位代码',
      positionCodePH: '请输入岗位代码',
      positionNameRule: '请输入岗位名称',
      positionCodeRule: '请输入岗位代码',
      orderNumRule: '请输入岗位排序',
      totalTip: '岗位总数'
    },
    // 配置管理(一期)
    config: {
      addConfig: '添加参数',
      editConfig: '编辑参数',
      configName: '参数名称',
      configNamePH: '请输入参数名称',
      configKey: '参数键名',
      configKeyPH: '请输入参数键名',
      builtInSystem: '系统内置',
      configId: '参数主键',
      configValue: '参数键值',
      configValuePH: '请输入参数键值',
      remark: '备注',
      remarkPH: '请输入备注',
      configNameRule: '参数名称不能为空',
      configKeyRule: '参数键名不能为空',
      configValueRule: '参数键值不能为空'
    }
  },
  profile: {
    user: {
      title: '个人信息',
      username: '用户名称',
      nickname: '用户昵称',
      mobile: '手机号码',
      email: '用户邮箱',
      dept: '所属部门',
      posts: '所属岗位',
      roles: '所属角色',
      sex: '性别',
      man: '男',
      woman: '女',
      createTime: '创建日期'
    },
    info: {
      title: '基本信息',
      basicInfo: '基本资料',
      resetPwd: '修改密码',
      userSocial: '社交信息'
    },
    rules: {
      nickname: '请输入用户昵称',
      mail: '请输入邮箱地址',
      truemail: '请输入正确的邮箱地址',
      phone: '请输入正确的手机号码',
      truephone: '请输入正确的手机号码'
    },
    password: {
      oldPassword: '旧密码',
      newPassword: '新密码',
      confirmPassword: '确认密码',
      oldPwdMsg: '请输入旧密码',
      newPwdMsg: '请输入新密码',
      cfPwdMsg: '请输入确认密码',
      pwdRules: '长度在 6 到 20 个字符',
      diffPwd: '两次输入密码不一致'
    }
  },
  cropper: {
    selectImage: '选择图片',
    uploadSuccess: '上传成功',
    modalTitle: '头像上传',
    okText: '确认并上传',
    btn_reset: '重置',
    btn_rotate_left: '逆时针旋转',
    btn_rotate_right: '顺时针旋转',
    btn_scale_x: '水平翻转',
    btn_scale_y: '垂直翻转',
    btn_zoom_in: '放大',
    btn_zoom_out: '缩小',
    preview: '预览'
  },
  // 关于输入框内的文字、开始时间和结束时间
  input: {
    // --------用户模块----------
    deptPlaceholder: '请输入部门名称',
    userPlaceholder: '请输入用户名称',
    userNickNamePlaceholder: '请输入用户昵称',
    phonePlaceholder: '请输入手机号码',
    emailPlaceholder: '请输入邮箱',
    // 请输入用户密码
    passwordTitle: '请输入用户密码',
    passwordPlaceholder: '请输入密码',
    startTime: '开始时间',
    endTime: '结束时间',
    pleaseInput: '请输入内容',
    // --------角色模块----------
    roleNamePlaceholder: '请输入角色名称',
    roleKeyPlaceholder: '请输入角色标识',
    orderPlaceholder: '请输入显示顺序',
    // --------菜单模块----------
    menuCodePlaceholder: '请输入菜单编码',
    menuPlaceholder: '请输入菜单名称',
    menuNamePlaceholder: '请输入中文菜单名称',
    menuNameEnPlaceholder: '请输入英文菜单名称',
    menuNameArPlaceholder: '请输入阿语菜单名称',
    routeAddressPlaceholder: '请输入路由地址',
    menuComponentPlaceholder: '例如说：system/user/index',
    menuComponentNamePlaceholder: '例如说：SystemUser',
    menuPermissionPlaceholder: '请输入权限标识',
    // --------部门模块----------
    deptNamePlaceholder: '请输入部门名称',
    deptStatus: '请选择部门状态',
    parentDept: '请选择上级部门',
    deptNickName: '请输入负责人',
    deptPhone: '请输入联系电话',
    deptEmail: '请输入邮箱',
    // --------岗位模块----------
    postNamePlaceholder: '请输入岗位名称',
    postCodePlaceholder: '请输入岗位编码',
    postSortPlaceholder: '请输入岗位顺序',
    remark: '请输入备注',
    // --------字典模块----------
    dictNamePlaceholder: '请输入字典名称',
    dictTypePlaceholder: '请输入字典类型',
    dictStatusPlaceholder: '请输入字典状态',
    dictLabelPlaceholder: '请输入字典标签',
    dictLabelCnPlaceholder: '请输入中文字典标签',
    dictLabelEnPlaceholder: '请输入英文字典标签',
    dictLabelArPlaceholder: '请输入阿语字典标签',
    dictNameTypePlaceholder: '请输入参数名称',
    dictValuePlaceholder: '请输入数据标签',
    dictKeyPlaceholder: '请输入字典键值',
    dictClassPlaceholder: '请输入 CSS Class',
    dictRemarkPlaceholder: '请输入字典排序',
    // --------消息中心----------
    // 短信管理模块(短信渠道)
    smsSignPlaceholder: '请输入短信签名',
    channelCodePlaceholder: '请选择渠道编码',
    apiAccountPlaceholder: '请输入短信 API 的账号',
    apiSecretPlaceholder: '请输入短信 API 的密钥',
    apiCallbackPlaceholder: '请输入短信发送回调 URL',
    // 短信管理模块(短信模板)
    templateCodePlaceholder: '请输入模板编号',
    templateNamePlaceholder: '请输入模板名称',
    templateContentPlaceholder: '请输入模板内容',
    templateApiNoPlaceholder: '请输入短信 API 的模板编号',
    // 邮箱管理模块(邮件模板)
    emailInputPlaceholder: '请输入收件邮箱',
    // --------站内信管理----------
    // 站内信管理===========> 模板管理
    templateCode: '请输入模版编码',
    templateName: '请输入模版名称',
    senderName: '请输入发件人名称',
    userCodePlaceholder: '请输入用户编号',
    // --------通知公告----------
    noticeTitlePlaceholder: '请输入公告标题',
    // --------租户列表----------
    tenantNamePlaceholder: '请输入租户名称',
    contactName: '请输入联系人',
    contactPhone: '请输入联系手机',
    tenantQuotaPlaceholder: '请输入账号额度',
    expireTime: '请选择过期时间',
    domainPlaceholder: '请输入绑定域名',
    // --------通知公告----------
    packageNamePlaceholder: '请输入套餐名',
    // --------基础设施----------
    // 基础设施===========> 配置管理
    paramNamePlaceholder: '请输入参数名称',
    paramKeyPlaceholder: '请输入参数键名',
    paramCategoryPlaceholder: '请输入参数分类',
    paramValuePlaceholder: '请输入参数键值'
  },
  // 下拉框内的默认文字
  select: {
    userStatus: '用户状态',
    userRole: '请选择角色信息',
    belongingDept: '请选择归属部门',
    pleaseSelect: '请选择',
    status: '请选择状态',
    menuStatus: '请选择菜单状态',
    smsStatusPlaceholder: '请选择启用状态',
    templateChannelCode: '请选择短信渠道编号',
    templateType: '请选择短信类型',
    // 短信管理模块(短信日志)
    templateChannel: '请选择短信渠道',
    templateChannelPlaceholder: '请选择短信渠道编号',
    templateStatusPlaceholder: '请选择发送状态',
    templateSendStatusPlaceholder: '请选择接收状态',
    templateTypePlaceholder: '请选择类型',
    templateReceiveNamePlaceholder: '请选择接收人',
    pleaseSelectStatus: '请选择开启状态',
    userTypePlaceholder: '请选择用户类型',
    notifyTemplateTypePlaceholder: '请选择模版类型',
    // --------通知公告----------
    noticeStatusPlaceholder: '请选择公告状态',
    noticeTypePlaceholder: '请选择公告类型',
    tenantStatusPlaceholder: '请选择状态',
    tenantPackagePlaceholder: '请选择租户套餐',
    // --------基础设施----------
    // 基础设施===========> 配置管理
    paramTypePlaceholder: '请选择系统内置'
  },
  // 搜索区域内容标题
  search: {
    userName: '用户名称',
    phoneNumber: '手机号码',
    status: '状态',
    createTime: '创建时间',
    search: '搜索',
    roleName: '角色名称',
    roleKey: '角色标识'
  },
  table: {
    table: '表格',
    index: '序号',
    title: '标题',
    author: '作者',
    createTime: '创建时间',
    action: '操作',
    pagination: '分页',
    reserveIndex: '叠加序号',
    restoreIndex: '还原序号',
    showSelections: '显示多选',
    hiddenSelections: '隐藏多选',
    showExpandedRows: '显示展开行',
    hiddenExpandedRows: '隐藏展开行',
    header: '头部',
    // --------用户模块----------
    userNumber: '用户编号',
    userName: '用户名称',
    userNickName: '用户昵称',
    deptName: '部门',
    phoneNumber: '手机号码',
    status: '状态',
    // --------角色模块----------
    roleNumber: '角色编号',
    roleName: '角色名称',
    roleType: '角色类型',
    roleKey: '角色标识',
    showSort: '显示顺序',
    remark: '备注',
    // --------菜单模块----------
    menuName: '菜单名称',
    menuIcon: '菜单图标',
    menuSort: '排序',
    menuPermission: '权限标识',
    menuComponent: '组件路径',
    menuComment: '组件名称',
    menuStatus: '菜单状态',
    // --------部门模块----------
    nickName: '负责人',
    deptSort: '排序',
    // --------岗位模块----------
    postNumber: '岗位编号',
    postName: '岗位名称',
    postCode: '岗位编码',
    postSort: '岗位排序',
    postRemark: '岗位备注',
    // --------字典模块----------
    dictNumber: '字典编号',
    dictName: '字典名称',
    dictType: '字典类型',
    dictLabel: '字典标签',
    dictLabelCn: '中文字典标签',
    dictLabelEn: '英文字典标签',
    dictLabelAr: '阿文字典标签',
    dictValue: '字典键值',
    dictSort: '字典排序',
    dictColor: '颜色类型',
    cssClass: 'CSS Class',
    // --------消息中心----------
    // 短信管理模块(短信渠道)
    channelId: '编号',
    channelCode: '渠道编码',
    smsSign: '短信签名',
    channelStatus: '启用状态',
    channelApiKey: '短信 API 的账号',
    channelApiSecret: '短信 API 的密钥',
    channelCallbackUrl: '短信发送回调 URL',
    // 短信管理模块(短信模板)
    templateName: '模板名称',
    templateCode: '模板编码',
    templateContent: '模板内容',
    templateTitle: '模板标题',
    templateType: '模板类型',
    templateApiTemplateCode: '短信 API 的模板编号',
    templateChannel: '短信渠道',
    // 短信管理模块(短信日志)
    templateNumber: '编号',
    logSmsContent: '短信内容',
    logSendStatus: '发送状态',
    logReceiveStatus: '接收状态',
    logTemplateCode: '模板编号',
    // 邮箱管理模块(邮箱账号)
    emailAccount: '邮箱账号',
    email: '邮箱',
    emailUsername: '用户名',
    emailPassword: '邮箱密码',
    emailSmtpHost: 'SMTP 服务器域名',
    emailSmtpPort: 'SMTP 服务器端口',
    emailSsl: '是否开启 SSL',
    emailStarttlsEnable: '是否开启 STARTTLS',
    // 邮箱管理模块(邮件模板)
    emailFromName: '发送人名称',
    // 邮箱管理模块(邮件记录)
    emailUserType: '用户类型',
    emailTitle: '邮件标题',
    emailContent: '邮件内容',
    emailParams: '邮箱参数',
    emailAddress: '发送邮箱地址',
    emailTemplateFromName: '模版发送人名称',
    emailSendNumber: '发送返回的消息编号',
    emailSendException: '发送异常',
    sendStatus: '发送状态',
    receiveEmail: '收件邮箱',
    // --------站内信管理----------
    // 站内信管理===========> 模板管理
    type: '类型',
    senderName: '发送人名称',
    openStatus: '开启状态',
    // 站内信管理===========> 消息记录
    templateParams: '模版参数',
    readStatus: '是否已读',
    readTime: '阅读时间',
    // --------通知公告----------
    noticeNumber: '公告编号',
    noticeTitle: '公告标题',
    noticeType: '公告类型',
    // --------租户列表----------
    tenantNumber: '租户编号',
    tenantName: '租户名',
    tenantPackage: '租户套餐',
    sysTenant: '系统租户',
    contactName: '联系人',
    contactPhone: '联系手机',
    tenantStatus: '租户状态',
    tenantQuota: '账号额度',
    domain: '绑定域名',
    // --------租户套餐----------
    tenantPackageNumber: '套餐编号',
    tenantPackageName: '套餐名',
    // --------基础设施----------
    // 基础设施===========> 配置管理
    paramId: '参数主键',
    paramCategory: '参数分类',
    paramName: '参数名称',
    paramKey: '参数键名',
    paramValue: '参数键值',
    paramVisible: '是否可见',
    paramClass: '系统内置',
    // --------我的站内信----------
    sendName: '发送人',
    sendTime: '发送时间',
    messageContent: '消息内容',
    detail: '详情',
    read: '已读'
  },
  action: {
    search: '搜索',
    create: '新增',
    add: '新增',
    del: '删除',
    delete: '删除',
    edit: '编辑',
    update: '编辑',
    reset: '重置',
    preview: '预览',
    more: '更多',
    sync: '同步',
    save: '保存',
    detail: '详情',
    export: '导出',
    import: '导入',
    userImport: '用户导入',
    generate: '生成',
    logout: '强制退出',
    test: '测试',
    typeCreate: '字典类型新增',
    typeUpdate: '字典类型编辑',
    dataCreate: '字典数据新增',
    dataUpdate: '字典数据编辑',
    resetPassword: '重置密码',
    assignRole: '分配角色',
    confirm: '确认',
    cancel: '取消',
    drag: '将文件拖到此处，或',
    upload: '点击上传',
    updateExistingUsers: '是否更新已经存在的用户数据',
    fileFormat: '仅允许导入 xls、xlsx 格式文件。',
    downloadTemplate: '下载模板',
    assign: '分配角色',
    menuPermission: '菜单权限',
    dataPermission: '数据权限',
    expand: '展开/折叠',
    refreshMenuCache: '刷新菜单缓存',
    refreshConfigCache: '刷新缓存',
    searchIcon: '搜索图标',
    push: '推送',
    fileUpload: '文件上传',
    markRead: '标记已读',
    markAllRead: '全部已读',
    putOnShelf: '上架',
    removeOffShelf: '下架',
    addKeyWord: '+ 新关键字',
    uploadFormat: '小于 10MB 的 xls/xlsx 文件',
    moveOffShelf: '移至下架',
    importingList: '导入列表',
    assignCourse: '分配课程',
    statistics: '统计',
    back: '返回',
    offShelf: '下架',
    onShelf: '上架',
    clickRetry: '点击此处重试',
    chooseExamPaper: '选择试卷',
    uploading: '正在上传',
    previous: '上一步',
    unzip: '解压',
    moveUp: '上移',
    moveDown: '下移',
    uploadFile: '上传',
    questionManagement: '问题管理',
    addChoice: '+ 添加选项',
    close: '关闭',
    view: '查看',
    chooseAll: '全选',
    remove: '移除',
    reAssign: '重新分配',
    answerDetails: '答案详情',
    uploadInBatch: '批量上传',
    parsingList: '解析列表',
    record: '记录',
    restart: '重新启动',
    report: '报告',
    batchDelete: '批量删除',
    settingInBatch: '批量设置',
    submit: '提交',
    refresh: '刷新',
    resissue: '重新发起'
  },
  // 新增修改form表单(包括搜索条件的form，目前将search JSON数据也合并到这里面,search JSON数据勿删！！！)标题使用
  form: {
    input: '输入框',
    inputNumber: '数字输入框',
    default: '默认',
    icon: '图标',
    mixed: '复合型',
    textarea: '多行文本',
    slot: '插槽',
    position: '位置',
    autocomplete: '自动补全',
    select: '选择器',
    selectGroup: '选项分组',
    selectV2: '虚拟列表选择器',
    cascader: '级联选择器',
    switch: '开关',
    rate: '评分',
    colorPicker: '颜色选择器',
    transfer: '穿梭框',
    render: '渲染器',
    radio: '单选框',
    button: '按钮',
    checkbox: '多选框',
    slider: '滑块',
    datePicker: '日期选择器',
    shortcuts: '快捷选项',
    today: '今天',
    yesterday: '昨天',
    aWeekAgo: '一周前',
    week: '周',
    year: '年',
    month: '月',
    dates: '日期',
    daterange: '日期范围',
    monthrange: '月份范围',
    dateTimePicker: '日期时间选择器',
    dateTimerange: '日期时间范围',
    timePicker: '时间选择器',
    timeSelect: '时间选择',
    inputPassword: '密码输入框',
    passwordStrength: '密码强度',
    operate: '操作',
    change: '更改',
    restore: '还原',
    disabled: '禁用',
    disablement: '解除禁用',
    delete: '删除',
    add: '添加',
    setValue: '设置值',
    resetValue: '重置值',
    set: '设置',
    subitem: '子项',
    formValidation: '表单验证',
    verifyReset: '验证重置',
    remark: '备注',
    // --------用户模块----------
    userNickName: '用户昵称',
    // 归属部门
    belongingDept: '归属部门',
    phoneNumber: '手机号码',
    userName: '用户名称',
    userPassword: '用户密码',
    email: '邮箱',
    userSex: '用户性别',
    posts: '岗位',
    // --------角色模块----------
    roles: '角色',
    roleName: '角色名称',
    roleType: '角色类型',
    roleKey: '角色标识',
    showSort: '显示顺序',
    status: '状态',
    // --------菜单模块----------
    menuPermission: '菜单权限',
    permissionScope: '权限范围',
    menuCode: '菜单编码',
    menu: '菜单名称',
    menuName: '中文菜单名称',
    menuNameEn: '英文菜单名称',
    menuNameAr: '阿语菜单名称',
    previousMenu: '上级菜单',
    menuType: '菜单类型',
    menuIcon: '菜单图标',
    menuPath: '路由地址',
    menuComponent: '组件路径',
    menuComponentName: '组件名称',
    menuPermissionKey: '权限标识',
    menuSort: '显示排序',
    menuStatus: '菜单状态',
    menuShowStatus: '显示状态',
    show: '显示',
    hide: '隐藏',
    alwaysShow: '总是显示',
    always: '总是',
    not: '不是',
    cacheStatus: '缓存状态',
    cache: '缓存',
    noCache: '不缓存',
    // --------部门模块----------
    deptName: '部门名称',
    deptStatus: '部门状态',
    parentDept: '上级部门',
    deptShowSort: '显示排序',
    deptNickName: '负责人',
    deptPhone: '联系电话',
    deptEmail: '邮箱',
    // --------岗位模块----------
    postName: '岗位名称',
    postCode: '岗位编码',
    postSort: '岗位顺序',
    // --------字典模块----------
    dictName: '字典名称',
    dictType: '字典类型',
    createTime: '创建时间',
    dictLabel: '字典标签',
    dictLabelCn: '中文字典标签',
    dictLabelEn: '英文字典标签',
    dictLabelAr: '阿语字典标签',
    dictValue: '字典键值',
    dictShowSort: '字典排序',
    dictColor: '颜色类型',
    cssClass: 'CSS Class',
    // --------消息中心----------
    // 短信管理模块(短信渠道)
    signature: '短信签名',
    enableStatus: '启用状态',
    channelCode: '渠道编码',
    channelStatus: '启用状态',
    channelApiKey: '短信 API 的账号',
    channelApiSecret: '短信 API 的密钥',
    channelCallbackUrl: '短信发送回调 URL',
    // 短信管理模块(短信模板)
    smsType: '短信类型',
    openStatus: '开启状态',
    templateCode: '模板编码',
    templateId: '短信 API 的模板编号',
    templateContent: '短信模板',
    templateChannel: '短信渠道',
    templateChannelCode: '短信渠道编号',
    templateName: '模板名称',
    templateSubstance: '模板内容',
    mobile: '手机号',
    // 短信管理模块(短信日志)
    logTemplateCode: '模板编号',
    sendStatus: '发送状态',
    sendTime: '发送时间',
    receiveStatus: '接收状态',
    receiveTime: '接收时间',
    // 邮箱管理模块(邮件模板)
    receiveEmail: '收件邮箱',
    // --------站内信管理----------
    // 站内信管理===========> 模板管理
    senderName: '发件人名称',
    type: '类型',
    emailUserType: '用户类型',
    recipientID: '接收人ID',
    recipient: '接收人',
    // 站内信管理===========> 消息记录
    userId: '用户编号',
    templateType: '模版类型',
    // --------通知公告----------
    noticeTitle: '公告标题',
    noticeContent: '公告内容',
    noticeStatus: '公告状态',
    noticeType: '公告类型',
    // --------租户列表----------
    tenantName: '租户名称',
    contactName: '联系人',
    contactPhone: '联系手机',
    tenantStatus: '租户状态',
    tenantPackage: '租户套餐',
    tenantQuota: '账号额度',
    expireTime: '过期时间',
    domain: '绑定域名',
    // --------租户套餐----------
    packageName: '套餐名称',
    // --------基础设施----------
    // 基础设施===========> 配置管理
    paramName: '参数名称',
    paramKey: '参数键名',
    paramType: '系统内置',
    paramCategory: '参数分类',
    paramValue: '参数键值',
    paramVisible: '是否可见',
    // --------我的站内信----------
    readStatus: '是否已读'
  },
  // form表单中的检验提醒
  formValidate: {
    // --------用户模块----------
    userName: '用户名称不能为空',
    userNickName: '用户昵称不能为空',
    userPassword: '用户密码不能为空',
    email: '请输入正确的邮箱地址',
    phoneNumber: '请输入正确的手机号码',
    // --------角色模块----------
    roles: '请选择角色',
    roleName: '角色名称不能为空',
    roleKey: '角色标识不能为空或者输入的格式不正确',
    showSort: '显示顺序不能为空',
    status: '状态不能为空',
    remark: '备注不能为空',
    // --------菜单模块----------
    menuName: '中文菜单名称不能为空',
    menuNameEn: '英文菜单名称不能为空',
    menuNameAr: '阿语菜单名称不能为空',
    menuCode: '菜单编码不能为空',
    menuSort: '菜单顺序不能为空',
    menuPath: '路由地址不能为空',
    // --------部门模块----------
    parentDept: '上级部门不能为空',
    deptName: '部门名称不能为空',
    deptShowSort: '显示顺序不能为空',
    deptEmail: '请输入正确的邮箱地址',
    deptPhone: '请输入正确的手机号码',
    // --------岗位模块----------
    postName: '岗位名称不能为空',
    postCode: '岗位编码不能为空',
    postStatus: '岗位状态不能为空',
    postRemark: '岗位内容不能为空',
    // --------字典模块----------
    dictName: '字典名称不能为空',
    dictType: '字典类型不能为空',
    dictLabelCn: '中文数据标签不能为空',
    dictLabelEn: '英文数据标签不能为空',
    dictLabelAr: '阿语数据标签不能为空',
    dictValue: '数据键值不能为空',
    dictShowSort: '数据排序不能为空',
    // --------消息中心----------
    // 短信管理模块(短信渠道)
    signature: '短信签名不能为空',
    channelCode: '渠道编码不能为空',
    enableStatus: '启用状态不能为空',
    channelApiKey: '短信 API 的账号不能为空',
    // 短信管理模块(短信模板)
    templateType: '短信类型不能为空',
    templateOpenStatus: '开启状态不能为空',
    templateCode: '模板编码不能为空',
    templateName: '模板名称不能为空',
    templateContent: '模板内容不能为空',
    templateId: '短信 API 的模板编号不能为空',
    templateChannelCode: '短信渠道编号不能为空',
    templateMobile: '手机不能为空',
    // 邮箱管理模块(邮件模板)
    templateEmail: '邮箱不能为空',
    templateEmailContent: '模版内容不能为空',
    // --------站内信管理----------
    // 站内信管理===========> 模板管理
    messageType: '消息类型不能为空',
    sendName: '发件人姓名不能为空',
    // --------站内信管理----------
    // 站内信管理===========> 模板管理
    userId: '用户编号不能为空',
    templateNumber: '模版编号不能为空',
    // --------通知公告----------
    noticeTitle: '公告标题不能为空',
    noticeContent: '公告内容不能为空',
    noticeType: '公告类型不能为空',
    // --------租户列表----------
    tenantName: '租户名称不能为空',
    tenantPackage: '租户套餐不能为空',
    contactName: '联系人不能为空',
    tenantStatus: '租户状态不能为空',
    tenantQuota: '账号额度不能为空',
    expireTime: '过期时间不能为空',
    domain: '绑定域名不能为空',
    // --------租户套餐----------
    packageName: '套餐名称不能为空',
    packageMenu: '关联的菜单编号不能为空',
    // --------基础设施----------
    // 基础设施===========> 配置管理
    paramCategory: '参数分类不能为空',
    paramName: '参数名称不能为空',
    paramKey: '参数键名不能为空',
    paramValue: '参数键值不能为空',
    paramVisible: '是否可见不能为空'
  },
  confirm: {
    // --------菜单模块----------
    refreshMenuCache: '即将更新缓存刷新浏览器！刷新菜单缓存',
    sendMessage: '是否推送所选中通知？',
    isOffShelf: '课程下架后，学生端将无法再次进入该课程。是否确认？',
    importCourse: '您确定要导入该课程吗？',
    reImportCourse: '您确定要重新导入当前课程吗？',
    deImportCourse: '您确定要删除当前课程吗？',
    deleteChapter: '您确定要删除名为的当前章节吗？',
    deleteOption: '确认删除所选选项？',
    deleteAssignment: '确认取消任务吗？',
    deleteChoice: '删除此选项，确认还是不确认',
    deleteAssignCourse: '确认是否取消任务？',
    uploading: '正在上传，关闭将中断上传，确定要关闭吗？',
    deleteScorm: '确定要删除标题为 " 的 scorm 吗？',
    refreshSuccess: '刷新当前页面将会中断上传，您确定要继续刷新当前页面吗？',
    sureTo: '确定要',
    resourceOf: '的资源',
    success: '成功',
    deleteResource: '确定要删除 ID 为 的资源吗'
  },
  success: {
    sendSuccess: '提交发送成功！发送结果，见发送日志编号：',
    pushSuccess: '推送成功',
    // --------我的站内信----------
    readSuccess: '全部已读成功!',
    // 批量已读成功
    readSuccessBatch: '批量已读成功!'
  },
  descriptions: {
    // --------消息中心模块----------
    // 短信管理--------> 短信日志(详情)
    logId: '日志主键',
    channelId: '短信渠道',
    templateId: '短信模板',
    templateCode: 'API 的模板编号',
    userId: '用户信息',
    content: '短信内容',
    params: '短信参数',
    createTime: '创建时间',
    sendStatus: '发送状态',
    sendTime: '发送时间',
    sendResult: 'API 发送结果',
    sendLogId: '短信编号',
    requestId: 'API 请求编号',
    receiveStatus: 'API 接收状态',
    receiveResult: 'API 接收结果',
    // 站内信管理===========> 消息记录
    id: '编号',
    userType: '用户类型',
    userNumber: '用户编号',
    templateNumber: '模版编号',
    templateCodes: '模版编码',
    senderName: '发件人名称',
    templateContent: '模版内容',
    templateParams: '模版参数',
    templateType: '模版类型',
    readStatus: '是否已读',
    readTime: '阅读时间',
    // --------我的站内信----------
    sendName: '发送人',
    messageType: '消息类型',
    messageContent: '内容'
  },
  global: {
    status: '状态',
    createTime: '创建时间',
    deleteSuccess: '删除成功',
    importSuccess: '导入成功',
    importRowLimit: '超出导入行数限制',
    notInFormat: '上传的表格格式不符合要求',
    enable: '启用',
    disable: '禁用',
    changeStatusFront: '你确定要',
    the: '这个',
    user: '用户',
    successfully: '成功',
    tip: '提示',
    ok: '确认',
    cancel: '取消',
    editSuccess: '编辑成功',
    addSuccess: '添加成功',
    pleaseSelectCourse: '请选择至少一个课程',
    submitSuccess: '提交成功',
    startDate: '开始日期',
    endDate: '结束日期',
    unknown: '未知',
    search: '搜索',
    reset: '重置',
    add: '添加',
    edit: '编辑',
    delete: '删除',
    deleteTip: '你确定要删除 ',
    deleteFile: '数据正在上传，是否删除？',
    deleteExam: '请注意，删除后学习者将无法查看考试。确定删除吗？',
    import: '导入',
    export: '导出',
    action: '操作',
    selectPH: '请选择',
    remark: '备注',
    inputPH: '请输入内容',
    confirm: '确认',
    fileDrag: '将文件拖放到此处',
    or: '或',
    clickToUpload: '点击上传',
    select: '请选择数据',
    clearSelection: '清除选择',
    company: '公司',
    refresh: '刷新缓存成功',
    video: '视频',
    audio: '音频',
    file: '文件',
    scorm: 'Scorm',
    aicc: 'AICC'
  },
  hr: {
    section: {
      refuseDelTip: '存在子部门，无法删除',
      delTip: '您确定要删除名为',
      des: '描述',
      totalTip: '子部门总数'
    }
  },
  // 一期关于分类国际化
  category: {
    topic: {
      addTopic: '添加主题',
      editTopic: '编辑主题',
      subjectName: '主题名称',
      subjectNamePH: '请输入',
      keyWords: '关键词',
      introduction: '简介',
      sort: '排序',
      creationTime: '创建时间',
      parentSubject: '父主题',
      parentSubject0: '父主题0',
      parentSubject2: '父主题2',
      cover: '封面',
      coverPH: 'PNG、JPG、GIF格式,不超过500KB',
      subjectNameRule: '请输入主题名称',
      keyWordsRule: '请输入关键词',
      introductionRule: '请输入简介'
    },
    journey: {
      addCategory: '添加分类',
      editCategory: '编辑分类',
      titleRule: '分类名称是必需的',
      sortRule: '排序是必需的',
      categoryTitle: '分类名称',
      categoryTitlePH: '请输入',
      title: '标题',
      sort: '排序',
      creationTime: '创建时间',
      creator: '创建者'
    }
  },
  // 操作日志
  log: {
    operaLog: {
      logId: '日志编号',
      operator: '操作人',
      operationModule: '操作模块',
      operationName: '操作名',
      operationContent: '操作内容',
      operationTime: '操作时间',
      businessNo: '业务编号',
      actionIP: '操作 IP',
      actionLog: '操作日志.xls',
      traceId: '链路追踪',
      operatorId: '操作人编号',
      operatorName: '操作人名字',
      operatorUA: '操作人 UA',
      operatorExt: '操作拓展参数',
      requestURL: '请求 URL',
      operatorPH: '请输入操作人员',
      operatorModulePH: '请输入操作模块',
      operatorNamePH: '请输入操作名',
      operatorContentPH: '请输入操作名',
      businessNoPH: '请输入业务编号'
    },
    loginLog: {
      userName: '用户名称',
      loginAddress: '登录地址',
      loginTime: '登录时间',
      logId: '日志编号',
      operationType: '操作类型',
      browser: '浏览器',
      loginResult: '登录结果',
      actionLog: '登录日志.xls',
      userNamePH: '请输入用户名称',
      loginAddressPH: '请输入登录地址'
    }
  },
  // learning-center
  learningCenter: {
    course: {
      days: '天',
      employee: '员工',
      course: '课程',
      courses: '课程列表',
      id: 'ID',
      format: '格式',
      courseTemplate: '课程模板.xlsx',
      title: '标题',
      onShelfStatus: '上架状态',
      newCourse: '新建课程',
      isRecommend: '推荐状态',
      isAssigned: '分配状态',
      level: '级别',
      language: '语言',
      duration: '时长',
      subTitle: '副标题',
      courseSource: '课程来源',
      uniqueId: '唯一标识符',
      tasks: '任务',
      exams: '考试',
      assignedNumber: '分配数量',
      basicInfo: '基本信息',
      courseCatalogue: '课程目录',
      assignScope: '分配范围',
      courseStatistics: '课程统计',
      addCourse: '添加课程',
      topicIdRule: '请选择',
      courseNameRule: '请输入课程名称，不超过200个字符',
      courseName: '课程名称',
      coverPH: '750*442像素或16:9，PNG、JPG、GIF格式，小于5M',
      timeliness: '时效性',
      permanent: '永久',
      setExpirationDate: '设置到期日期',
      certificate: '证书',
      autoAssign: '自动分配',
      courseDescription: '课程描述',
      taskMgt: '任务管理',
      studentName: '姓名',
      badgeNo: '工号',
      email: '邮箱',
      type: '类型',
      deptName: '部门',
      section: '子部门',
      company: '公司',
      position: '岗位',
      star: '星级',
      operator: '操作员',
      sendingStatus: '发送状态',
      score: '分数',
      status: '状态',
      electiveCourse: '选修课程学生',
      mandatoryCourse: '完成的必修课程',
      comprehensiveCourse: '综合课程评分',
      selectCourseRule: '请选择课程信息',
      chooseCourse: '选择课程',
      taskId: '任务ID',
      contentTitle: '内容标题',
      contentId: '内容ID',
      assetUUID: '资产UUID',
      categoryL1: '分类-L1',
      areaL2: '区域-L2',
      subjectL3: '主题-L3',
      channelL4: '频道-L4',
      estimatedDuration: '预计时长',
      isExam: '考试(是/否)',
      isSubtitle: '字幕(是/否)',
      isLocal: '本地/云端',
      imageUrl: '图片URL',
      keywords: '关键词',
      courseFileName: '课程文件名',
      fileLocation: '文件位置',
      importTime: '导入时间',
      mandatoryScope: '必修范围',
      languageRule: '语言不能为空',
      titleRule: '标题不能为空',
      durationRule: '时长不能为空',
      setTeachingContent: '设置教学内容',
      // 导入状态
      notStarted: '未开始',
      inProcess: '进行中',
      successfully: '成功',
      fail: '失败'
    },
    exam: {
      examPaper: '试卷',
      addExam: '添加考试',
      editExam: '编辑考试',
      customizedPaper: '自定义试卷',
      autoPaper: '自动组卷',
      passScore: '及格分数',
      passScorePH: '请输入考试分数',
      points: '分',
      minutes: '分钟',
      examScorePH: '请输入考试分数',
      editExamTip: '编辑考试不会影响之前参加考试学生的成绩和结果，仅影响编辑后的数据。',
      examPaperName: '试卷名称',
      examDuration: '考试时长',
      examAttempts: '考试次数',
      taskName: '任务名称',
      appendix: '附件',
      examTimes: '考试次数',
      passFail: '通过/未通过'
    },
    paper: {
      paperName: '试卷名称',
      type: '类型',
      itemNumber: '题号',
      fullScore: '满分'
    },
    task: {
      addTask: '添加任务',
      tasks: '任务',
      totalTasks: '总任务数',
      taskNo: '任务编号',
      taskName: '任务名称',
      type: '类型',
      progress: '进度'
    },
    journey: {
      addJourney: '添加旅程',
      editJourney: '编辑旅程',
      title: '标题',
      cover: '封面',
      categoryTitle: '分类',
      uniqueId: '唯一标识符',
      creationTime: '创建时间',
      journeyTitleRule: '请输入旅程名称，不超过200个字符'
    },
    boarding: {
      addBoarding: '添加入职培训',
      editBoarding: '编辑入职培训',
      boarding: '入职培训',
      category: '分类',
      mandatoryRule: '必填项是必需的',
      titleRule: '标题是必需的',
      coverRule: '封面是必需的',
      fileRule: '文件是必需的',
      durationRule: '时长是必需的',
      mandatory: '必修',
      description: '描述',
      name: '姓名',
      badgeNumber: '工号',
      title: '标题',
      learningRecords: '学习记录'
    },
    companyPolicy: {
      companyPolicy: '公司政策',
      ackRule: '确认是必需的',
      declarationRule: '声明是必需的',
      titleRule: '标题是必需的',
      categoryIdRule: '分类是必需的',
      coverRule: '封面是必需的',
      muploaderRule: '文件是必需的',
      durationRule: '时长是必需的',
      addCompanyPolicy: '添加公司政策',
      editCompanyPolicy: '编辑公司政策',
      ack: '确认',
      controlledScope: '控制范围',
      acknowledgement: '确认书',
      declaration: '宣言'
    },
    orientation: {
      displayRule: '显示是必需的',
      addOrientation: '添加方向',
      editOrientation: '编辑方向',
      display: '显示',
      orientation: '导览'
    },
    onboardingMap: {
      index: '序号',
      name: '学习地图名称',
      mode: '学习模式',
      effectiveVersion: '当前生效的版本号',
      createTime: '创建时间',
      action: '操作',
      detail: '详情',
      pleaseInputName: '请输入学习地图名称',
      pleaseInputVersion: '请输入当前生效的版本号',
      startDate: '开始日期',
      endDate: '结束日期',
      search: '搜索',
      reset: '重置',
      add: '新增'
    }
  },
  // 考试
  examMgt: {
    question: {
      poolTitle: '题库标题',
      contentRule: '请输入内容',
      questionContent: '问题内容',
      questionType: '问题类型',
      choice: '选项',
      correct: '正确',
      itemTitleRule: '请输入条目标题',
      choiceSubjectRule: '请选择主题',
      itemTitle: '条目标题',
      correctAnswer: '正确答案:',
      questionAnalysis: '问题分析',
      addNewQuestion: '添加新问题',
      editQuestion: '编辑问题',
      questionPreview: '问题预览',
      singleChoice: '单选题',
      multipleChoice: '多选题',
      trueOrFalse: '判断题',
      thisQuestion: '此问题？'
    },
    paper: {
      name: '名称',
      itemName: '条目名称',
      questionNumber: '问题编号',
      content: '内容',
      type: '类型',
      selectedQuestion: '已选问题:',
      setScoreInBatch: '批量设置分数',
      paperNameRule: '请输入试卷名称',
      createAutoPaper: '创建自动组卷',
      namePH: '请输入名称',
      chooseQuestion: '选择问题',
      questionPackage: '题库包',
      multipleChoiceType: '多选题类型',
      fullScore: '满分:',
      chooseQuestionPackage: '选择题库包',
      createCustomizedPaper: '创建自定义试卷',
      editAutoPaper: '编辑自动组卷',
      editCustomizedPaper: '编辑自定义试卷',
      pleaseChooseTheQuestion: '请选择问题，员工的问题是相同的',
      autoPaper: '自动组卷',
      autoPaperPH: '预设从一个或多个题库中选择。考试开始时，系统将从题库包中随机选择题目'
    },
    exam: {
      notAttend: '未参加',
      failed: '未通过',
      fail: '失败',
      pass: '通过',
      numberExam: '考试参与人数',
      numberPass: '通过员工数',
      numberFailed: '未通过员工数',
      examPassRate: '考试通过率',
      onSchedule: '按计划进行',
      submitTime: '提交时间',
      topScore: '最高分',
      examNameRule: '请输入考试名称',
      examTimeRule: '请选择考试时间',
      examAttemptsRule: '请输入考试尝试次数',
      passScoreRule: '请输入及格分数',
      examDurationRule: '请输入考试时长',
      examPaperRule: '请选择考试试卷',
      examName: '考试名称',
      examTime: '考试时间',
      selectExamPaper: '选择考试试卷',
      times: '次',
      examDescription: '考试描述',
      examDescriptionPH: '请输入考试描述',
      examTaken: '已进行的考试:',
      remain: '剩余:',
      namePH: '请输入名称',
      paperRule: '请选择试卷',
      to: '至',
      moment: '时刻',
      examType: '考试类型',
      notStarted: '未开始',
      inProcess: '进行中',
      expired: '已过期',
      examStatistics: '考试统计',
      score: '分数:',
      nonAnswered: '未作答',
      userList: '用户列表'
    }
  },
  // 统计
  statistics: {
    course: {
      requiredUsers: '必修用户',
      completedUsersOfRequired: '已完成的必修用户',
      uncompletedUsersOfRequired: '未完成的必修用户',
      electiveUsers: '选修用户',
      subjectCourseStatistics: '科目课程统计',
      completed: '已完成',
      sending: '发送中',
      sendSuccess: '发送成功',
      sendFail: '发送失败',
      none: '无',
      scorm: 'SCORM'
    },
    exam: {
      totalExams: '考试总数',
      passRates: '通过率',
      scoreDetails: '分数详情'
    },
    companyPolicy: {
      allCompanyPolicy: '所有公司政策',
      acknowledgement: '确认',
      optional: '可选'
    },
    student: {
      student: '学生',
      active: '活跃',
      inactive: '不活跃',
      badge: '徽章',
      mandatoryCourse: '必修课程',
      electiveCourse: '选修课程',
      exam: '考试',
      courseEvaluation: '课程评估'
    },
    training: {
      // 统计卡片
      totalCourses: '课程总数',
      offlineClasses: '线下课堂',
      virtualClasses: '虚拟课堂',
      hybridClasses: '混合课堂',
      totalStudents: '学生总数',
      hstDdtCourses: 'HST/DDT课程',

      // 报告和图表
      report: '报告',
      exportReport: '导出报告',

      // 表格列标题
      courseTitle: '课程标题',
      courseCode: '课程代码',
      category: '分类',
      noOfClasses: '课堂数量',
      plannedAttendance: '计划出勤',
      actualAttendance: '实际出勤',
      noShow: '缺席',
      attendance: '出勤率',
      classEvaluationScore: '课堂评估分数',
      trainerEvaluationScore: '培训师评估分数',
      facilityEvaluationScore: '设施评估分数',

      // 图表标题
      percentageActualAttendanceNoShow: '实际出勤和缺席百分比',
      passportIssued: '已颁发证书',
      bocAosKbr: 'BOC-AOS-KBR',
      noShowReport: '缺席报告',
      coursesDelivered: '已交付课程',
      noOfCourses: '课程数量',
      weeklyReport: '周报',
      mlcDdtWeeklyReport: 'MLC-DDT周报',

      // 详情页面
      student: '学生',
      class: '课堂',
      company: '公司',
      trainer: '培训师',
      details: '详情',

      // 统计数据
      total: '总计',
      plannedAttendanceLabel: '计划出勤',
      actualAttendanceLabel: '实际出勤',
      noShowLabel: '缺席',

      // 培训师相关
      trainerType: '培训师类型',
      trainerName: '培训师姓名',
      trainerDetail: '培训师详情',

      // 课堂相关
      classCode: '课堂代码',
      classType: '课堂类型',
      classroom: '教室',
      duration: '持续时间',

      // 时间选项
      thisWeek: '本周',
      thisMonth: '本月',
      thisYear: '本年',
      time: '时间',

      // 公司相关
      studentNumber: '学生人数',
      completionRate: '完成率',
      notCompleted: '未完成',

      // 学生相关
      testResult: '测试结果',
      attendanceStatus: '出勤状态',
      name: '姓名',
      badgeNumber: '工号',
      position: '岗位',

      // 课堂详情相关
      classDetail: '课堂详情',
      courseName: '课程名称',
      checkIn: '签到',
      checkOut: '签退',
      checkInOut: '签到/签退',
      department: '部门',
      badgeNo: '工号',

      // 公司详情相关
      companyDetail: '公司详情',
      companyName: '公司名称'
    }
  },
  dashboard: {
    quick: {
      quickEntrance: '快速入口'
    },
    statistics: {
      totalStudents: '总学生数',
      distinctStudents: '不同学生数',
      totalAdmins: '总管理员数',
      distinctAdmins: '不同管理员数',
      loginStatistics: '登录统计'
    },
    total: {
      totalNumberOfCourse: '课程总数',
      totalNumberOfExam: '考试总数',
      totalNumberOfOnboarding: '入职培训总数',
      totalNumberOfOrientation: '导览总数',
      totalNumberOfCompanyPolicy: '公司政策总数',
      user: '用户'
    }
  },
  // 资源管理
  resource: {
    id: 'ID',
    size: '大小',
    referenced: '引用',
    dataPermissionsRule: '数据权限不能为空',
    fileUploadCompleted: '文件上传完成',
    thereAreStillUnprocessedFiles: '仍有未处理的文件',
    allFilesError: '所有文件均已上传，但存在错误',
    supportResource:
      '支持一次最多上传5个资源。格式支持：Zip, MP3, MP4, PPT, DOCX, PDF, Excel, TXT。',
    eventType: '事件类型',
    before: '之前',
    resourceCreated: '资源创建',
    after: '之后',
    operateTime: '操作时间',
    noResourceModificationRecord: '目前没有资源修改记录',
    languageRule: '语言不能为空',
    uploadIn: '正在上传至',
    batch: '批量',
    waitingForUploads: '等待上传...',
    uploadFailed: '上传失败',
    fileBeingAnalyzed: '文件正在被分析，这可能需要一些时间',
    fileParsing: '文件正在解析中...'
  },
  warning: {
    noResource: '当前选中的课程没有资源，请检查',
    noResourceList: '当前课程没有资源，禁止上架',
    unsupportedCourseFileTypes: '不支持的课程文件类型，请检查',
    canNotDelete: '无法删除',
    noResourcesByNow: '目前没有需要提交的资源',
    resourcesUploadedIncorrectly: '存在上传不正确的资源，请检查',
    someFilesNotUploaded: '部分文件未上传',
    noPreview: '当前文件类型不支持预览',
    delOption: '请删除未填写的选项',
    delContent: '请填写问题内容',
    answerOption: '请选择正确答案'
  },

  survey: {
    // 通用
    category: '分类',
    template: '模板',
    question: '问题',
    instance: '实例',
    response: '回答',
    statistics: '统计',

    // 分类管理
    categoryManagement: '分类管理',
    categoryName: '分类名称',
    parentCategory: '父级分类',
    rootCategory: '根分类',
    sortOrder: '排序',
    status: '状态',
    enabled: '启用',
    disabled: '禁用',
    description: '描述',
    addCategory: '新增分类',
    editCategory: '编辑分类',
    addSubcategory: '新增子分类',
    deleteCategory: '删除分类',
    expandAll: '展开全部',
    collapseAll: '收起全部',

    // 模板管理
    templateManagement: '模板管理',
    templateName: '模板名称',
    templateCode: '模板编码',
    templateEditor: '模板编辑器',
    addTemplate: '新增模板',
    editTemplate: '编辑模板',
    copyTemplate: '复制模板',
    deleteTemplate: '删除模板',
    previewTemplate: '预览模板',
    publishTemplate: '发布模板',
    archiveTemplate: '归档模板',
    templateStatus: '模板状态',
    draft: '草稿',
    published: '已发布',
    archived: '已归档',
    unpublished: '未发布',
    ongoing: '进行中',
    ended: '已结束',
    questionCount: '问题数量',

    // 问题管理
    questionManagement: '问题管理',
    questionTitle: '问题标题',
    questionType: '问题类型',
    questionEditor: '问题编辑器',
    addQuestion: '新增问题',
    editQuestion: '编辑问题',
    copyQuestion: '复制问题',
    deleteQuestion: '删除问题',
    required: '必答',
    optional: '选答',
    singleChoice: '单选题',
    multipleChoice: '多选题',
    trueFalse: '判断题',
    rating: '评分题',
    fileUpload: '文件上传题',
    text: '文本题',
    singleChoiceDesc: '单选题，用户只能选择一个选项',
    multipleChoiceDesc: '多选题，用户可以选择多个选项',
    trueFalseDesc: '判断题，用户选择正确或错误',
    ratingDesc: '评分题，用户给出评分',
    fileUploadDesc: '文件上传题，用户上传文件',
    textDesc: '文本题，用户输入文本内容',

    // 问题选项默认文本
    option1: '选项1',
    option2: '选项2',
    correct: '正确',
    incorrect: '错误',
    veryPoor: '很差',
    veryGood: '很好',
    pleaseEnterAnswer: '请输入您的答案',

    // 问题配置相关
    questionConfig: '问题配置',
    options: '选项',
    option: '选项',
    addOption: '添加选项',
    score: '分数',
    minSelections: '最少选择',
    maxSelections: '最多选择',
    allowedFileTypes: '允许的文件类型',
    selectAllowedFileTypes: '选择允许的文件类型',
    maxFileCount: '最大文件数量',
    singleFileSize: '单文件大小(MB)',
    minLength: '最小长度',
    maxLength: '最大长度',
    textRows: '文本行数',
    placeholderText: '占位符文本',
    enterPlaceholderText: '请输入占位符文本',
    correctAnswerScore: '正确答案分数',
    incorrectAnswerScore: '错误答案分数',
    minScore: '最小分数',
    maxScore: '最大分数',
    scorePerPoint: '每分分值',
    wordDocument: 'Word文档',
    wordDocumentNew: 'Word文档(新)',
    imageJPG: '图片(JPG)',
    imagePNG: '图片(PNG)',
    selectFile: '选择文件',
    allowedUpload: '允许上传',
    maxFiles: '最多',
    filesMaxSize: '个文件，单个文件不超过',
    wordCountRequirement: '字数要求',
    unlimited: '无限制',
    points: '分',
    selectionRange: '选择范围',
    copyOf: '副本',

    // 实例管理
    instanceManagement: '实例管理',
    instanceName: '实例名称',
    instanceEditor: '实例编辑器',
    addInstance: '新增实例',
    editInstance: '编辑实例',
    copyInstance: '复制实例',
    deleteInstance: '删除实例',
    publishInstance: '发布实例',
    stopInstance: '停止实例',
    instanceStatus: '实例状态',
    stopped: '已停止',
    expired: '已过期',
    startTime: '开始时间',
    endTime: '结束时间',
    department: '部门',
    anonymous: '匿名',
    scopeType: '作用域类型',
    public: '公开',
    internal: '内部',
    submissionFrequency: '提交频率',
    once: '单次',
    onceOnly: '仅一次',
    multiple: '多次',
    maxSubmissions: '最大提交次数',
    maxResponses: '最大响应数',
    maxResponsesTip: '当收到指定数量的有效问卷时，问卷将自动结束',
    submissionInterval: '提交间隔(小时)',
    allowViewStatistics: '允许查看统计',
    estimatedTime: '预计时长(分钟)',
    responseCount: '回答数量',
    completionRate: '完成率',
    view: '查看',
    completed: '已完成',
    inProgress: '进行中',
    notStarted: '未开始',
    confirmPublish: '确认发布此问卷实例？',
    confirmStop: '确认停止此问卷实例？',
    confirmCopy: '确认复制此问卷实例？',
    confirmDelete: '确认删除此问卷实例？',
    publishSuccess: '发布成功',
    stopSuccess: '停止成功',
    copySuccess: '复制成功',
    deleteSuccess: '删除成功',
    exportSuccess: '导出成功',
    instanceDetail: '实例详情',
    basicInfo: '基本信息',
    totalResponses: '总回答数',
    averageTime: '平均用时',
    todayResponses: '今日回答数',
    minutes: '分钟',
    scopeConfiguration: '作用域配置',
    targetType: '目标类型',
    targetName: '目标名称',
    user: '用户',
    role: '角色',
    templateInfo: '模板信息',
    questionList: '问题列表',
    trueOrFalse: '判断题',
    unknown: '未知',
    addScope: '添加作用域',
    noScopeConfigured: '暂无作用域配置',
    target: '目标',
    pleaseSelectTarget: '请选择目标',
    scopeAlreadyExists: '作用域已存在',
    scopeConfigSuccess: '作用域配置成功',
    scopeAddedSuccess: '添加作用域成功，共添加 {count} 个',
    scopeConfig: '作用域配置',
    noScopeData: '暂无作用域数据',
    availableUsers: '可选用户',
    availableDepts: '可选部门',
    selectedScopes: '已选作用域',
    searchMobile: '请输入手机号',
    totalDepts: '共 {count} 个部门',
    scopeRemovedSuccess: '成功移除 {count} 个作用域',
    selectionError: '选择操作失败，请重试',
    loadUserDataError: '加载用户数据失败，请重试',
    loadDeptDataError: '加载部门数据失败，请重试',
    hours: '小时',
    loadFailed: '加载失败',
    createSuccess: '创建成功',
    updateSuccess: '更新成功',
    pleaseSelectTemplate: '请先选择模板',
    pleaseCheckFormData: '请检查表单数据',
    featureNotImplemented: '功能暂未实现',
    publicSurveyNoScope: '公开问卷无需配置作用域，所有用户都可以参与',
    questionStatistics: '问题统计',
    responseData: '回答数据',
    totalAnswers: '回答总数',
    validResponses: '有效回答',
    pleaseEnterUserName: '请输入用户姓名',
    to: '至',
    displayRange: '显示范围',
    pleaseSelect: '请选择',
    allRecords: '全部记录',
    validRecordsOnly: '仅有效记录',
    search: '搜索',
    reset: '重置',
    userInfo: '用户信息',
    completionTime: '完成耗时',
    seconds: '秒',
    valid: '有效',
    invalid: '无效',
    submitCount: '提交次数',
    submitCountFormat: '第{count}次',
    ipAddress: 'IP地址',
    operation: '操作',
    viewDetail: '查看详情',
    loadDataFailed: '加载数据失败',
    responseDetailTitle: '回答记录详情',
    deptName: '部门名称',
    totalScore: '总得分',
    isValid: '是否有效',
    validResponse: '有效回答',
    invalidResponse: '无效回答',
    responseStatistics: '回答统计',
    totalQuestions: '总问题数',
    answeredQuestions: '已回答问题数',
    skippedQuestions: '跳过问题数',
    answerDetails: '答题详情',
    answer: '答案',
    file: '文件',
    viewFile: '查看文件',
    notAnswered: '未回答',
    close: '关闭',
    loadDetailFailed: '加载详情失败',
    totalRecords: '共 {count} 条记录',
    noData: '暂无数据',
    answered: '已回答',
    noFileUploaded: '未上传文件',
    pleaseEnter: '请输入',
    loadStatistics: '加载统计',
    loadStatisticsFailed: '加载统计失败',
    loadingTemplateData: '正在加载模板数据...',

    // 统计页面
    surveyStatistics: '问卷统计',
    loadingStatistics: '加载统计数据...',
    refreshData: '刷新数据',
    exportReport: '导出报告',
    participationStatus: '参与情况',
    validAnswers: '有效回答',
    averageScore: '平均得分',
    highest: '最高',
    lowest: '最低',
    averageCompletionTime: '平均完成时间',
    statisticsTime: '统计时间',
    lastUpdated: '最后更新',
    submissionTimeDistribution: '提交时间分布',
    byDay: '按天',
    byWeek: '按周',
    byMonth: '按月',
    departmentParticipationDistribution: '部门参与分布',
    questionDetailedStatistics: '问题详细统计',
    searchQuestions: '搜索问题...',
    clear: '清空',
    noMatchingQuestions: '没有找到匹配的问题',
    noStatisticsData: '暂无统计数据',
    possibleReasons: '可能的原因：',
    noParticipants: '问卷还没有人参与',
    dataGenerating: '数据正在生成中',
    reload: '重新加载',
    dataRefreshSuccess: '数据刷新成功',
    dataRefreshFailed: '数据刷新失败',
    exportFeatureInDevelopment: '导出功能开发中...',
    pleaseEnterSearchKeyword: '请输入搜索关键词',
    foundMatchingQuestions: '找到 {count} 个匹配的问题',
    searchConditionsCleared: '已清空搜索条件',
    dailySubmission: '当日提交',
    cumulativeSubmission: '累计提交',
    participationRate: '参与率',
    participantCount: '参与人数',

    responseRate: '回答率',
    people: '人',
    noOptionData: '暂无选项数据',
    yes: '是',
    no: '否',
    star: '星',
    answerCount: '回答数量',
    answerList: '回答列表',
    collapse: '收起',
    viewAllAnswers: '查看全部 {count} 条回答',
    uploadedFiles: '上传文件数',
    uploadedPeople: '上传人数',
    notUploadedCount: '未上传人数',
    fileList: '文件列表',
    fileName: '文件名',
    uploadTime: '上传时间',
    fileSize: '文件大小',
    download: '下载',
    skipCount: '跳过人数',
    optionDistribution: '选项分布',
    selectTemplate: '选择模板',
    changeTemplate: '更换模板',
    searchTemplateName: '搜索模板名称',
    templateSelectedSuccess: '模板选择成功',
    select: '选择',
    selectTargetType: '选择目标类型',
    searchUserName: '搜索用户名称',
    searchDeptName: '搜索部门名称',
    searchRoleName: '搜索角色名称',
    pleaseSearch: '请输入搜索内容',
    userName: '用户名称',
    roleName: '角色名称',
    name: '名称',
    statisticsOverview: '统计概览',
    loadTemplateFailed: '加载模板列表失败',
    templateSelectFailed: '模板选择失败',
    pleaseSelectScope: '请选择作用域',
    loadScopeListFailed: '加载作用域列表失败',
    allScopeAlreadyExists: '所选作用域已全部存在',
    normal: '正常',
    instanceContent: '实例内容',
    basicInfoDesc: '配置实例基本信息',
    templateInfoDesc: '查看模板详情',
    scopeConfigDesc: '配置作用域',
    statisticsDesc: '查看统计数据',
    timeRange: '时间范围',
    submitFrequency: '提交频率',
    searchNickname: '搜索昵称',
    searchEmail: '搜索邮箱',
    searchLeader: '搜索负责人',
    searchRoleCode: '搜索角色编码',
    nickname: '昵称',
    email: '邮箱',
    mobile: '手机号',
    deptCode: '部门编码',
    parentDept: '上级部门',
    sort: '排序',
    leader: '负责人',
    phone: '电话',
    roleCode: '角色编码',
    roleType: '角色类型',
    system: '系统',
    custom: '自定义',

    // 回答记录
    responseManagement: '回答记录管理',
    responseDetail: '回答详情',
    deleteResponse: '删除回答',
    exportResponse: '导出回答',

    // 统计分析
    statisticsAnalysis: '统计分析',
    statisticsDetail: '统计详情',
    add: '新增',
    edit: '编辑',
    delete: '删除',
    copy: '复制',
    preview: '预览',
    export: '导出',
    import: '导入',
    save: '保存',
    cancel: '取消',
    confirm: '确认',

    // 消息提示
    importSuccess: '导入成功',

    // 表单验证
    categoryNameRequired: '分类名称不能为空',
    templateNameRequired: '模板名称不能为空',
    questionTitleRequired: '问题标题不能为空',

    // 其他
    createTime: '创建时间',
    updateTime: '更新时间',
    actions: '操作',
    loading: '加载中...',
    refreshCategories: '刷新分类',
    searchCategoryName: '搜索分类名称',
    autoGenerated: '留空则自动生成',
    dragOrClickToAdd: '拖拽或点击添加题目',
    dragQuestionHere: '拖拽题目到这里',
    dragQuestionDesc: '从左侧拖拽题目类型到这里，或点击题目类型添加',
    allTemplates: '所有模板',
    poor: '很差',
    excellent: '很好',
    allowedTypes: '允许类型',
    maxSize: '最大大小',
    lengthRequirement: '长度要求',
    saveTemplateFirst: '请先保存模板',
    previewOpened: '预览已打开',
    insertHere: '插入到这里',
    fullscreen: '全屏编辑',
    exitFullscreen: '退出全屏',
    new: '新',
    scrollToSeeMore: '滚动查看更多'
  },
  'OAuth 2.0': 'OAuth 2.0', // 避免菜单名是 OAuth 2.0 时，一直 warn 报错
  // -------------------------在线学院 ----------------------------------
  academy: {
    classroom: {
      name: '名称',
      location: '位置',
      roomNumber: '房间号',
      totalSeats: '总座位数',
      description: '描述',
      status: '状态',
      time: '时间',
      startDate: '开始日期',
      endDate: '结束日期',
      pleaseSelect: '请选择',
      pleaseInput: '请输入',
      search: '搜索',
      reset: '重置',
      add: '新增',
      edit: '编辑',
      view: '查看',
      delete: '删除',
      action: '操作',
      classroomNumber: '教室编号',
      pleaseEnterName: '请输入名称',
      cover: '封面',
      default: '默认',
      customize: '自定义',
      pleaseInputDescription: '请输入描述',
      cancel: '取消',
      save: '保存',
      nameRequired: '教室名称不能为空',
      roomNumberRequired: '教室编号不能为空',
      statusRequired: '状态不能为空',
      locationRequired: '位置不能为空',
      totalSeatsRequired: '教室容量不能为空'
    },
    trainer: {
      trainerType: '讲师类型',
      company: '公司',
      department: '部门',
      position: '岗位',
      time: '时间',
      startDate: '开始日期',
      endDate: '结束日期',
      trainerName: '讲师姓名',
      pleaseSelect: '请选择',
      pleaseInput: '请输入',
      search: '搜索',
      reset: '重置',
      add: '新增',
      edit: '编辑',
      delete: '删除',
      action: '操作',
      badgeNo: '工号',
      // Form placeholders
      trainerNamePlaceholder: '请输入讲师姓名',
      trainerTypePlaceholder: '请选择讲师类型',
      // Form validation messages
      trainerNameRequired: '讲师姓名不能为空',
      trainerTypeRequired: '讲师类型不能为空',
      pleaseSelectUser: '请选择用户',
      // Button labels
      cancel: '取消',
      save: '保存',
      // Description labels
      badge: '工号',
      email: '邮箱'
    },
    course: {
      // Search form labels
      category: '分类',
      categoryPlaceholder: '请选择分类',
      language: '语言',
      languagePlaceholder: '请选择语言',
      trainerType: '培训类型',
      trainerTypePlaceholder: '请选择培训类型',
      courseTitle: '课程标题',
      courseTitlePlaceholder: '请输入课程标题',
      shortTitle: '简短标题',
      shortTitlePlaceholder: '请输入简短标题',
      // Button labels
      search: '搜索',
      reset: '重置',
      add: '新增',
      assign: '分配',
      edit: '编辑',
      copy: '复制',
      delete: '删除',
      cancel: '取消',
      confirm: '确认',
      // Table column labels
      courseCode: '课程编码',
      approvals: '审批',
      validity: '有效期',
      action: '操作',
      months: '个月',
      yes: '是',
      no: '否',
      // Dialog titles
      newCourseTitle: '新课程标题',
      selectStudents: '选择学员',
      courseCategory: '课程类别',
      manage: '管理',
      remove: '移除',
      // Form validation
      courseNameRequired: '课程名称不能为空',
      shortTitleRequired: '简短标题不能为空',
      trainingDaysRequired: '培训天数不能为空',
      courseCodeRequired: '课程编码不能为空',
      categoryRequired: '课程分类不能为空',
      coverRequired: '请上传封面图片',
      languageRequired: '语言不能为空',
      trainerTypeRequired: '培训类型不能为空',
      approvalSettingRequired: '审批设置不能为空',
      validityRequired: '证书有效期不能为空',
      certificateRequired: '证书不能为空',
      bookingTimeRequired: '预约时间设置不能为空',
      restrictionRequired: '缺席次数和冻结时间不能为空',
      // Common labels
      pleaseSelect: '请选择',
      pleaseInput: '请输入',
      // CourseForm specific labels
      addCategory: '添加分类',
      cover: '封面',
      default: '默认',
      customize: '自定义',
      level: '级别',
      ifmsApproval: 'IFMS审批',
      contractorApproval: '承包商审批',
      certificate: '证书',
      exam: '考试',
      prerequisite: '前置条件',
      course: '课程',
      attachment: '附件',
      new: '新建',
      restriction: '限制',
      absent: '缺席',
      absentTime: '次',
      unableToSubscribe: '，无法在',
      feedback: '反馈',
      bookingTimeSetting: '预约时间设置',
      bookingTimeDesc: '在计划开始前24小时预约',
      description: '描述',
      // Placeholders
      pleaseSelectCertificate: '请选择证书',
      pleaseSelectExam: '请选择考试',
      pleaseSelectCourse: '请选择课程',
      // Validity
      monthsUnit: '个月',
      noRestrictions: '0表示无限制',
      // Dialog titles
      template: '模板',
      // Approval workflow
      student: '学员',
      review: '审核',
      end: '结束',
      // Approval descriptions
      noReviewNeeded: '无需审核',
      submitBookingRequest: '提交预约请求',
      trainingAdmin: '培训管理员',
      lineManager: '直属领导',
      eptwAdmin: 'EPTW管理员',
      contractorHolder: '承包商负责人',
      // Category management
      newCategory: '新建分类',
      categoryName: '分类名称',
      categoryParent: '父级分类',
      status: '状态',
      categoryNamePlaceholder: '请输入分类名称',
      selectParentCategory: '请选择父级分类',
      parentCategoryRequired: '请选择父级分类',
      // Certificate selection
      selectCertificate: '选择证书',
      name: '名称',
      time: '时间',
      creationTime: '创建时间',
      startDate: '开始日期',
      endDate: '结束日期',
      pleaseInputName: '请输入名称',
      // Exam selection
      selectOnlineExam: '选择在线考试',
      examName: '考试名称',
      subject: '科目',
      type: '类型',
      examTime: '考试时间',
      assignedNumber: '分配人数',
      creator: '创建者',
      // Prerequisite course selection
      selectPrerequisitesCourse: '选择前置课程'
    },
    class: {
      // Search form labels
      course: '课程',
      type: '类型',
      language: '语言',
      publishStatus: '发布状态',
      status: '状态',
      date: '日期',
      title: '标题',
      start: '开始',
      end: '结束',
      pleaseInputTitle: '请输入标题',
      // Button labels
      search: '搜索',
      reset: '重置',
      add: '新增',
      publish: '发布',
      join: '加入',
      // Table column labels
      courseTitle: '课程标题',
      classTitle: '课堂标题',
      classType: '课堂类型',
      trainer: '讲师',
      classroom: '教室',
      duration: '时长',
      bookingNumber: '报名人数',
      action: '操作',
      // Action buttons
      manage: '管理',
      copy: '复制',
      postpone: '延期',
      cancel: '取消',
      delete: '删除',
      // Dialog titles and content
      cancelClass: '取消课堂',
      confirmCancelClass: '您确认要取消课堂吗',
      postponeClass: '延期课堂',
      confirmPostponeClass: '确认延期课堂',
      confirmPostponeAndMerge: '您确认延期课堂并与 {name} 课堂合并吗',
      mergeSuccessful: '合并成功',
      messageNotification: '消息通知',
      notificationDescription: '如果您打开开关，学员将收到相关通知。',
      newClassTitle: '新课堂标题',
      className: '课堂名称：',
      publishClass: '发布课堂',
      offlineClass: '线下课堂',
      virtualClass: '虚拟课堂',
      confirmPublishSchedule: '您确认发布月度课程表吗',
      publishedSuccessfully: '发布成功',
      classPublishedCannotSelect: '课堂已发布，无法选择',
      withdraw: '撤回',
      withdrawClass: '撤回课堂',
      confirmWithdrawClass: '您确认撤回课堂吗',
      confirmPublishClass: '您确认发布课堂吗',
      withdrawSuccessfully: '撤回成功',
      publishSuccessfully: '发布成功',
      owner: '拥有者',
      startEndTime: '开始结束时间',
      basicInformation: '基本信息',
      bookingManagement: '预订管理',
      checkInOut: '签到/签退',
      studentsManagement: '学员管理',
      feedbackManagement: '反馈管理',
      materialsManagement: '资料管理',
      classRoster: '课堂名册',
      classCode: '课堂编码',
      trainingDays: '培训天数',
      translator: '翻译',
      maximumAttendanceNum: '最大出席人数',
      minimumAttendanceNum: '最小出席人数',
      trainingDescription: '培训描述',
      bookingInformation: '预订信息',
      allowedBookingTime: '允许预订时间',
      bookingRate: '预订率',
      bookingTime: '预订时间',
      prerequisite: '前置条件',
      reviewProcess: '审核流程',
      studentInformation: '学员信息',
      badgeNo: '工牌号',
      position: '岗位',
      fullRefresher: '全新/复训',
      full: '全新',
      refresher: '复训',
      approve: '批准',
      reject: '拒绝',
      approvalPassedSuccessfully: '审批通过成功',
      approvalFailed: '审批失败',
      attachment: '附件',
      bookingOverallProgress: '预订总体进度',
      bookingStatus: '预订状态',
      review: '审核',
      checkInStudentNumber: '签到学员人数',
      checkOutStudentNumber: '签退学员人数',
      attendanceStatus: '出席状态',
      checkIn: '签到',
      checkOut: '签退',
      checkInTime: '签到时间',
      checkOutTime: '签退时间',
      scanQRCode: '扫描二维码',
      studentNumber: '学员人数',
      completionRate: '完成率',
      notPassed: '未通过',
      attendance: '出勤',
      companyName: '公司名称',
      classStatus: '课堂状态',
      planned: '计划',
      actualAttendance: '实际出席',
      noShow: '未出席',
      attendanceRate: '出席率',
      comments: '评论',
      evaluationTime: '评估时间',
      courseEvaluation: '课程评估',
      trainerEvaluation: '讲师评估',
      facilityEvaluation: '设施评估',
      confirm: '确认',
      // Form validation
      courseNameRequired: '课程名称不能为空',
      // Success messages
      cancelSuccessfully: '取消成功',
      // ClassForm specific labels
      startDate: '开始日期',
      pickADate: '选择日期',
      to: '至',
      startTime: '开始时间',
      endTime: '结束时间',
      days: '天',
      pleaseSelectClassroom: '请选择教室',
      maximum: '最大值',
      minimum: '最小值',
      save: '保存',
      // Form validation messages
      courseRequired: '课程不能为空',
      courseTypeRequired: '课程类型不能为空',
      trainerRequired: '讲师不能为空',
      durationRequired: '时长不能为空',
      startDateRequired: '开始日期不能为空',
      student: '学员',
      // AssignClass specific labels (only unique ones, others use common)
      time: '时间'
    },
    internal: {
      // Search form labels
      type: '类型',
      place: '地点',
      company: '公司',
      time: '时间',
      courseTitleEnglish: '课程标题(英文)',
      courseTitleArabic: '课程标题(阿文)',
      // Table column labels
      trainingCode: '培训编码',
      courseTitleEN: '课程标题(英文)',
      courseTitleAR: '课程标题(阿文)',
      startEndDate: '开始结束日期',
      courseDuration: '课程时长',
      studentNumber: '学员人数',
      studentList: '学员列表',
      notes: '备注',
      days: '天',
      // Form labels
      form: {
        trainingCode: '培训编码',
        courseTitleEN: '课程标题(英文)',
        courseTitleAR: '课程标题(阿文)',
        trainingType: '培训类型',
        place: '地点',
        startEndTime: '开始结束时间',
        duration: '时长',
        implementingCompany: '实施公司',
        attachment: '附件',
        comments: '备注',
        studentInformation: '学员信息',
        multipleGroups: '多组',
        enableStudentsByMultipleGroups: '启用多组学员',
        addStudent: '添加学员',
        groupNo: '组号',
        name: '姓名',
        arabicName: '阿拉伯姓名',
        position: '岗位',
        notes: '备注',
        action: '操作',
        remove: '移除',
        start: '开始',
        end: '结束',
        // Validation messages
        courseTitleENRequired: '课程标题(英文)不能为空',
        courseTitleARRequired: '课程标题(阿文)不能为空',
        trainingCodeRequired: '培训编码不能为空',
        trainingTypeRequired: '培训类型不能为空',
        placeRequired: '地点不能为空',
        startEndTimeRequired: '开始和结束时间不能为空',
        durationRequired: '时长不能为空',
        implementingCompanyRequired: '实施公司不能为空'
      }
    },
    external: {
      // Search form labels
      country: '国家',
      costBearer: '费用承担方',
      travelDate: '出行日期',
      returnDate: '返回日期',
      courseTitleEnglish: '课程标题(英文)',
      courseTitleArabic: '课程标题(阿文)',
      startDate: '开始日期',
      endDate: '结束日期',
      // Table column labels
      trainingCode: '培训编码',
      courseTitleEN: '课程标题(英文)',
      courseTitleAR: '课程标题(阿文)',
      receivingCountry: '接收国家',
      adminNo: '管理员编号',
      studentNumber: '学员人数',
      studentList: '学员列表',
      // Form labels
      travelReturn: '出行和返回',
      travel: '出行',
      return: '返回',
      receivingCountryLabel: '接收国家',
      constBearer: '费用承担方',
      attachment: '附件',
      comments: '备注',
      studentInformation: '学员信息',
      multipleGroups: '多组',
      enableStudentsByMultipleGroups: '启用多组学员',
      addStudent: '添加学员',
      groupNo: '组号',
      name: '姓名',
      arabicName: '阿拉伯姓名',
      bocBadgeNo: 'BOC工号',
      position: '岗位',
      department: '部门',
      // Validation messages
      courseTitleENRequired: '课程标题(英文)不能为空',
      courseTitleARRequired: '课程标题(阿文)不能为空',
      trainingCodeRequired: '培训编码不能为空',
      receivingCountryRequired: '接收国家不能为空',
      travelReturnRequired: '出行和返回日期不能为空',
      adminNoRequired: '管理员编号不能为空',
      costBearerRequired: '费用承担方不能为空'
    }
  },
  bpm: {
    toDo: {
      taskTitlePlaceholder: '请输入任务名称',
      categoryPlaceholder: '请选择流程分类',
      process: '所属流程',
      processPlaceholder: '请选择流程定义',
      startTime: '发起时间',
      processName: '流程',
      summary: '摘要',
      initiator: '发起人',
      currentTask: '当前任务',
      taskTime: '任务时间',
      processInstanceId: '流程编号',
      taskId: '任务编号',
      handle: '办理',
      processStatus: '流程状态',
      processStatusApproving: '审批中',
      processStatusPerson: '人',
      processStatusWait: '等',
      processStatusPlaceholder: '请选择流程状态',
      processCategory: '流程分类',
      processStartFailed: '重新发起流程失败，原因：该流程使用业务表单，不支持重新发起',
      processCancelReasonPlaceholder: '请输入取消原因',
      processCancel: '取消流程',
      processCancelReasonRequired: '取消原因不能为空',
      processCancelSuccess: '取消成功'
    },
    processInstance: {
      operationButtonApprove: '批准',
      operationButtonReject: '拒绝',
      operationButtonTransfer: '转移',
      operationButtonDelegate: '委派',
      operationButtonAddSign: '加签',
      operationButtonReturn: '返回',
      operationButtonCopy: '复制',
      processInstanceDetailTitle: '审批详情',
      flowChartTitle: '流程图',
      circulationRecordTitle: '流转记录',
      number: '编号',
      submit: '提交',
      approve: '审批意见',
      approvePlaceholder: '请输入审批意见',
      processNode: '审批节点',
      approver: '审批人',
      status: '审批状态',
      approveSuggestion: '审批建议',
      viewForm: '查看表单',
      costTime: '耗时',
      formDetail: '表单详情',
      approveTitle: '审批',
      approveOpinionTitle: '意见',
      pleaseInput: '请输入',
      approveOpinionRequired: '审批意见不能为空'
    }
  },
  // Certificate template management
  certificate: {
    name: '证书名称',
    status: '状态',
    createTime: '创建时间',
    numberPrefix: '编号前缀',
    template: '证书模板',
    logo: '标志',
    officialSeal: '公章',
    namePlaceholder: '请输入证书名称',
    numberPrefixPlaceholder: '请输入证书编号前缀',
    templateRequired: '证书模板不能为空',
    nameRequired: '证书名称不能为空',
    numberPrefixRequired: '证书编号前缀不能为空',
    logoRequired: '标志不能为空',
    officialSealRequired: '公章不能为空',
    statusRequired: '状态不能为空',
    loadTemplatesFailed: '加载证书模板失败',
    loadDataFailed: '加载证书数据失败',
    templateName: '模板名称',
    templateImage: '模板图片',
    templateNameRequired: '证书模板名称不能为空',
    templateImageRequired: '证书模板图片不能为空',
    preview: '预览',
    previewResult: '预览结果',
    previewError: '预览失败',
    previewFailed: '预览失败，请重试',
    saveTemplateFirst: '请先保存模板后再预览',
    coordinateDesigner: '坐标设计器',
    textElements: '文本元素',
    imageElements: '图片元素',
    certificateName: '证书名称',
    certificateNumber: '证书编号',
    issueDate: '颁发日期',
    userName: '用户姓名',
    logo: 'Logo',
    officialSeal: '公章',
    dragToPosition: '拖拽到指定位置',
    resizeImage: '拖拽边角调整图片大小',
    coordinates: '坐标',
    size: '尺寸',
    width: '宽度',
    height: '高度',
    // Certificate render page
    certificateRender: '证书绘制预览生成',
    basicInfo: '基础信息',
    variables: '变量设置',
    certificateId: '证书ID',
    userId: '用户ID',
    number: '证书编号',
    taskId: '任务ID',
    userNickname: '用户昵称',
    customVariables: '自定义变量',
    addVariable: '添加变量',
    variableKey: '变量名',
    variableValue: '变量值',
    refreshPreview: '刷新预览',
    generateCertificate: '生成证书',
    previewPlaceholder: '选择模板并填写表单后预览证书',
    generateSuccess: '生成成功',
    copyUrl: '复制链接',
    openUrl: '打开链接',
    copySuccess: '复制成功',
    copyFailed: '复制失败',
    // Placeholders
    userIdPlaceholder: '请输入用户ID',
    numberPlaceholder: '请输入证书编号',
    taskIdPlaceholder: '请输入任务ID',
    userNicknamePlaceholder: '可选，若不填且提供userId，后端会自动补充',
    issueDatePlaceholder: '可选，不传则后端默认英文长日期',
    // Validation messages
    certificateIdRequired: '请选择证书',
    selectCertificateFirst: '请先选择证书',
    fillRequiredFields: '请填写所有必填字段',
    generateFailed: '生成失败，请重试'
  }
}
