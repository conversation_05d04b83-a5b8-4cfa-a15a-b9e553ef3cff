<script setup lang="ts">
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import {SkillTagsLibraryApi, SkillTagsLibraryPageReqVO} from '@/api/edp/skilltagslibrary'
import SkillTagsLibraryForm from './SkillTagsLibraryForm.vue'

/** ----- INTERFACE ----- */
/** 三级技能节点 */
interface ThirdLevelNode {
  id: number
  type: string
  deptName: string
  firstSkill: string
  secondSkill: string
  thirdSkill: string
  status: boolean
  updateTime: string
}

/** 二级技能节点 */
interface SecondLevelNode {
  id: string
  type: string
  deptName: string
  firstSkill: string
  secondSkill: string
  children: ThirdLevelNode[]
}

/** 一级技能节点 */
interface FirstLevelNode {
  id: string
  type: string
  deptName: string
  firstSkill: string
  children: SecondLevelNode[]
}

/** 部门节点 */
interface DeptNode {
  id: string
  type: string
  deptId: number
  deptName: string
  children: FirstLevelNode[]
}

/**  ----- SETUP ----- */
defineOptions({ name: 'SkillTagsLibrary' }) // 技能标签库 列表
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const loading = ref(true) // 列表的加载中
const tagList = ref<DeptNode[]>([]) // 列表的数据
const total = ref<number>(0) // 列表的总页数
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中
const isExpandAll = ref(false) // 是否展开，默认全部折叠
const refreshTable = ref(true) // 重新渲染表格状态
const queryParams = reactive<SkillTagsLibraryPageReqVO>({
  pageNo: 1,
  pageSize: 10,
  deptName: '',
  createTime:'',
  updateTime: ''
}) // 定义查询参数

/**  ----- METHODS ----- */
/** 将接口数据转换为表格需要的树形结构 */
function transformData(deptData) {
  const result: DeptNode[] = []

  deptData.forEach((dept) => {
    // 添加部门节点
    const deptNode: DeptNode = {
      id: `dept_${dept.deptId}`,
      type: 'dept',
      deptId: dept.deptId,
      deptName: dept.deptName,
      children: []
    }

    // 处理skills对象结构: {"1": {"1": [...], "2": [...]}, "2": {"4": [...], "5": [...]}}
    if (dept.skills) {
      Object.entries(dept.skills).forEach(([firstSkillId, secondSkillsObj]) => {
        // 添加一级标签节点
        const firstNode: FirstLevelNode = {
          id: `first_${dept.deptId}_${firstSkillId}`,
          type: 'first',
          deptName: dept.deptName,
          firstSkill: `Level ${firstSkillId} Skills`,
          children: []
        }

        Object.entries(secondSkillsObj).forEach(([secondSkillId, thirdSkills]) => {
          // 添加二级标签节点
          const secondNode: SecondLevelNode = {
            id: `second_${dept.deptId}_${firstSkillId}_${secondSkillId}`,
            type: 'second',
            deptName: dept.deptName,
            firstSkill: `Level ${firstSkillId} Skills`,
            secondSkill: `Category ${secondSkillId}`,
            children: []
          }

          thirdSkills?.forEach((thirdSkill) => {
            // 添加三级标签节点
            secondNode.children.push({
              id: thirdSkill.id,
              type: 'third',
              deptName: dept.deptName,
              firstSkill: `Level ${firstSkillId} Skills`,
              secondSkill: `Category ${secondSkillId}`,
              thirdSkill: thirdSkill.name,
              status: thirdSkill.status,
              updateTime: thirdSkill.updateTime
            })
          })

          firstNode.children.push(secondNode)
        })

        deptNode.children.push(firstNode)
      })
    }

    result.push(deptNode)
  })

  return result
}

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const res = await SkillTagsLibraryApi.getSkillTagsLibraryList(queryParams)
    console.log('通过API获取到的岗位标签数据:', res)

    // 存储树形结构
    tagList.value = transformData(res.list)
    total.value = res.total
    console.log('保存的数据', tagList.value)
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 启用/禁用操作 */
const handleToggleStatus = async (id: number, status: boolean) => {
  try {
    loading.value = true
    await SkillTagsLibraryApi.toggleSkillLibraryStatus(id, status)
    message.success(status ? 'Enable Success' : 'Disable Success')
  } catch (error) {
    console.log('切换状态时发生错误:', error)
    message.error('Operation Failed')
    // 操作失败时重新获取列表以恢复原始状态
    await getList()
  } finally {
    loading.value = false
  }
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, rowData?: any) => {
  formRef.value.open(type, rowData)
}

/** 删除按钮操作 */
const handleDelete = async (id?: number) => {
  if (!id) return

  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await SkillTagsLibraryApi.deleteSkillTagsLibrary(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await SkillTagsLibraryApi.exportSkillTagsLibrary()
    download.excel(data, '技能标签库.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>

<template>
  <ContentWrap>
    <!-- 工作栏 -->
    <el-form class="-mb-15px" :model="queryParams" ref="queryFormRef" :inline="true">
      <!--部门名称筛选-->
      <el-form-item label="Department" prop="deptName">
        <el-input
          v-model="queryParams.deptName"
          placeholder="please input department name"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>

      <!--时间选择器-->
      <el-form-item label="Edit Time" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="Start Date"
          end-placeholder="End Date"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-220px"
        />
      </el-form-item>

      <!--按钮组-->
      <el-form-item>
        <!--搜索按钮-->
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> Search </el-button>

        <!--重置按钮-->
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> Reset </el-button>

        <!--增加标签按钮-->
        <el-button type="primary" plain @click="openForm('create')">
          <Icon icon="ep:plus" class="mr-5px" /> Add
        </el-button>

        <!--导出按钮-->
        <el-button type="success" plain @click="handleExport" :loading="exportLoading">
          <Icon icon="ep:download" class="mr-5px" /> Export
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table
      v-if="refreshTable"
      v-loading="loading"
      :data="tagList"
      :stripe="false"
      :show-overflow-tooltip="true"
      row-key="id"
      :default-expand-all="isExpandAll"
    >
      <template #empty>
        <el-empty description="No data available" />
      </template>

      <!-- 部门 -->
      <el-table-column label="Department" align="center" prop="deptName">
        <template #default="scope">
          <span v-if="scope.row.type === 'dept'">
            <b>{{ scope.row.deptName || 'Unknown Department' }}</b>
          </span>
          <span v-else>
            {{ scope.row.deptName || 'Unknown Department' }}
          </span>
        </template>
      </el-table-column>

      <!-- 一级技能标签 -->
      <el-table-column label="Level 1 Skill" align="center">
        <template #default="scope">
          <span v-if="scope.row.type === 'first'">
            <b>{{ scope.row.firstSkill || '-' }}</b>
          </span>
          <span v-else-if="scope.row.type === 'second' || scope.row.type === 'third'">
            {{ scope.row.firstSkill || '-' }}
          </span>
          <span v-else>-</span>
        </template>
      </el-table-column>

      <!-- 二级技能标签 -->
      <el-table-column label="Level 2 Skill" align="center">
        <template #default="scope">
          <span v-if="scope.row.type === 'second'">
            <b>{{ scope.row.secondSkill || '-' }}</b>
          </span>
          <span v-else-if="scope.row.type === 'third'">
            {{ scope.row.secondSkill || '-' }}
          </span>
          <span v-else>-</span>
        </template>
      </el-table-column>

      <!-- 三级技能标签 -->
      <el-table-column label="Level 3 Skill" align="center">
        <template #default="scope">
          <el-tag
            v-if="scope.row.type === 'third' && scope.row.thirdSkill"
            size="small"
            type="info"
            class="mx-1 my-1"
          >
            {{ scope.row.thirdSkill }}
          </el-tag>
          <span v-else>-</span>
        </template>
      </el-table-column>

      <!-- 状态 -->
      <el-table-column label="Status" align="center">
        <template #default="scope">
          <el-switch
            v-if="scope.row.type === 'third'"
            v-model="scope.row.status"
            active-color="#13ce66"
            inactive-color="#ff4949"
            @change="(val: boolean) => handleToggleStatus(scope.row.id, val)"
          />
          <span v-else>-</span>
        </template>
      </el-table-column>

      <!-- 编辑时间 -->
      <el-table-column
        label="Edit Time"
        align="center"
        prop="updateTime"
        :formatter="dateFormatter"
        width="180px"
      />

      <!-- Actions -->
      <el-table-column label="Actions" align="center" min-width="120px">
        <template #default="scope">
          <!-- 编辑 - 对三级标签显示 -->
          <el-tooltip
            v-if="scope.row.type === 'third'"
            content="Edit"
            placement="top"
            effect="light"
          >
            <el-button link type="primary" @click="openForm('update', scope.row)">
              <Icon icon="ep:edit" />
            </el-button>
          </el-tooltip>

          <!-- 添加 - 对部门、一级和二级标签显示 -->
          <el-tooltip
            v-if="
              scope.row.type === 'dept' || scope.row.type === 'first' || scope.row.type === 'second'
            "
            content="Add"
            placement="top"
            effect="light"
          >
            <el-button link type="primary" @click="openForm('create', scope.row)">
              <Icon icon="ep:plus" />
            </el-button>
          </el-tooltip>

          <!-- 删除 - 对三级标签显示 -->
          <el-tooltip
            v-if="scope.row.type === 'third'"
            content="Delete"
            placement="top"
            effect="light"
          >
            <el-button link type="danger" @click="handleDelete(scope.row.id)">
              <Icon icon="ep:delete" />
            </el-button>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <SkillTagsLibraryForm ref="formRef" @success="getList" />
</template>