<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { departmentReportApi } from '@/api/edp/report'

/** ----- SETUP ----- */
const list = ref()
const activeSection = ref('All Sections')
const contentSectionFilter = ref('All Sections')
const skipSectionFilter = ref('All Sections')
const currentPage = ref(1)
const pageSize = 10
const totalPages = 3

/** ----- FUNCTIONS ----- */
/** 获取部门学习报告 */
const getList = async () => {
  try {
    const queryParams = {
      year: 2025
    }

    list.value = await departmentReportApi.getDepartmentReport(queryParams)
    console.log('Fetched department report:', list.value)
    console.log('baseInfo:', list.value?.baseInfo)
    console.log('sectStudyStatistics:', list.value?.sectStudyStatistics)
  } catch (error) {
    console.error('Error fetching department report:', error)
  }
}

// 部门核心指标数据
const sectionKPIData = ref({
  'All Sections': { completedCourses: 1420, avgtime: '82.5h', coverage: '88.2%' },
  'Data Center': { completedCourses: 180, avgtime: '78.2h', coverage: '85.0%' },
  'IT Business Management': { completedCourses: 156, avgtime: '88.1h', coverage: '90.5%' },
  'IT Support Team': { completedCourses: 224, avgtime: '80.3h', coverage: '87.2%' },
  'Infrastructure Management': { completedCourses: 160, avgtime: '76.8h', coverage: '84.7%' },
  'SAP Project': { completedCourses: 132, avgtime: '90.4h', coverage: '92.1%' },
  'SAP Support': { completedCourses: 154, avgtime: '85.9h', coverage: '89.8%' },
  'IT Applications Management': { completedCourses: 170, avgtime: '75.2h', coverage: '83.5%' },
  'IT EPC Project': { completedCourses: 100, avgtime: '72.6h', coverage: '81.0%' },
  'IOF Project': { completedCourses: 80, avgtime: '70.0h', coverage: '80.0%' },
  'IT Systems-TBC': { completedCourses: 50, avgtime: '68.0h', coverage: '78.0%' },
  'Telecom & Network-TBC': { completedCourses: 50, avgtime: '67.5h', coverage: '77.5%' }
})

// 计算部门列表
const sections = computed(() => {
  if (!list.value?.sectStudyStatistics) {
    console.log('No sectStudyStatistics data, returning empty array')
    return []
  }

  const sectStats = list.value.sectStudyStatistics

  // sectStudyStatistics是对象，需要转换为数组
  const sectionsFromApi = Object.entries(sectStats)
    .filter(([key]) => key !== 'All Sections')
    .map(([key, value]: [string, any]) => ({
      name: key,
      count: value.employeeCount || 0
    }))

  // 获取All Sections的数据，如果没有就计算总数
  const allSectionsData = sectStats['All Sections']
  const totalCount =
    allSectionsData?.employeeCount || sectionsFromApi.reduce((sum, item) => sum + item.count, 0)
  const allSections = { name: 'All Sections', count: totalCount }

  console.log('Final sections:', [allSections, ...sectionsFromApi])
  return [allSections, ...sectionsFromApi]
})

// 技能完成率数据
const skillCompletionData = ref([
  { name: 'Python', rate: 92 },
  { name: 'Excel', rate: 88 },
  { name: 'Cloud Infrastructure', rate: 85 },
  { name: 'SAP Expertise', rate: 83 },
  { name: 'Networking', rate: 81 },
  { name: 'Data Management', rate: 78 },
  { name: 'Project Management', rate: 75 },
  { name: 'Cybersecurity', rate: 72 },
  { name: 'AI & ML', rate: 68 },
  { name: 'DevOps', rate: 65 },
  { name: 'Leadership', rate: 60 },
  { name: 'Communication', rate: 58 },
  { name: 'Teamwork', rate: 55 },
  { name: 'Problem Solving', rate: 52 },
  { name: 'First Aid', rate: 50 },
  { name: 'Fire Safety', rate: 48 },
  { name: 'Risk Assessment', rate: 45 },
  { name: 'Incident Reporting', rate: 42 },
  { name: 'Ergonomics', rate: 40 },
  { name: 'Environmental Protection', rate: 38 }
])

// 课程数据
const courseData = ref([
  { name: 'Python Basics', completions: 120, rating: 4.8, type: 'Online course' },
  { name: 'Excel Advanced', completions: 110, rating: 4.7, type: 'Online course' },
  { name: 'Cloud Fundamentals', completions: 105, rating: 4.6, type: 'MLC Training' },
  { name: 'SAP Overview', completions: 98, rating: 4.5, type: 'Online course' },
  { name: 'Cybersecurity Essentials', completions: 90, rating: 4.4, type: 'Knowledge Doc' },
  { name: 'Project Management', completions: 85, rating: 4.3, type: 'Online course' },
  { name: 'AI Introduction', completions: 80, rating: 4.2, type: 'MLC Training' },
  { name: 'Leadership Skills', completions: 75, rating: 4.1, type: 'Online course' },
  { name: 'First Aid', completions: 70, rating: 4.0, type: 'Knowledge Doc' },
  { name: 'Communication', completions: 65, rating: 3.9, type: 'Online course' }
])

// 跳过技能数据
const skipSkillsData = ref([
  { skill: 'Cybersecurity', skipCount: 18, skipEmployees: 12 },
  { skill: 'AI & ML', skipCount: 15, skipEmployees: 10 },
  { skill: 'Project Management', skipCount: 12, skipEmployees: 8 },
  { skill: 'First Aid', skipCount: 10, skipEmployees: 7 },
  { skill: 'Cloud Infrastructure', skipCount: 9, skipEmployees: 6 },
  { skill: 'Leadership', skipCount: 8, skipEmployees: 5 },
  { skill: 'Communication', skipCount: 7, skipEmployees: 4 },
  { skill: 'SAP Expertise', skipCount: 6, skipEmployees: 3 },
  { skill: 'DevOps', skipCount: 5, skipEmployees: 3 },
  { skill: 'Data Management', skipCount: 4, skipEmployees: 2 },
  { skill: 'Teamwork', skipCount: 4, skipEmployees: 2 },
  { skill: 'Problem Solving', skipCount: 3, skipEmployees: 2 },
  { skill: 'Networking', skipCount: 3, skipEmployees: 2 },
  { skill: 'Fire Safety', skipCount: 3, skipEmployees: 2 },
  { skill: 'Risk Assessment', skipCount: 3, skipEmployees: 2 },
  { skill: 'Incident Reporting', skipCount: 2, skipEmployees: 1 },
  { skill: 'Ergonomics', skipCount: 2, skipEmployees: 1 },
  { skill: 'Environmental Protection', skipCount: 2, skipEmployees: 1 },
  { skill: 'Python', skipCount: 2, skipEmployees: 1 },
  { skill: 'Excel', skipCount: 1, skipEmployees: 1 },
  { skill: 'Time Management', skipCount: 1, skipEmployees: 1 },
  { skill: 'Conflict Resolution', skipCount: 1, skipEmployees: 1 },
  { skill: 'Critical Thinking', skipCount: 1, skipEmployees: 1 },
  { skill: 'Work Ethic', skipCount: 1, skipEmployees: 1 },
  { skill: 'Safety Awareness', skipCount: 1, skipEmployees: 1 },
  { skill: 'Emergency Response', skipCount: 1, skipEmployees: 1 },
  { skill: 'PPE Usage', skipCount: 1, skipEmployees: 1 },
  { skill: 'Hazard Identification', skipCount: 1, skipEmployees: 1 },
  { skill: 'Adaptability', skipCount: 1, skipEmployees: 1 },
  { skill: 'Creativity', skipCount: 1, skipEmployees: 1 }
])

// 计算属性
const currentKPI = computed(() => {
  if (!list.value?.sectStudyStatistics) {
    return sectionKPIData.value[activeSection.value] || sectionKPIData.value['All Sections']
  }

  const sectData = list.value.sectStudyStatistics[activeSection.value]
  if (!sectData) {
    return sectionKPIData.value['All Sections']
  }

  return {
    completedCourses: sectData.completedCourseCount || 0,
    avgStudyTime: sectData.avgStudyTime || '0h',
    avgSkillCoverage: sectData.avgSkillCoverage || '0%'
  }
})

const topSkills = computed(() => {
  return [...skillCompletionData.value].sort((a, b) => b.rate - a.rate).slice(0, 10)
})

const bottomSkills = computed(() => {
  return [...skillCompletionData.value].sort((a, b) => a.rate - b.rate).slice(0, 10)
})

const paginatedSkipData = computed(() => {
  const sorted = [...skipSkillsData.value].sort((a, b) => b.skipCount - a.skipCount)
  const startIndex = (currentPage.value - 1) * pageSize
  return sorted.slice(startIndex, startIndex + pageSize)
})

// 方法
const selectSection = (section: string) => {
  activeSection.value = section
}

const previousPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--
  }
}

const nextPage = () => {
  if (currentPage.value < totalPages) {
    currentPage.value++
  }
}

const onSkipSectionChange = () => {
  currentPage.value = 1
}

const sectionOptions = computed(() => {
  const options = sections.value.map((s) => s.name)
  return options.length > 0 ? options : ['All Sections']
})

/** LIFE CYCLE HOOKS */
onMounted(async () => {
  await getList()
})
</script>

<template>
  <div class="min-h-screen">
    <div class="rounded-md overflow-hidden">
      <!-- Header -->
      <header
        class="bg-gradient-to-b from-[#017b3d] to-white text-white px-10 py-20 pb-30 text-center overflow-hidden"
      >
        <div class="relative z-10 max-w-4xl mx-auto">
          <h1 class="text-4xl font-bold mb-2 tracking-tight">
            {{ list?.baseInfo.deptName || '-' }}
          </h1>

          <div
            class="inline-flex items-center gap-2 bg-white/20 rounded-xl px-6 py-3 mt-4 backdrop-blur"
          >
            <Icon icon="ep:calendar" :size="20" class="text-white" />
            <span class="text-lg font-medium">Report Period: 2024-2025</span>
          </div>

          <!-- Department Info -->
          <div
            class="flex flex-wrap justify-center gap-4 mt-6 bg-white/20 rounded-2xl p-5 backdrop-blur border border-white/15 max-w-4xl mx-auto"
          >
            <div class="flex flex-col items-center flex-1 min-w-0 px-4">
              <div class="w-15 h-15 bg-white/15 rounded-full flex items-center justify-center mb-3">
                <Icon icon="ep:office-building" :size="24" class="text-white" />
              </div>
              <div class="text-2xl font-bold">{{ list?.baseInfo.deptShortName || '-' }}</div>
              <div class="text-sm">Department</div>
            </div>

            <div class="flex flex-col items-center flex-1 min-w-0 px-4">
              <div class="w-15 h-15 bg-white/15 rounded-full flex items-center justify-center mb-3">
                <Icon icon="ep:user" :size="24" class="text-white" />
              </div>
              <div class="text-2xl font-bold">{{ list?.baseInfo.employeeCount || '-' }}</div>
              <div class="text-sm">Employees</div>
            </div>

            <div class="flex flex-col items-center flex-1 min-w-0 px-4">
              <div class="w-15 h-15 bg-white/15 rounded-full flex items-center justify-center mb-3">
                <Icon icon="ep:briefcase" :size="24" class="text-white" />
              </div>
              <div class="text-2xl font-bold">{{ list?.baseInfo.positionCount || '-' }}</div>
              <div class="text-sm">Positions</div>
            </div>

            <div class="flex flex-col items-center flex-1 min-w-0 px-4">
              <div class="w-15 h-15 bg-white/15 rounded-full flex items-center justify-center mb-3">
                <Icon icon="ep:operation" :size="24" class="text-white" />
              </div>
              <div class="text-2xl font-bold">{{ list?.baseInfo.sectionCount || '-' }}</div>
              <div class="text-sm">Sections</div>
            </div>
          </div>
        </div>
      </header>

      <!--数据内容区域-->
      <div class="md:px-6">
        <!-- Department Sections -->
        <div class="flex items-center gap-4 text-[#017b3d] my-6">
          <div class="w-10 h-10 bg-[#017b3d] rounded-full flex items-center justify-center">
            <Icon icon="ep:operation" class="w-5 h-5 text-white" />
          </div>
          <h2 class="text-2xl font-bold"> Department Sections </h2>
        </div>

        <!-- KPI Cards -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-5 mb-8">
          <!--完成课程数量-->
          <el-card class="text-center relative overflow-hidden pt-1" shadow="hover">
            <div
                class="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#017b3d] to-green-500"
            ></div>
            <div
                class="w-18 h-18 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-5"
            >
              <Icon icon="ep:reading" :size="28" class="text-[#017b3d]" />
            </div>
            <div class="text-3xl font-bold text-[#017b3d] mb-2">{{
                currentKPI.completedCourses
              }}</div>
            <div class="text-gray-600 font-medium">Completed Courses</div>
          </el-card>

          <!--平均学习时长-->
          <el-card class="text-center relative overflow-hidden pt-1" shadow="hover">
            <div
                class="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#017b3d] to-green-500"
            ></div>
            <div
                class="w-18 h-18 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-5"
            >
              <Icon icon="ep:timer" :size="28" class="text-[#017b3d]" />
            </div>
            <div class="text-3xl font-bold text-[#017b3d] mb-2"> {{ currentKPI.avgStudyTime }} </div>
            <div class="text-gray-600 font-medium">Avg. Learning Hours (per employee)</div>
          </el-card>

          <!--平均技能覆盖率-->
          <el-card class="text-center relative overflow-hidden pt-1" shadow="hover">
            <div
                class="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#017b3d] to-green-500"
            ></div>
            <div
                class="w-18 h-18 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-5"
            >
              <Icon icon="ep:circle-check" :size="28" class="text-[#017b3d]" />
            </div>
            <div class="text-3xl font-bold text-[#017b3d] mb-2">{{
                currentKPI.avgSkillCoverage
              }}</div>
            <div class="text-gray-600 font-medium">Avg. Skill Coverage (per employee)</div>
          </el-card>
        </div>

        <!-- Section Selector -->
        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4 mb-10">
          <el-card
              v-for="section in sections"
              :key="section.name"
              @click="selectSection(section.name)"
              :class="[
            'text-center cursor-pointer transition-all duration-300 hover:shadow-lg relative overflow-hidden min-h-[100px] flex flex-col justify-center',
            activeSection === section.name
              ? 'bg-gradient-to-br from-[#017b3d] to-green-500 !text-white border-[#017b3d] shadow-none hover:shadow-none'
              : 'border-green-200 hover:border-[#017b3d]'
          ]"
              shadow="never"
          >
            <div class="font-semibold text-base mb-2">{{ section.name }}</div>
            <div class="flex gap-2 items-center justify-center text-sm opacity-80">
              <Icon icon="ep:user" :size="16" />
              <p>{{ section.count }} employees</p>
            </div>
          </el-card>
        </div>

        <!-- Skills Analysis -->
        <div class="flex items-center gap-4 text-[#017b3d] my-6">
          <div class="w-10 h-10 bg-[#017b3d] rounded-full flex items-center justify-center">
            <Icon icon="ep:data-analysis" class="w-5 h-5 text-white" />
          </div>
          <h2 class="text-2xl font-bold"> Top & Bottom 10 Learning Content Completion Rate (by Skill) </h2>
        </div>

        <el-card class="mb-8" shadow="none">
          <div class="flex justify-between items-center mb-6 flex-wrap gap-4">
            <h3 class="flex items-center gap-3 text-xl font-bold text-[#017b3d]">
              <Icon icon="ep:data-analysis" :size="20" class="text-[#017b3d]" />
              Top & Bottom 10 Learning Content Completion Rate (by Skill)
            </h3>
            <div class="flex items-center gap-2">
              <span class="text-[#017b3d] font-semibold">Section</span>
              <el-select v-model="activeSection" class="!w-80">
                <el-option
                    v-for="option in sectionOptions"
                    :key="option"
                    :label="option"
                    :value="option"
                />
              </el-select>
            </div>
          </div>

          <div class="grid md:grid-cols-2 gap-10">
            <!-- Top 10 Skills -->
            <div>
              <h4 class="text-center font-bold text-[#017b3d] mb-4">Top 10 Skills</h4>
              <div class="space-y-3">
                <div
                    v-for="skill in topSkills"
                    :key="skill.name"
                    class="flex justify-between items-center"
                >
                  <span class="font-medium text-gray-700">{{ skill.name }}</span>
                  <div class="flex items-center gap-2 flex-1 ml-4">
                    <div class="flex-1 bg-gray-200 rounded-full h-3">
                      <div
                          class="bg-gradient-to-r from-[#017b3d] to-green-500 h-3 rounded-full transition-all duration-1000"
                          :style="{ width: `${skill.rate}%` }"
                      ></div>
                    </div>
                    <span class="text-sm font-bold text-[#017b3d] min-w-[3rem]"
                    >{{ skill.rate }}%</span
                    >
                  </div>
                </div>
              </div>
            </div>

            <!-- Bottom 10 Skills -->
            <div>
              <h4 class="text-center font-bold text-red-500 mb-4">Bottom 10 Skills</h4>
              <div class="space-y-3">
                <div
                    v-for="skill in bottomSkills"
                    :key="skill.name"
                    class="flex justify-between items-center"
                >
                  <span class="font-medium text-gray-700">{{ skill.name }}</span>
                  <div class="flex items-center gap-2 flex-1 ml-4">
                    <div class="flex-1 bg-gray-200 rounded-full h-3">
                      <div
                          class="bg-gradient-to-r from-red-500 to-red-400 h-3 rounded-full transition-all duration-1000"
                          :style="{ width: `${skill.rate}%` }"
                      ></div>
                    </div>
                    <span class="text-sm font-bold text-red-500 min-w-[3rem]">{{ skill.rate }}%</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div
              class="bg-gradient-to-r from-green-50 to-green-100 border-l-4 border-[#017b3d] p-4 rounded-r-xl mt-6"
          >
            <div class="flex items-start gap-3">
              <Icon icon="ep:opportunity" :size="18" class="text-[#017b3d]" />
              <div>
                <strong class="text-[#017b3d]">Insight:</strong>
                <span class="text-green-800">
                The chart shows the top 10 and bottom 10 learning content completion rates by skill
                for the selected section.</span
                >
              </div>
            </div>
          </div>
        </el-card>

        <!-- Learning Content Distribution -->
        <div class="flex items-center gap-4 text-[#017b3d] my-6">
          <div class="w-10 h-10 bg-[#017b3d] rounded-full flex items-center justify-center">
            <Icon icon="ep:data-analysis" class="w-5 h-5 text-white" />
          </div>
          <h2 class="text-2xl font-bold"> Learning Content Distribution </h2>
        </div>

        <el-card class="mb-8" shadow="none">
          <div class="flex justify-between items-center mb-6 flex-wrap gap-4">
            <h3 class="flex items-center gap-3 text-xl font-bold text-[#017b3d]">
              <Icon icon="ep:menu" :size="20" class="text-[#017b3d]" />
              Top 10 Popular Learning Content
            </h3>
            <div class="flex items-center gap-2">
              <span class="text-[#017b3d] font-semibold">Section</span>
              <el-select v-model="activeSection" class="!w-80">
                <el-option
                    v-for="option in sectionOptions"
                    :key="option"
                    :label="option"
                    :value="option"
                />
              </el-select>
            </div>
          </div>

          <div class="overflow-x-auto">
            <el-table :data="courseData" class="w-full">
              <el-table-column prop="name" label="Course Name" />
              <el-table-column prop="completions" label="Completions" width="120" />
              <el-table-column prop="rating" label="Rating" width="100" />
              <el-table-column prop="type" label="Type" width="150" />
            </el-table>
          </div>
        </el-card>

        <!-- Skip Content Analysis -->
        <div class="flex items-center gap-4 text-[#017b3d] my-6">
          <div class="w-10 h-10 bg-[#017b3d] rounded-full flex items-center justify-center">
            <Icon icon="ep:data-analysis" class="w-5 h-5 text-white" />
          </div>
          <h2 class="text-2xl font-bold"> Skip Content Analysis </h2>
        </div>

        <el-card class="mb-8" shadow="none">
          <div class="flex justify-between items-center mb-6 flex-wrap gap-4">
            <h3 class="flex items-center gap-3 text-xl font-bold text-[#017b3d]">
              <Icon icon="ep:list" :size="20" class="text-[#017b3d]" />
              Skip Counts for Learning Content (by Skill)
            </h3>
            <div class="flex items-center gap-2">
              <span class="text-[#017b3d] font-semibold">Section</span>
              <el-select v-model="activeSection" @change="onSkipSectionChange" class="!w-80">
                <el-option
                    v-for="option in sectionOptions"
                    :key="option"
                    :label="option"
                    :value="option"
                />
              </el-select>
            </div>
          </div>

          <div class="overflow-x-auto">
            <el-table :data="paginatedSkipData" class="w-full mb-6">
              <el-table-column prop="skill" label="Skill Name" />
              <el-table-column prop="skipCount" label="Skipped Content" width="150" />
              <el-table-column prop="skipEmployees" label="Employees Skipped" width="180" />
            </el-table>

            <!-- Pagination -->
            <div class="flex justify-center items-center gap-4 mt-5">
              <button
                  @click="previousPage"
                  :disabled="currentPage === 1"
                  :class="[
                'px-4 py-2 rounded border transition-all',
                currentPage === 1
                  ? 'bg-gray-100 border-gray-300 text-gray-400 cursor-not-allowed'
                  : 'bg-white border-gray-300 text-gray-700 hover:bg-green-50 hover:border-green-400 hover:text-[#017b3d]'
              ]"
              >
                Previous
              </button>
              <span class="text-gray-600">Page {{ currentPage }} of {{ totalPages }}</span>
              <button
                  @click="nextPage"
                  :disabled="currentPage === totalPages"
                  :class="[
                'px-4 py-2 rounded border transition-all',
                currentPage === totalPages
                  ? 'bg-gray-100 border-gray-300 text-gray-400 cursor-not-allowed'
                  : 'bg-white border-gray-300 text-gray-700 hover:bg-green-50 hover:border-green-400 hover:text-[#017b3d]'
              ]"
              >
                Next
              </button>
            </div>
          </div>
        </el-card>
      </div>


    </div>
  </div>
</template>

<style scoped>
/* Element Plus table customization */
:deep(.el-table) {
  border-radius: 12px;
  overflow: hidden;
}

:deep(.el-table th) {
  background-color: #f5f8fd;
  color: #1a3a5f;
  font-weight: 600;
}

:deep(.el-table tr:hover > td) {
  background-color: #f9fbfe;
}
</style>