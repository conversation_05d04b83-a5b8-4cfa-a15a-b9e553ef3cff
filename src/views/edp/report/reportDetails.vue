<script setup lang="ts">
import { ref, onMounted, nextTick, watch } from 'vue'
import { useUserStore } from '@/store/modules/user'
import {
  employeesReportApi,
  PersonBaseInfoVO,
  CoreLearningMetricsVO,
  PersonalCompletionStatusVO,
  PersonSkillCoverageVO,
  ErrorRateTop5VO,
  ContentStarTop5VO,
  PersonAnalysisStudyTimeVO,
  AIGCVO
} from '@/api/edp/report'
import * as echarts from 'echarts'
import { Icon } from '@/components/Icon'

defineOptions({ name: 'ReportDetails' })

/** ----- SETUP ----- */
const route = useRoute()
const userStore = useUserStore()

const queryParams = ref({
  userId: route.query.userId,
  year: 2025
}) // 查询参数
const baseInfo = ref<PersonBaseInfoVO>() // 用户基本信息
const coreLearningMetrics = ref<CoreLearningMetricsVO>() // 核心学习指标
const learningProgressAnalysis = ref<PersonalCompletionStatusVO>() // 学习进度分析
const learningContentCompletionRate = ref<PersonSkillCoverageVO>() // 学习内容完成率
const identifySkillWeaknesses = ref<ErrorRateTop5VO[]>([]) // 短板分布分析
const discoverLearningInterests = ref<ContentStarTop5VO[]>([]) // 发现学习兴趣
const learningTimeAnalysis = ref<PersonAnalysisStudyTimeVO>() // 学习时长分析
const aiContent = ref<AIGCVO>()
const loading = ref(true) // 加载状态
const aiAnalysisLoading = ref(false) // AI分析加载状态
const selectedCategory = ref('hard')
const skillsChart = ref<HTMLDivElement>()
const weaknessChart = ref<HTMLDivElement>()
const ratingChart = ref<HTMLDivElement>()
const timeChart = ref<HTMLDivElement>()
let myChart: echarts.ECharts | null = null

/** ----- FUNCTIONS ----- */
/** 获取用户基本信息 */
const getBaseInfo = async () => {
  try {
    baseInfo.value = await employeesReportApi.getPersonBaseInfo(queryParams.value)
  } catch (error) {
    console.log('获取用户基本信息失败:', error)
  }
}

/** 获取核心学习指标 */
const getCoreMetrics = async () => {
  try {
    coreLearningMetrics.value = await employeesReportApi.getCoreLearningMetrics(queryParams.value)
  } catch (error) {
    console.log('获取核心学习指标失败:', error)
  }
}

/** 获取学习要求完成情况 */
const getLearningProgressAnalysis = async () => {
  try {
    learningProgressAnalysis.value = await employeesReportApi.getPersonalCompletionStatus(
      queryParams.value
    )
    console.log('学习要求完成情况:', learningProgressAnalysis.value)
  } catch (error) {
    console.log('获取学习要求完成情况失败:', error)
  }
}

/** 获取技能掌握分布 **/
const getLearningContentCompletionRate = async () => {
  try {
    learningContentCompletionRate.value = await employeesReportApi.getSkillCoverage(
      queryParams.value
    )
    console.log('技能掌握分布:', learningContentCompletionRate.value)
  } catch (error) {
    console.log('获取技能掌握分布失败:', error)
  }
}

/** 获取短板分布分析 - 岗位技能错误率 */
const getIdentifySkillWeaknesses = async () => {
  try {
    identifySkillWeaknesses.value = await employeesReportApi.getErrorRateTop5(queryParams.value)
    console.log('岗位技能错误率:', learningContentCompletionRate.value)
  } catch (error) {
    console.log('获取岗位技能错误率失败:', error)
  }
}

/** 获取发现学习兴趣 **/
const getDiscoverLearningInterests = async () => {
  try {
    discoverLearningInterests.value = await employeesReportApi.getContentStarTop5(queryParams.value)
    console.log('发现学习兴趣:', discoverLearningInterests.value)
  } catch (error) {
    console.log('获取发现学习兴趣失败:', error)
  }
}

/** 获取学习时间分布分析 */
const getLearningTimeAnalysis = async () => {
  try {
    learningTimeAnalysis.value = await employeesReportApi.getStudyTimeDistribution(
      queryParams.value
    )
    console.log('发现学习兴趣:', learningTimeAnalysis.value)
  } catch (error) {
    console.log('获取发现学习兴趣失败:', error)
  }
}

/** 获取报告中AI生成的内容 */
const getAIReportContent = async () => {
  if (aiAnalysisLoading.value) return

  try {
    aiAnalysisLoading.value = true
    aiContent.value = await employeesReportApi.getAiContent(queryParams.value)
    console.log('AI生成内容:', aiContent.value)
  } catch (error) {
    console.log('获取AI生成内容失败:', error)
  } finally {
    aiAnalysisLoading.value = false
  }
}

/** 处理AI Analysis按钮点击 */
const handleAIAnalysis = () => {
  getAIReportContent()
}

// 从真实API数据中获取特定类别的技能数据
function getSkillDataForCategory(category: string) {
  if (!learningContentCompletionRate.value) return { skills: [], data: [] }

  let categoryData: Record<string, number> = {}

  switch (category) {
    case 'hard':
      categoryData = learningContentCompletionRate.value.hardSkillCoverage || {}
      break
    case 'soft':
      categoryData = learningContentCompletionRate.value.softSkillCoverage || {}
      break
    case 'hse':
      categoryData = learningContentCompletionRate.value.hseCoverage || {}
      break
    case 'all':
    default:
      categoryData = {
        ...learningContentCompletionRate.value.hardSkillCoverage,
        ...learningContentCompletionRate.value.softSkillCoverage,
        ...learningContentCompletionRate.value.hseCoverage
      }
      break
  }

  const skills = Object.keys(categoryData)
  const data = Object.values(categoryData).map((value) => Math.round(value * 100))

  return { skills, data }
}

// 从真实数据中获取表现最好和最差的技能
function getTopBottomSkills() {
  if (!learningContentCompletionRate.value) return { topSkill: null, bottomSkill: null }

  const allSkills = {
    ...learningContentCompletionRate.value.hardSkillCoverage,
    ...learningContentCompletionRate.value.softSkillCoverage,
    ...learningContentCompletionRate.value.hseCoverage
  }

  const skillEntries = Object.entries(allSkills)
  if (skillEntries.length === 0) return { topSkill: null, bottomSkill: null }

  const sortedSkills = skillEntries.sort((a, b) => b[1] - a[1])
  const topSkill = { name: sortedSkills[0][0], rate: Math.round(sortedSkills[0][1] * 100) }
  const bottomSkill = {
    name: sortedSkills[sortedSkills.length - 1][0],
    rate: Math.round(sortedSkills[sortedSkills.length - 1][1] * 100)
  }

  return { topSkill, bottomSkill }
}

// 初始化图表
function initializeCharts() {
  if (!skillsChart.value || !weaknessChart.value || !ratingChart.value || !timeChart.value) return

  // 雷达图 (skillsChart) - 将使用真实数据更新
  if (skillsChart.value && learningContentCompletionRate.value) {
    updateRadarChart('hard')
  }

  // 弱项图表 (柱状图) - 数据加载后更新
  if (weaknessChart.value && identifySkillWeaknesses.value.length > 0) {
    updateWeaknessChart()
  }

  // 评分图表 (柱状图) - 数据加载后更新
  if (ratingChart.value && discoverLearningInterests.value.length > 0) {
    updateRatingChart()
  }

  // 时间图表 (饼图) - 数据加载后更新
  if (timeChart.value && learningTimeAnalysis.value) {
    updateTimeChart()
  }
}

// 使用真实数据更新弱项图表的函数
function updateWeaknessChart() {
  if (!weaknessChart.value || identifySkillWeaknesses.value.length === 0) return

  const weaknessSkills = identifySkillWeaknesses.value.map((item) => item.skillName)
  const weaknessErrorRates = identifySkillWeaknesses.value.map((item) =>
    Math.round(item.errorRate * 100)
  )

  const weaknessChartInstance = echarts.init(weaknessChart.value)
  const weaknessOption = {
    title: {
      text: 'Weakness Distribution Analysis',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: '{b}: {c}%'
    },
    xAxis: {
      type: 'category',
      data: weaknessSkills,
      axisLabel: {
        rotate: 45
      }
    },
    yAxis: {
      type: 'value',
      name: 'Error Rate (%)',
      max: Math.max(...weaknessErrorRates) + 10
    },
    series: [
      {
        data: weaknessErrorRates,
        type: 'bar',
        itemStyle: {
          color: '#ff6b6b'
        }
      }
    ]
  }
  weaknessChartInstance.setOption(weaknessOption)
}

// 使用真实数据更新评分图表的函数
function updateRatingChart() {
  if (!ratingChart.value || discoverLearningInterests.value.length === 0) return

  const ratingSkills = discoverLearningInterests.value.map((item) => item.skillName)
  const ratingValues = discoverLearningInterests.value.map((item) => item.star)

  const ratingChartInstance = echarts.init(ratingChart.value)
  const ratingOption = {
    title: {
      text: 'Top Rated Skills',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: '{b}: {c}'
    },
    xAxis: {
      type: 'category',
      data: ratingSkills,
      axisLabel: {
        rotate: 45
      }
    },
    yAxis: {
      type: 'value',
      name: 'Rating',
      max: 1
    },
    series: [
      {
        data: ratingValues,
        type: 'bar',
        itemStyle: {
          color: '#4caf50'
        }
      }
    ]
  }
  ratingChartInstance.setOption(ratingOption)
}

// 使用真实数据更新雷达图的函数
function updateRadarChart(category: string) {
  if (!skillsChart.value || !learningContentCompletionRate.value) return

  const { skills, data } = getSkillDataForCategory(category)

  if (skills.length === 0) return

  if (myChart) {
    myChart.dispose()
  }

  myChart = echarts.init(skillsChart.value)
  const radarOption = {
    title: {
      text: 'Learning Content Completion Rate',
      left: 'center'
    },
    // tooltip: {
    //   trigger: 'item',
    //   formatter: function(params: any) {
    //     // 雷达图的tooltip处理：显示所有技能数据，而不是单个技能
    //     if (params.componentType === 'series' && params.seriesType === 'radar') {
    //       if (params.value && Array.isArray(params.value)) {
    //         let tooltipContent = '<strong>' + params.name + '</strong><br/>'
    //         skills.forEach((skill, index) => {
    //           if (index < params.value.length) {
    //             tooltipContent += `${skill}: ${params.value[index]}%<br/>`
    //           }
    //         })
    //         return tooltipContent
    //       }
    //     }
    //     return '无可用数据'
    //   }
    // },
    legend: {
      data: ['Completion Rate'],
      bottom: 0
    },
    radar: {
      indicator: skills.map((name) => ({ name, max: 100 })),
      radius: '60%'
    },
    series: [
      {
        name: 'Learning Content Completion Rate',
        type: 'radar',
        data: [
          {
            value: data,
            name: 'Completion Rate',
            areaStyle: {
              color: 'rgba(58, 157, 255, 0.2)'
            },
            lineStyle: {
              color: 'rgb(58, 157, 255)'
            },
            itemStyle: {
              color: 'rgb(58, 157, 255)'
            }
          }
        ]
      }
    ]
  }
  myChart.setOption(radarOption)
}

// 使用真实数据更新时间图表的函数
function updateTimeChart() {
  if (!timeChart.value || !learningTimeAnalysis.value) return

  const timeData = learningTimeAnalysis.value

  // 从百分比字符串转换为数值
  const earlyMorning = parseFloat(timeData.earlyMorningPercentage?.replace('%', '') || '0')
  const am = parseFloat(timeData.amPercentage?.replace('%', '') || '0')
  const pm = parseFloat(timeData.pmPercentage?.replace('%', '') || '0')
  const evening = parseFloat(timeData.eveningPercentage?.replace('%', '') || '0')

  const timeChartInstance = echarts.init(timeChart.value)
  const timeOption = {
    title: {
      text: 'Learning Time Distribution by Period',
      left: 'center'
    },
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c}%'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [
      {
        name: 'Learning Time',
        type: 'pie',
        radius: '50%',
        data: [
          { value: earlyMorning, name: 'Early Morning (00-06)', itemStyle: { color: '#3a9dff' } },
          { value: am, name: 'Morning (06-12)', itemStyle: { color: '#4caf50' } },
          { value: pm, name: 'Afternoon (12-18)', itemStyle: { color: '#ffd166' } },
          { value: evening, name: 'Evening (18-24)', itemStyle: { color: '#ff6b6b' } }
        ].filter((item) => item.value > 0), // 只显示有数据的时间段
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  timeChartInstance.setOption(timeOption)
}

// 获取主要学习时段和相应建议
function getLearningTimeInsight() {
  if (!learningTimeAnalysis.value)
    return { peakPeriod: null, percentage: 0, recommendation: '-' }

  const timeData = learningTimeAnalysis.value
  const periods = [
    {
      name: 'Early Morning (00-06)',
      value: parseFloat(timeData.earlyMorningPercentage?.replace('%', '') || '0'),
      period: 'early morning'
    },
    {
      name: 'Morning (06-12)',
      value: parseFloat(timeData.amPercentage?.replace('%', '') || '0'),
      period: 'morning'
    },
    {
      name: 'Afternoon (12-18)',
      value: parseFloat(timeData.pmPercentage?.replace('%', '') || '0'),
      period: 'afternoon'
    },
    {
      name: 'Evening (18-24)',
      value: parseFloat(timeData.eveningPercentage?.replace('%', '') || '0'),
      period: 'evening'
    }
  ]

  const peakPeriod = periods.reduce((max, current) => (current.value > max.value ? current : max))

  let recommendation = ''
  if (peakPeriod.period === 'early morning' && peakPeriod.value > 30) {
    recommendation = `${peakPeriod.value}% of learning concentrated in early morning hours (00-06), consider adjusting sleep schedule`
  } else if (peakPeriod.period === 'evening' && peakPeriod.value > 50) {
    recommendation = `${peakPeriod.value}% of learning concentrated in evening hours (18-24), recommend earlier learning sessions for better retention`
  } else if (peakPeriod.period === 'morning') {
    recommendation = `${peakPeriod.value}% of learning in morning hours - excellent for retention and focus!`
  } else if (peakPeriod.period === 'afternoon') {
    recommendation = `${peakPeriod.value}% of learning in afternoon - good for practical application and review`
  } else {
    recommendation = `Learning time is well distributed across different periods`
  }

  return { peakPeriod: peakPeriod.name, percentage: peakPeriod.value, recommendation }
}

// 监听类别变化
watch(selectedCategory, (newCategory) => {
  if (!learningContentCompletionRate.value) return
  updateRadarChart(newCategory)
})

/** ----- LIFECYCLE HOOKS ----- */
onMounted(async () => {
  try {
    await Promise.all([
      getBaseInfo(), // 获取用户基本信息
      getCoreMetrics(), // 获取核心学习指标
      getLearningProgressAnalysis(), // 获取学习要求完成情况
      getLearningContentCompletionRate(), // 获取技能掌握分布
      getIdentifySkillWeaknesses(), // 获取短板分布分析
      getDiscoverLearningInterests(), // 获取发现学习兴趣
      getLearningTimeAnalysis() // 获取学习时长分析
    ])
  } finally {
    loading.value = false // 无论成功失败都关闭加载状态
  }

  await nextTick(() => {
    initializeCharts()
    // 数据加载完成后使用真实数据更新图表
    if (identifySkillWeaknesses.value.length > 0) {
      updateWeaknessChart()
    }
    if (learningContentCompletionRate.value) {
      updateRadarChart(selectedCategory.value)
    }
    if (discoverLearningInterests.value.length > 0) {
      updateRatingChart()
    }
    if (learningTimeAnalysis.value) {
      updateTimeChart()
    }
  })
})
</script>

<template>
  <ContentWrap :body-style="{ padding: '0' }">
    <el-scrollbar class="!app-main-height">
      <div class="mb-8 overflow-hidden relative">
        <!-- AI Analysis 按钮 - 页头右上角 -->
        <el-button
          @click="handleAIAnalysis"
          :disabled="aiAnalysisLoading"
          :loading="aiAnalysisLoading"
          class="absolute top-8 right-8 z-50 bg-gradient-to-br from-white to-[#017b3d] hover:from-gray-50 hover:to-[#017b3d]/50 text-[#017b3d] shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 border border-gray-200"
        >
          <img v-if="!aiAnalysisLoading" src="@/assets/svgs/generate.svg" class="w-4 h-4 mr-2" />
          {{ aiAnalysisLoading ? 'Analyzing...' : 'AI Analysis' }}
        </el-button>

        <!--渐变背景-->
        <div
          class="flex items-center justify-center w-full min-h-126 bg-gradient-to-b from-[#017B3D] to-white text-white p-10 md:p-8"
        >
          <div class="mx-auto text-center mb-20">
            <!-- Profile -->
            <div class="flex flex-col md:flex-row items-center justify-center mb-6">
              <el-avatar
                :size="96"
                :src="userStore.getUser.avatar"
                style="border: 4px solid white; margin-bottom: 16px; margin-right: 24px"
              >
                <Icon icon="ep:graduation-cap" class="w-12 h-12" />
              </el-avatar>

              <!-- 用户信息 -->
              <div
                class="text-center md:text-left md:max-w-md lg:max-w-2xl xl:max-w-3xl 2xl:max-w-4xl"
              >
                <h1 class="items-center text-2xl md:text-3xl font-bold mb-2">
                  {{ baseInfo?.nickname || '-' }} | {{ baseInfo?.positionName || '-' }}
                </h1>
                <p class="text-blue-100 text-base"> Dept: {{ baseInfo?.deptName || '-' }} </p>
              </div>
            </div>

            <!-- Report Period -->
            <el-tag
              size="large"
              type="info"
              effect="light"
              style="
                background: rgba(255, 255, 255, 0.2);
                color: white;
                border: 1px solid rgba(255, 255, 255, 0.3);
                padding: 8px 16px;
                font-size: 16px;
                margin-bottom: 16px;
              "
            >
              <Icon icon="ep:calendar" class="w-4 h-4 mr-2" />
              Report Period: June 11, 2024 - June 11, 2025 (1 year)
            </el-tag>

            <!-- Summary -->
            <template v-if="aiAnalysisLoading">
              <div class="bg-white/15 rounded-lg p-4 max-w-3xl mx-auto">
                <el-skeleton :rows="2" animated />
              </div>
            </template>
            <template v-else-if="aiContent">
              <div class="bg-white/15 rounded-lg p-4 max-w-3xl mx-auto">
                <div class="flex items-center justify-center mb-2">
                  <Icon icon="ep:light" class="w-5 h-5 mr-2" />
                  <span class="font-medium">Learning Profile:</span>
                </div>
                <p class="text-lg">
                  <strong>{{
                    '"' + aiContent.studyOverview + '"' ||
                    '"New OLP user, limited learning history"'
                  }}</strong>
                </p>
              </div>
            </template>
          </div>
        </div>
      </div>

      <!-- 内容区域 -->
      <div class="flex flex-col gap-8 px-6 relative">
        <!-- Core Learning Metrics Section -->
        <div>
          <div class="flex items-center my-6">
            <div
              class="w-10 h-10 rounded-full bg-[#017B3D] flex items-center justify-center text-white mr-3"
            >
              <Icon icon="ep:trend-charts" class="w-5 h-5 text-white" />
            </div>
            <h2 class="text-2xl font-bold text-[#017B3D]">Core Learning Metrics</h2>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <!-- Learning Engagement Card -->
            <el-card
              class="hover:shadow-lg transition-all duration-300 hover:-translate-y-1"
              shadow="never"
              :body-style="{ padding: '24px' }"
            >
              <div class="flex items-center mb-4">
                <div class="w-12 h-12 rounded-lg bg-blue-50 flex items-center justify-center mr-4">
                  <Icon icon="ep:clock" class="w-6 h-6 text-[#017B3D]" />
                </div>
                <h3 class="font-semibold text-gray-900">Learning Engagement</h3>
              </div>
              <div class="text-3xl font-bold text-[#017B3D] mb-2"
                >{{
                  coreLearningMetrics?.learningEngagement?.totalLearningDuration || 0
                }}
                hrs</div
              >
              <p class="text-sm text-gray-600 mb-3">Total learning hours</p>
              <el-progress
                :percentage="
                  Math.min(
                    ((coreLearningMetrics?.learningEngagement?.activeDays || 0) / 365) * 100,
                    100
                  )
                "
                class="mb-3"
              />
              <p class="text-sm text-gray-600"
                >Active days: {{ coreLearningMetrics?.learningEngagement?.activeDays || 0 }} days
                (Avg
                {{ coreLearningMetrics?.learningEngagement?.avgHoursPerDay || 0 }}
                hrs/day)</p
              >
            </el-card>

            <!-- Learning Progress Card -->
            <el-card
              class="hover:shadow-lg transition-all duration-300 hover:-translate-y-1"
              shadow="never"
              :body-style="{ padding: '24px' }"
            >
              <div class="flex items-center mb-4">
                <div class="w-12 h-12 rounded-lg bg-green-50 flex items-center justify-center mr-4">
                  <Icon icon="ep:select" class="w-6 h-6 text-green-600" />
                </div>
                <h3 class="font-semibold text-gray-900">Learning Progress</h3>
              </div>
              <div class="text-3xl font-bold text-[#017B3D] mb-2">{{
                coreLearningMetrics?.learningProgress?.completedCount || 0
              }}</div>
              <p class="text-sm text-gray-600 mb-3">Completed courses</p>
              <el-progress
                :percentage="
                  parseInt(
                    coreLearningMetrics?.learningProgress?.completionRate?.replace('%', '') || '0'
                  )
                "
                class="mb-3"
              />
              <p class="text-sm text-gray-600"
                >Completion rate:
                {{ coreLearningMetrics?.learningProgress?.completionRate || '0%' }}</p
              >
            </el-card>

            <!-- Skill Coverage Card -->
            <el-card
              class="hover:shadow-lg transition-all duration-300 hover:-translate-y-1"
              shadow="never"
              :body-style="{ padding: '24px' }"
            >
              <div class="flex items-center mb-4">
                <div
                  class="w-12 h-12 rounded-lg bg-purple-50 flex items-center justify-center mr-4"
                >
                  <Icon icon="ep:aim" class="w-6 h-6 text-purple-600" />
                </div>
                <h3 class="font-semibold text-gray-900">Skill Coverage</h3>
              </div>
              <div class="text-3xl font-bold text-[#017B3D] mb-2">{{
                coreLearningMetrics?.skillCoverage?.masteryRate || '0%'
              }}</div>
              <p class="text-sm text-gray-600 mb-3">Job skills mastered</p>
              <el-progress
                :percentage="
                  parseInt(
                    coreLearningMetrics?.skillCoverage?.masteryRate?.replace('%', '') || '0'
                  )
                "
                class="mb-3"
              />
              <p class="text-sm text-gray-600"
                >{{ coreLearningMetrics?.skillCoverage?.masteredCount || 0 }} out of
                {{ coreLearningMetrics?.skillCoverage?.total || 0 }} core skills mastered</p
              >
            </el-card>

            <!-- Learning Effectiveness Card -->
            <el-card
              class="hover:shadow-lg transition-all duration-300 hover:-translate-y-1"
              shadow="never"
              :body-style="{ padding: '24px' }"
            >
              <div class="flex items-center mb-4">
                <div
                  class="w-12 h-12 rounded-lg bg-yellow-50 flex items-center justify-center mr-4"
                >
                  <Icon icon="ep:trophy" class="w-6 h-6 text-yellow-600" />
                </div>
                <h3 class="font-semibold text-gray-900">Learning Effectiveness</h3>
              </div>
              <div class="text-3xl font-bold text-[#017B3D] mb-2"
                >{{
                  Math.round(
                    (coreLearningMetrics?.learningEffectiveness?.avgExamCorrectRate || 0) * 100
                  )
                }}%</div
              >
              <p class="text-sm text-gray-600 mb-3">Average exam score</p>
              <el-progress
                :percentage="
                  Math.round(
                    (coreLearningMetrics?.learningEffectiveness?.avgExamCorrectRate || 0) * 100
                  )
                "
                class="mb-3"
              />
              <p class="text-sm text-gray-600"
                >Certificates:
                {{ coreLearningMetrics?.learningEffectiveness?.gotCertificateCount || 0 }}</p
              >
            </el-card>
          </div>
        </div>

        <!-- Learning Progress Analysis Section -->
        <div class="">
          <div class="flex items-center mb-6">
            <div
              class="w-10 h-10 rounded-full bg-[#017B3D] flex items-center justify-center text-white mr-3"
            >
              <Icon icon="ep:histogram" class="w-5 h-5 text-white" />
            </div>
            <h2 class="text-2xl font-bold text-[#017B3D]">Learning Progress Analysis</h2>
          </div>

          <!-- Learning Requirements Completion -->
          <el-card shadow="never" class="mb-8">
            <template #header>
              <div class="flex items-center text-[#017B3D]">
                <Icon icon="ep:reading" class="w-5 h-5 mr-2" />
                Learning Requirements Completion
              </div>
            </template>
            <div
              class="bg-gradient-to-r from-blue-50 to-cyan-50 border-l-4 border-blue-500 p-4 rounded-lg mb-4"
            >
              <p class="font-medium text-gray-900 flex gap-1">
                <strong>Required Skills:</strong>
                <span v-if="learningProgressAnalysis?.requiredSkills?.length">
                  {{ learningProgressAnalysis.requiredSkills.join(', ') }}
                </span>
                <span v-else class="text-gray-500">-</span>
              </p>
            </div>

            <div class="space-y-3">
              <div class="flex items-start p-3 bg-green-50 rounded-lg">
                <Icon icon="ep:circle-check" class="w-5 h-5 text-green-600 mr-3 mt-0.5" />
                <div class="flex gap-1">
                  <strong class="text-gray-900">Certificates Earned:</strong>
                  <span class="text-gray-700">
                    <span v-if="learningProgressAnalysis?.earnedCertificates?.length">
                      {{ learningProgressAnalysis.earnedCertificates.join(', ') }}
                    </span>
                    <span v-else class="text-gray-500">-</span>
                  </span>
                </div>
              </div>

              <div class="flex items-start p-3 bg-green-50 rounded-lg">
                <Icon icon="ep:circle-check" class="w-5 h-5 text-green-600 mr-3 mt-0.5" />
                <div class="flex gap-1">
                  <strong class="text-gray-900">Courses Completed:</strong>
                  <span class="text-gray-700">
                    {{ learningProgressAnalysis?.completedCourses?.completedCount || 0 }} out of
                    {{ learningProgressAnalysis?.completedCourses?.total || 0 }} courses ({{
                      learningProgressAnalysis?.completedCourses?.completionRate || '0%'
                    }}
                    completion)
                  </span>
                </div>
              </div>

              <div>
                <div class="flex items-start p-3 bg-orange-50 rounded-lg">
                  <Icon icon="ep:circle-close" class="w-5 h-5 text-orange-600 mr-3 mt-0.5" />
                  <div>
                    <div :class="aiContent?.skipAnalysis ? 'mb-3' : 'mb-0'">
                      <strong class="text-gray-900">Courses Skipped:</strong>
                      <span class="text-gray-700">
                        Skipped
                        {{ learningProgressAnalysis?.skippedStudyContent?.skippedCount || 0 }}
                        learning contents of
                        {{ learningProgressAnalysis?.skippedStudyContent?.total || 0 }} skills
                      </span>
                    </div>
                    <template v-if="aiAnalysisLoading">
                      <div class="flex items-center">
                        <el-skeleton class="w-4 h-4 rounded-full mr-2 mt-0.5" />
                        <div class="flex-1">
                          <el-skeleton class="h-4 w-full" />
                        </div>
                      </div>
                    </template>
                    <template v-else-if="aiContent?.skipAnalysis">
                      <div class="flex items-center">
                        <Icon icon="ep:info-filled" class="w-4 h-4 text-blue-600 mr-2 mt-0.5" />
                        <span class="text-sm text-gray-600">
                          <strong>Skip Analysis:</strong> {{ aiContent.skipAnalysis }}
                        </span>
                      </div>
                    </template>
                  </div>
                </div>
              </div>
            </div>
          </el-card>

          <!-- Learning Content Completion Rate -->
          <el-card shadow="never" class="">
            <template #header>
              <div class="flex items-center text-[#017B3D]">
                <Icon icon="ep:discount" class="w-5 h-5 mr-2" />
                Learning content completion rate (by skill)
              </div>
            </template>
            <div class="flex items-center gap-3 mb-6">
              <el-text class="font-medium text-gray-700">Filter by category:</el-text>
              <el-select v-model="selectedCategory" class="w-64" placeholder="Select category">
                <el-option value="all" label="All categories">All categories</el-option>
                <el-option value="hard" label="Hard skill">Hard skill</el-option>
                <el-option value="soft" label="Soft skill">Soft skill</el-option>
                <el-option value="hse" label="HSE skill">HSE skill</el-option>
              </el-select>
            </div>

            <div class="h-96">
              <div ref="skillsChart" class="w-full h-full"></div>
            </div>

            <div class="bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-4 mt-4">
              <div class="flex items-center justify-center text-center">
                <Icon icon="ep:star-filled" class="w-5 h-5 text-yellow-500 mr-2" />
                <span class="font-medium">Top Skill:</span>
                <span v-if="getTopBottomSkills().topSkill" class="text-blue-600 font-bold mx-2">
                  {{ getTopBottomSkills().topSkill.name }} ({{
                    getTopBottomSkills().topSkill.rate
                  }}%)
                </span>
                <span v-else class="text-gray-500 mx-2">-</span>
                <span class="mx-2">|</span>
                <Icon icon="ep:warning" class="w-5 h-5 text-orange-500 mr-2" />
                <span class="font-medium">Needs Improvement:</span>
                <span
                  v-if="getTopBottomSkills().bottomSkill"
                  class="text-orange-600 font-bold ml-2"
                >
                  {{ getTopBottomSkills().bottomSkill.name }} ({{
                    getTopBottomSkills().bottomSkill.rate
                  }}%)
                </span>
                <span v-else class="text-gray-500 ml-2">-</span>
              </div>
            </div>
          </el-card>
        </div>

        <!-- Skill Analysis Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <!-- Skill Weaknesses -->
          <el-card shadow="never">
            <template #header>
              <div class="flex items-center text-[#017B3D]">
                <Icon icon="ep:warning" class="w-5 h-5 mr-2" />
                Identify Skill Weaknesses
              </div>
            </template>
            <div class="space-y-4">
              <template v-if="aiAnalysisLoading">
                <div class="p-4 bg-red-50 rounded-lg">
                  <el-skeleton class="h-6 w-32 mb-3" />
                  <div class="flex flex-wrap gap-2">
                    <el-skeleton class="h-6 w-20" />
                    <el-skeleton class="h-6 w-24" />
                    <el-skeleton class="h-6 w-28" />
                  </div>
                </div>
              </template>

              <!--AI分析-->
              <template v-else-if="aiContent?.skillWeakness">
                <div
                  v-for="skill in aiContent?.skillWeakness"
                  :key="skill.skillName"
                  class="p-4 bg-red-50 rounded-lg"
                >
                  <h4 class="font-bold text-gray-900 mb-3">{{ skill.skillName }}</h4>
                  <div class="flex flex-wrap gap-2">
                    <!--虚弱率-->
                    <el-tag type="danger" class="text-xs">
                      <Icon icon="ep:bug" class="w-3 h-3 mr-1" />
                      Error Rate: {{ Math.round(skill.errorRate * 100) }}%
                    </el-tag>

                    <!--完成率-->
                    <el-tag class="text-xs">
                      <Icon icon="ep:bug" class="w-3 h-3 mr-1" />
                      Completion Rate: {{ Math.round(skill.completeRate * 100) }}%
                    </el-tag>

                    <!--访问量-->
                    <el-tag type="info" class="text-xs">
                      <Icon icon="ep:bug" class="w-3 h-3 mr-1" />
                      Course Access: {{ skill.courseAccess }}
                    </el-tag>
                  </div>
                </div>
              </template>
            </div>

            <div class="mt-6">
              <h4 class="font-medium text-gray-900 mb-3">Weakness Distribution Analysis</h4>
              <div class="h-64">
                <div ref="weaknessChart" class="w-full h-full"></div>
              </div>
            </div>
          </el-card>

          <!-- Learning Interests -->
          <el-card shadow="never">
            <template #header>
              <div class="flex items-center text-[#017B3D]">
                <Icon icon="ep:star-filled" class="w-5 h-5 mr-2" />
                Discover Learning Interests
              </div>
            </template>
            <div class="space-y-4">
              <template v-if="aiAnalysisLoading">
                <div class="p-4 bg-green-50 rounded-lg">
                  <el-skeleton class="h-6 w-32 mb-3" />
                  <div class="flex flex-wrap gap-2">
                    <el-skeleton class="h-6 w-20" />
                    <el-skeleton class="h-6 w-24" />
                    <el-skeleton class="h-6 w-28" />
                  </div>
                </div>
              </template>
              <template v-else-if="aiContent?.learningInterest">
                <div
                  v-for="interest in aiContent?.learningInterest"
                  :key="interest.skillName"
                  class="p-4 bg-green-50 rounded-lg"
                >
                  <h4 class="font-bold text-gray-900 mb-3">{{ interest.skillName }}</h4>
                  <div class="flex flex-wrap gap-2">
                    <!--评分-->
                    <el-tag type="warning" class="text-xs bg-yellow-500">
                      <Icon icon="ep:star-filled" class="w-3 h-3 mr-1" />
                      Rating: {{ interest.rate.toFixed(1) }}
                    </el-tag>

                    <!--完成率-->
                    <el-tag class="text-xs">
                      <Icon icon="ep:check" class="w-3 h-3 mr-1" />
                      Completion Rate: {{ Math.round(interest.completeRate * 100) }}%
                    </el-tag>
                  </div>
                </div>
                <div
                  v-if="discoverLearningInterests.length === 0"
                  class="p-4 bg-gray-50 rounded-lg"
                >
                  <p class="text-gray-500 text-center">-</p>
                </div>
              </template>
            </div>

            <div class="mt-6">
              <h4 class="font-medium text-gray-900 mb-3">Top Rated Skills</h4>
              <div class="h-64">
                <div ref="ratingChart" class="w-full h-full"></div>
              </div>
            </div>
          </el-card>
        </div>

        <!-- Learning Method Analysis Section -->
        <div class="">
          <div class="flex items-center mb-6">
            <div
              class="w-10 h-10 rounded-full bg-[#017B3D] flex items-center justify-center mr-3"
            >
              <Icon icon="ep:list" class="w-5 h-5 text-white" />
            </div>
            <h2 class="text-2xl font-bold text-[#017B3D]">Learning Method Analysis</h2>
          </div>

          <div class="grid grid-cols-1 gap-6">
            <!-- Learning Time Analysis -->
            <el-card shadow="never">
              <template #header>
                <div class="flex items-center text-[#017B3D]">
                  <Icon icon="ep:clock" class="w-5 h-5 mr-2" />
                  Learning Time Analysis
                </div>
              </template>
              <div class="h-80">
                <div ref="timeChart" class="w-full h-full"></div>
              </div>

              <template v-if="aiAnalysisLoading">
                <div class="mt-4 p-4 bg-orange-50 rounded-lg">
                  <div class="flex items-center">
                    <Icon icon="ep:moon" class="w-5 h-5 text-orange-600 mr-3 mt-0.5" />
                    <el-skeleton class="h-4 w-80" />
                  </div>
                </div>
              </template>
              <template v-else-if="aiContent?.studyMethodSuggest">
                <div class="mt-4 p-4 bg-orange-50 rounded-lg">
                  <div class="flex items-start">
                    <Icon icon="ep:moon" class="w-5 h-5 text-orange-600 mr-3 mt-0.5" />
                    <div>
                      <strong class="text-gray-900">Learning Time Insight: </strong>
                      <span class="text-gray-700">
                        {{ aiContent.studyMethodSuggest }}
                      </span>
                    </div>
                  </div>
                </div>
              </template>
            </el-card>
          </div>
        </div>

        <!-- Learning Improvement Recommendations -->
        <div class="mb-6">
          <div class="flex items-center mb-6">
            <div
              class="w-10 h-10 rounded-full bg-[#017B3D] flex items-center justify-center mr-3"
            >
              <Icon icon="ep:sunrise" class="w-5 h-5 text-white" />
            </div>
            <h2 class="text-2xl font-bold text-[#017B3D]"> Learning Improvement Recommendations </h2>
          </div>

          <template v-if="aiAnalysisLoading">
            <el-card :body-style="{ padding: '24px' }">
              <el-card shadow="never" class="bg-white">
                <template #header>
                  <div class="flex items-center text-blue-600">
                    <el-skeleton class="w-5 h-5 rounded-full mr-2" />
                    <el-skeleton class="h-6 w-48" />
                  </div>
                </template>
                <div class="space-y-3">
                  <el-skeleton class="h-5 w-full" />
                  <el-skeleton class="h-5 w-full" />
                  <el-skeleton class="h-5 w-3/4" />
                </div>
              </el-card>
            </el-card>
          </template>

          <template v-else>
            <el-card shadow="never" class="bg-white">
              <template #header>
                <div class="flex items-center text-[#017B3D]">
                  <Icon icon="ep:warning" class="w-5 h-5 mr-2" />
                  Insufficient Data for Recommendations
                </div>
              </template>
              <template v-if="aiAnalysisLoading">
                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-5 text-center mb-4">
                  <el-skeleton class="w-6 h-6 rounded-full mx-auto mb-3" />
                  <el-skeleton class="h-5 w-40 mx-auto mb-2" />
                  <el-skeleton class="h-4 w-80 mx-auto" />
                </div>
              </template>
              <div
                v-else-if="aiContent?.studyUpgradeSuggest"
                class="bg-yellow-50 border border-yellow-200 rounded-lg p-5 text-center mb-4"
              >
                <Icon icon="ep:info-filled" class="w-6 h-6 text-yellow-600 mx-auto mb-3" />
                <p class="text-yellow-800 font-medium mb-2">Limited Learning History</p>
                <p class="text-yellow-700 text-sm">
                  {{ aiContent.studyUpgradeSuggest }}
                </p>
              </div>

              <div v-else class="p-4 bg-[#017B3D]/10 rounded-lg">
                <h4 class="text-[#017B3D] font-medium mb-3 flex items-center">
                  <Icon icon="ep:sunrise" class="w-4 h-4 mr-2" />
                  Next Steps
                </h4>
                <div class="space-y-2 text-[#017B3D] text-sm">
                  <div class="flex items-start">
                    <div class="w-2 h-2 rounded-full bg-[#017B3D] mr-3 mt-2"></div>
                    <span>Complete your first course to establish learning patterns</span>
                  </div>
                  <div class="flex items-start">
                    <div class="w-2 h-2 rounded-full bg-[#017B3D] mr-3 mt-2"></div>
                    <span
                      >Engage with different course categories to build your learning profile</span
                    >
                  </div>
                  <div class="flex items-start">
                    <div class="w-2 h-2 rounded-full bg-[#017B3D] mr-3 mt-2"></div>
                    <span>Take exams to measure your learning progress</span>
                  </div>
                  <div class="flex items-start">
                    <div class="w-2 h-2 rounded-full bg-[#017B3D] mr-3 mt-2"></div>
                    <span
                      >Return after accumulating more learning data for personalized
                      recommendations</span
                    >
                  </div>
                </div>
              </div>
            </el-card>
          </template>
        </div>
      </div>
    </el-scrollbar>
  </ContentWrap>
</template>