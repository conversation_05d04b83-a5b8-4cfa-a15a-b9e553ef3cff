<script lang="ts" setup>
import { ref } from 'vue'
import type { TabsPaneContext } from 'element-plus'
import { OfficeBuilding, User } from '@element-plus/icons-vue'
import DepartmentReport from './DepartmentReport.vue'
import EmployeeReport from './EmployeeReport.vue'

defineOptions({ name: 'Report' })

const activeName = ref('Department Report')

const handleClick = (tab: TabsPaneContext, event: Event) => {
  console.log(tab, event)
}
</script>

<template>
  <ContentWrap>
    <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
      <!--部门学习报告-->
      <el-tab-pane name="Department Report">
        <template #label>
          <el-icon class="mr-2"><OfficeBuilding /></el-icon>
          <span>Department Report</span>
        </template>
        <DepartmentReport />
      </el-tab-pane>

      <!--员工学习报告-->
      <el-tab-pane name="Employee Report">
        <template #label>
          <el-icon class="mr-2"><User /></el-icon>
          <span>Employee Report</span>
        </template>
        <EmployeeReport />
      </el-tab-pane>
    </el-tabs>
  </ContentWrap>
</template>