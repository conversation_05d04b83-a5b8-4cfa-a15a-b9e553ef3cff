<script setup lang="ts">
import { useRouter } from 'vue-router'
import 'splitpanes/dist/splitpanes.css'
import { Pane, Splitpanes } from 'splitpanes'
import type { ElTree } from 'element-plus'
import { OrgType } from '@/enums/OrgType'
import { getSection, sectTreeSelect } from '@/api/system/section'
import { handlePhaseTree } from '@/utils/tree'
import { useI18n } from 'vue-i18n'
import GenerationDialog from '@/views/edp/learningmap/GenerationDialog.vue'
import { Position, positionLearningMapApi } from '@/api/edp/learningmap'
import { useMessage } from '@/hooks/web/useMessage'
import { departmentReportApi, EmployeesReportVO } from '@/api/edp/report'

const { t } = useI18n()
const router = useRouter()
const message = useMessage() // 消息弹窗
const list = ref<EmployeesReportVO[]>([])
const loading = ref(true)
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(30) // 每页显示20个卡片
const generationDialogRef = ref() // GenerationDialog 引用
const data = reactive<{
  form: any
  queryParams: any
  rules: any
}>({
  form: {},
  queryParams: {
    postCode: undefined,
    name: undefined,
    date: undefined,
    status: undefined,
    parentId: undefined,
    deptId: undefined,
    pageNo: 1,
    pageSize: 30
  },
  rules: {
    name: [{ required: true, message: t('sys.post.positionNameRule'), trigger: 'blur' }],
    postCode: [{ required: false, message: t('sys.post.positionCodeRule'), trigger: 'blur' }],
    orderNum: [{ required: true, message: t('sys.post.orderNumRule'), trigger: 'blur' }]
  }
})

const { queryParams, form, rules } = toRefs(data)
const deptName = ref('')
const deptOptions = ref<any[]>([])
const postTreeRef = ref<InstanceType<typeof ElTree>>()
/** 当前选中的部门信息 */
const selDept = ref()
const defaultExpand = ref()

/** 左侧-查询公司部门下拉树结构 */
const getDeptTree = async () => {
  const data = await sectTreeSelect({ type: 0 })
  console.log('部门树数据:', data)
  deptOptions.value = data

  // 查找默认选中的部门或二级节点
  let defaultNode = null
  for (const company of deptOptions.value) {
    if (company.children && company.children.length > 0) {
      // 优先选择第一个部门节点
      const deptNode = company.children.find((child) => child.level === OrgType.Department)
      if (deptNode) {
        defaultNode = deptNode
        break
      }
      // 如果没有部门，选择第一个子节点
      defaultNode = company.children[0]
      break
    }
  }

  // 如果没有找到合适的节点，回退到第一个公司节点
  if (!defaultNode) {
    defaultNode = deptOptions.value[0]
  }

  selDept.value = defaultNode

  // 展开默认选中节点的父级
  const expandKeys = []
  if (defaultNode.level !== OrgType.Company) {
    // 展开公司节点
    const parentCompany = deptOptions.value.find((company) =>
      company.children?.some((child) => child.virtualId === defaultNode.virtualId)
    )
    if (parentCompany) {
      expandKeys.push(parentCompany.virtualId)
    }
  }
  expandKeys.push(defaultNode.virtualId)
  defaultExpand.value = expandKeys

  await nextTick(() => {
    postTreeRef.value?.setCurrentNode(defaultNode)
  })

  // 调用handleNodeClick传递默认节点
  await handleNodeClick(defaultNode)
}

/** 通过条件过滤节点  */
const filterNode = (value: string, data: any) => {
  if (!value) return true
  return data.label.toLocaleLowerCase().includes(value.toLocaleLowerCase())
}

/** 根据名称筛选部门树 */
watch(deptName, (val) => {
  postTreeRef.value!.filter(val)
})

/** 查询部门下员工列表 */
const getList = async () => {
  loading.value = true
  try {
    // 同步分页参数
    queryParams.value.pageNo = currentPage.value
    queryParams.value.pageSize = pageSize.value

    const data = await departmentReportApi.getEmployeesReportByDeptId(queryParams.value)

    // 如果返回的是分页数据结构
    if (data.list && data.total !== undefined) {
      list.value = data.list
      total.value = data.total
    } else {
      // 如果返回的是数组，进行客户端分页
      const allData = handlePhaseTree(data, 'postId')
      total.value = allData.length

      const startIndex = (currentPage.value - 1) * pageSize.value
      const endIndex = startIndex + pageSize.value
      list.value = allData.slice(startIndex, endIndex)
    }
  } finally {
    loading.value = false
  }
}

// 节点点击事件
const handleNodeClick = async (node: any) => {
  console.log('node:', node)

  // 一级节点（公司）不可点击
  if (node.level === OrgType.Company) {
    return
  }

  selDept.value = node
  if (node.level === OrgType.Department) {
    queryParams.value.deptId = node.id
    queryParams.value.compId = null
    queryParams.value.sectId = null
    queryParams.value.sectName = null
    queryParams.value.date = 2025
  }
  if (node.level === OrgType.Section) {
    queryParams.value.sectName = node.label
    queryParams.value.sectId = node.id
    queryParams.value.deptId = null
    queryParams.value.compId = null
    const data = await getSection(node.id as number)
    queryParams.value.deptId = data.department.id
    queryParams.value.date = 2025
  }
  await getList()
}

// 查看员工详情
const handleViewDetails = (userId?: number) => {
  if (userId) {
    router.push({
      path: '/edp/report-details',
      query: { userId }
    })
  } else {
    message.warning('User ID is missing')
    return
  }
}

/** 分页处理 */
const handleCurrentChange = (page: number) => {
  currentPage.value = page
  getList()
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  getList()
}

/** 初始化 */
onMounted(() => {
  // 同步分页参数
  queryParams.value.pageNo = currentPage.value
  queryParams.value.pageSize = pageSize.value
  getDeptTree()
})
</script>

<template>
  <div class="app-main-height">
    <Splitpanes class="default-theme">
      <Pane :size="26" class="!bg-white">
        <el-scrollbar class="!app-main-height p-2.5" height="calc(100vh)">
          <div class="head-container">
            <el-input
              v-model="deptName"
              :placeholder="t('sys.user.deptNamePH')"
              clearable
              class="mb-[20px]"
            >
              <template #prefix>
                <Icon class="mr-5px" icon="ep:search" />
              </template>
            </el-input>
          </div>
          <div class="head-container">
            <el-tree
              ref="postTreeRef"
              :data="deptOptions"
              :props="{ label: 'label', children: 'children' }"
              :expand-on-click-node="false"
              :default-expanded-keys="defaultExpand"
              :filter-node-method="filterNode"
              node-key="virtualId"
              highlight-current
              @node-click="handleNodeClick"
            >
              <template #default="{ node }">
                <div
                  class="flex justify-between"
                  :class="{ 'cursor-not-allowed opacity-60': node.data.level === OrgType.Company }"
                >
                  <div class="me-2.5">
                    <el-tag
                      v-if="node.data.level === OrgType.Company"
                      :title="node.data.shortName"
                      :style="{
                        '--el-tag-text-color': '#630EB8',
                        '--el-tag-bg-color': '#F3F1FF',
                        '--el-tag-border-color': '#D3CEF0'
                      }"
                    >
                      {{ node.data.shortName ? node.data.shortName : 'Company' }}
                    </el-tag>
                    <el-tag
                      v-else-if="node.data.level === OrgType.Department"
                      :title="node.data.shortName"
                    >
                      {{ node.data.shortName ? node.data.shortName : 'Dept' }}
                    </el-tag>
                    <el-tag v-else-if="node.data.level === OrgType.Section" type="info">
                      {{ 'Section' }}
                    </el-tag>
                  </div>
                  <span :title="node.label" class="whitespace-normal line-clamp-1 break-all">
                    {{ node.label }}</span
                  >
                </div>
              </template>
            </el-tree>
          </div>
        </el-scrollbar>
      </Pane>

      <Pane class="!app-main-height !bg-white">
        <el-scrollbar class="app-main-height p-2.5" height="calc(100vh)">
          <div class="pt-3 flex items-center">
            <div class="p-2">
              <el-tag
                v-if="selDept?.level === OrgType.Company"
                :style="{
                  '--el-tag-text-color': '#630EB8',
                  '--el-tag-bg-color': '#F3F1FF',
                  '--el-tag-border-color': '#D3CEF0'
                }"
              >
                {{ t('global.company') }}
              </el-tag>
              <el-tag v-if="selDept?.level === OrgType.Department">
                {{ t('sys.user.department') }}
              </el-tag>
              <el-tag v-else-if="selDept?.level === OrgType.Section" type="info">
                {{ t('sys.user.section') }}
              </el-tag>
              <span class="ms-2.5">{{ selDept?.label }}</span>
            </div>
          </div>
          <OrgTotalBar :number="total" :text="t('sys.post.totalTip')" />

          <!--员工学习报告卡片列表-->
          <ContentWrap v-loading="loading" class="min-h-[20vh]">
            <div
              class="grid gap-4 grid-cols-1 md:grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4"
            >
              <el-card
                v-for="employee in list"
                :key="employee.userId"
                class="hover:border-[#017b3d] transition-all duration-300"
                shadow="hover"
                :body-style="{
                  padding: '20px',
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'space-between',
                  height: '100%'
                }"
              >
                <!-- 员工信息部分 -->
                <div class="flex flex-col items-center space-y-3">
                  <!-- 员工头像 -->
                  <div
                    class="w-20 h-20 bg-[#e8f5e8] rounded-full flex items-center justify-center overflow-hidden"
                  >
                    <img
                      v-if="employee.avatar"
                      :src="employee.avatar"
                      :alt="employee.nickname || 'Employee'"
                      class="w-full h-full object-cover"
                      @error="$event.target.style.display = 'none'"
                    />
                    <Icon v-else icon="ep:avatar" :size="30" class="text-[#017b3d]" />
                  </div>
                  <!-- 员工姓名 -->
                  <div class="font-bold text-[#017b3d] text-center">{{
                    employee.nickname || '-'
                  }}</div>
                  <!-- 岗位 -->
                  <div class="text-sm text-gray-500 text-center px-2 py-1 bg-gray-50 rounded-lg">{{
                    employee.positionName || '-'
                  }}</div>
                </div>

                <el-divider border-style="dashed" />

                <!-- 学习统计信息 -->
                <div class="grid grid-cols-3 gap-3 text-center">
                  <div>
                    <div class="text-lg font-bold text-[#017b3d]">{{
                      employee.totalStudyTime || '-'
                    }}</div>
                    <div class="text-xs text-gray-500 mt-1"> Hours </div>
                  </div>
                  <div>
                    <div class="text-lg font-bold text-[#017b3d]">{{
                      employee.completedCourseCount || '-'
                    }}</div>
                    <div class="text-xs text-gray-500 mt-1"> Courses </div>
                  </div>
                  <div>
                    <div class="text-lg font-bold text-[#017b3d]">{{
                      employee.skillCoverage || '-'
                    }}</div>
                    <div class="text-xs text-gray-500 mt-1"> Completed </div>
                  </div>
                </div>

                <!-- 查看详情按钮 -->
                <div class="flex mt-6 w-full items-center justify-end">
                  <el-button
                    type="primary"
                    @click="handleViewDetails(employee.userId)"
                    class="!bg-[#017b3d] !hover:bg-[#e8f5e8] !hover:text-[#017b3d] !border-none"
                  >
                    <Icon icon="ep:view" class="mr-1" />
                    <p> View Report </p>
                  </el-button>
                </div>
              </el-card>
            </div>

            <!-- 空状态 -->
            <div v-if="!loading && list.length === 0" class="text-center py-12">
              <Icon icon="ep:document" class="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p class="text-gray-500">{{ t('common.noData') }}</p>
            </div>

            <!-- 分页 -->
            <div v-if="total > 0" class="flex justify-end mt-6">
              <el-pagination
                v-model:current-page="currentPage"
                v-model:page-size="pageSize"
                :page-sizes="[10, 15, 20, 30, 50]"
                :total="total"
                layout="total, sizes, prev, pager, next, jumper"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                background
              />
            </div>
          </ContentWrap>
        </el-scrollbar>
      </Pane>
    </Splitpanes>

    <GenerationDialog ref="generationDialogRef" />
  </div>
</template>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>