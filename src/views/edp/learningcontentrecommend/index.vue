<template>
  <ContentWrap>
    <!-- Search Toolbar -->
    <el-form class="-mb-15px" :model="queryParams" ref="queryFormRef" :inline="true">
      <el-form-item label="Content Title" prop="title">
        <el-input
          v-model="queryParams.title"
          placeholder="Enter content title (fuzzy search)"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="Keywords" prop="keywords">
        <el-input
          v-model="queryParams.keywords"
          placeholder="Enter keywords"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>

      <!-- Skill Search -->
      <el-form-item label="Skill Name" prop="skillName">
        <el-input
          v-model="queryParams.skillName"
          placeholder="Enter skill name (exact search)"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>

      <!-- Match Degree Range Search -->
      <el-form-item label="Match Degree" prop="matchDegreeRange">
        <el-input-number
          v-model="queryParams.degreeRangeIndex"
          :min="0"
          :max="100"
          placeholder="Min value"
          class="!w-100px"
        />
        <span class="mx-2">-</span>
        <el-input-number
          v-model="queryParams.degreeRangeOffset"
          :min="0"
          :max="100"
          placeholder="Max value"
          class="!w-100px"
        />
      </el-form-item>

      <!-- Create Time -->
      <el-form-item label="Create Time" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="Start Date"
          end-placeholder="End Date"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-220px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> Search </el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> Reset </el-button>
        <el-button type="warning" @click="handleUnmatchedContent">
          <Icon icon="ep:document-add" class="mr-5px" /> Unmatched Content
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table
      v-loading="loading"
      :data="flatList"
      :show-overflow-tooltip="true"
      :span-method="objectSpanMethod"
    >
      <el-table-column label="Title" align="center" prop="title" />
      <el-table-column label="Keywords" align="center">
        <template #default="scope">
          <span v-if="scope.row.keywords && scope.row.keywords.length > 0">
            <el-tag v-for="(keyword, index) in scope.row.keywords" :key="index" class="mr-5px">
              {{ keyword }}
            </el-tag>
          </span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="Difficulty Level" align="center">
        <template #default="scope">
          <span>{{ getDifficultyLabel(scope.row.level) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="Description" align="center" prop="introduction" />

      <!-- 技能层级列 -->
      <el-table-column label="一级技能" align="center" prop="firstName" />
      <el-table-column label="二级技能" align="center" prop="secondName" />
      <el-table-column label="三级技能" align="center">
        <template #default="scope">
          <el-tag v-if="scope.row.skillName" size="small" type="info">
            {{ scope.row.skillName }}
          </el-tag>
          <span v-else>-</span>
        </template>
      </el-table-column>

      <!-- 匹配程度和原因只在技能层级显示 -->
      <el-table-column label="Match Degree" align="center">
        <template #default="scope">
          <span v-if="scope.row.matchDegree">{{ scope.row.matchDegree }}%</span>
          <span v-else>-</span>
        </template>
      </el-table-column>

      <el-table-column label="Match Reason" align="center">
        <template #default="scope">
          <span v-if="scope.row.matchReason">{{ scope.row.matchReason }}</span>
          <span v-else>-</span>
        </template>
      </el-table-column>

      <el-table-column label="Actions" align="center" min-width="200px">
        <template #default="scope">
          <el-button link type="primary" @click="handleMatch(scope.row)" :loading="matchLoading">
            {{ scope.row.matchDegree ? 'Re-match' : 'Match' }}
          </el-button>
          <el-button
            v-if="scope.row.matchDegree && scope.row.pendingMatch"
            link
            type="success"
            @click="handleConfirm(scope.row)"
            :loading="confirmLoading"
          >
            Confirm
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- Form Dialog: Add/Edit -->
  <LearningContentRecommendForm ref="formRef" @success="getList" />

  <!-- Unmatched Content Dialog -->
  <UnmatchedContentDialog v-model="unmatchedDialogVisible" @refresh="getList" />

  <!-- Match Result Dialog -->
  <MatchResultDialog
    v-model="matchResultDialogVisible"
    :match-result="currentMatchResult"
    @confirmed="handleMatchConfirmed"
  />
</template>

<script setup lang="ts">
import { DICT_TYPE, getDictLabel } from '@/utils/dict'
import {
  LearningContentRecommendApi,
  LearningContentRecommendVO,
  LearningContentMatchVO,
  MatchResultVO
} from '@/api/edp/learningcontentrecommend'
import LearningContentRecommendForm from './LearningContentRecommendForm.vue'
import UnmatchedContentDialog from './UnmatchedContentDialog.vue'
import MatchResultDialog from './MatchResultDialog.vue'
import { ElMessage } from 'element-plus'

/** Learning Content Recommendation List */
defineOptions({ name: 'LearningContentRecommend' })

// 扩展 LearningContentRecommendVO 接口，添加 skills 字段
interface ExtendedLearningContentRecommendVO extends LearningContentRecommendVO {
  skills?: {
    [firstId: string]: {
      [secondId: string]: Array<{
        id: number
        name: string
        matchDegree: number
        matchReason: string
      }>
    }
  }
}

// Flattened data interface
interface FlatItem {
  id: string
  bizType?: number
  bizId?: number
  title: string
  keywords: string[]
  level: number | null
  introduction: string
  firstName?: string
  secondName?: string
  skillName?: string
  matchDegree?: number
  matchReason?: string
  pendingMatch?: boolean // Whether there is a pending match result
}

const loading = ref(true) // List loading status
const list = ref<ExtendedLearningContentRecommendVO[]>([]) // List data
const flatList = ref<FlatItem[]>([]) // Flattened data structure
const total = ref(0) // Total pages
const matchLoading = ref(false) // Match operation loading
const confirmLoading = ref(false) // Confirm operation loading
const unmatchedDialogVisible = ref(false) // Unmatched content dialog visibility
const matchResultDialogVisible = ref(false) // Match result dialog visibility
const currentMatchResult = ref<MatchResultVO | null>(null) // Current match result
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  bizType: 10, // 类型为课程
  bizId: undefined,
  level: undefined,
  title: undefined,
  keywords: undefined,
  skillName: undefined, // Skill name search
  degreeRangeIndex: undefined, // Match degree min value
  degreeRangeOffset: undefined, // Match degree max value
  type: undefined,
  typeId: undefined,
  skillId: undefined,
  introduction: undefined,
  matchDegree: undefined,
  matchReason: undefined,
  status: undefined,
  createTime: []
})
const queryFormRef = ref() // Search form reference

// 难度级别字典
const difficultyOptions = ref([
  { label: '初级', value: 1 },
  { label: '中级', value: 2 },
  { label: '高级', value: 3 }
])

// 获取难度级别标签
const getDifficultyLabel = (level: number | null) => {
  if (!level) return '-'
  const option = difficultyOptions.value.find((item) => item.value === level)
  return option ? option.label : level
}

// 将数据转换为平铺结构
const flattenData = (contentData: ExtendedLearningContentRecommendVO[]): FlatItem[] => {
  const result: FlatItem[] = []
  console.log('contentData', contentData)
  contentData.forEach((content) => {
    if (!content.skills || Object.keys(content.skills).length === 0) {
      // 没有技能数据的内容直接添加
      result.push({
        id: `content_${content.typeId || 0}`,
        bizType: content.bizType,
        bizId: content.bizId,
        title: content.title || '',
        keywords: Array.isArray(content.keywords) ? content.keywords : [],
        level: content.level,
        introduction: content.introduction || ''
      })
    } else {
      // 处理有技能数据的内容
      Object.entries(content.skills).forEach(([firstLevelSkillKey, firstSkills]) => {
        // 一级技能是否有二级技能
        const hasSecondLevel = Object.keys(firstSkills).length > 0

        if (!hasSecondLevel) {
          // 只有一级技能
          result.push({
            id: `first_${content.typeId}_${firstLevelSkillKey}`,
            bizType: content.bizType,
            bizId: content.bizId,
            title: content.title || '',
            keywords: Array.isArray(content.keywords) ? content.keywords : [],
            level: content.level,
            introduction: content.introduction || '',
            firstName: getDictLabel(DICT_TYPE.EDP_FIRST_LEVEL_SKILL, firstLevelSkillKey)
          })
        } else {
          // 处理二级技能
          Object.entries(firstSkills).forEach(([secondLevelSkillKey, skills]) => {
            if (!skills || skills.length === 0) {
              // 只有二级技能，没有三级技能
              result.push({
                id: `second_${content.typeId}_${firstLevelSkillKey}_${secondLevelSkillKey}`,
                bizType: content.bizType,
                bizId: content.bizId,
                title: content.title || '',
                keywords: Array.isArray(content.keywords) ? content.keywords : [],
                level: content.level,
                introduction: content.introduction || '',
                firstName: getDictLabel(DICT_TYPE.EDP_FIRST_LEVEL_SKILL, firstLevelSkillKey),
                secondName: getDictLabel(DICT_TYPE.EDP_SECOND_LEVEL_SKILL, secondLevelSkillKey)
              })
            } else {
              // 处理三级技能
              skills.forEach((skill) => {
                result.push({
                  id: `skill_${content.typeId}_${firstLevelSkillKey}_${secondLevelSkillKey}_${skill.id}`,
                  bizType: content.bizType,
                  bizId: content.bizId,
                  title: content.title || '',
                  keywords: Array.isArray(content.keywords) ? content.keywords : [],
                  level: content.level,
                  introduction: content.introduction || '',
                  firstName: getDictLabel(DICT_TYPE.EDP_FIRST_LEVEL_SKILL, firstLevelSkillKey),
                  secondName: getDictLabel(DICT_TYPE.EDP_SECOND_LEVEL_SKILL, secondLevelSkillKey),
                  skillName: skill.name,
                  matchDegree: skill.matchDegree,
                  matchReason: skill.matchReason
                })
              })
            }
          })
        }
      })
    }
  })
  console.log('result', result)
  return result
}

// Cell merge method
const objectSpanMethod = ({ rowIndex, columnIndex }) => {
  // Object to store merge information
  const titleSpanArr: number[] = []
  const firstSkillSpanArr: number[] = []
  const secondSkillSpanArr: number[] = []

  let titlePos = 0
  let firstSkillPos = 0
  let secondSkillPos = 0

  // Calculate merge information for each column
  flatList.value.forEach((item, index) => {
    // Title column
    if (index === 0) {
      titleSpanArr.push(1)
      titlePos = 0
    } else {
      if (item.title === flatList.value[titlePos].title) {
        titleSpanArr[titlePos] += 1
        titleSpanArr.push(0)
      } else {
        titleSpanArr.push(1)
        titlePos = index
      }
    }

    // First level skill column
    if (index === 0 || !item.firstName) {
      firstSkillSpanArr.push(item.firstName ? 1 : 0)
      firstSkillPos = index
    } else {
      if (
        item.firstName &&
        item.firstName === flatList.value[firstSkillPos].firstName &&
        item.title === flatList.value[firstSkillPos].title
      ) {
        firstSkillSpanArr[firstSkillPos] += 1
        firstSkillSpanArr.push(0)
      } else {
        firstSkillSpanArr.push(item.firstName ? 1 : 0)
        firstSkillPos = index
      }
    }

    // Second level skill column
    if (index === 0 || !item.secondName) {
      secondSkillSpanArr.push(item.secondName ? 1 : 0)
      secondSkillPos = index
    } else {
      if (
        item.secondName &&
        item.secondName === flatList.value[secondSkillPos].secondName &&
        item.firstName === flatList.value[secondSkillPos].firstName &&
        item.title === flatList.value[secondSkillPos].title
      ) {
        secondSkillSpanArr[secondSkillPos] += 1
        secondSkillSpanArr.push(0)
      } else {
        secondSkillSpanArr.push(item.secondName ? 1 : 0)
        secondSkillPos = index
      }
    }
  })

  // Return corresponding merge information based on column index
  if (columnIndex === 0) {
    // Title column
    const rowspan = titleSpanArr[rowIndex]
    const colspan = rowspan > 0 ? 1 : 0
    return { rowspan, colspan }
  } else if (columnIndex === 1) {
    // Keywords column
    const rowspan = titleSpanArr[rowIndex]
    const colspan = rowspan > 0 ? 1 : 0
    return { rowspan, colspan }
  } else if (columnIndex === 2) {
    // Difficulty level column
    const rowspan = titleSpanArr[rowIndex]
    const colspan = rowspan > 0 ? 1 : 0
    return { rowspan, colspan }
  } else if (columnIndex === 3) {
    // Description column
    const rowspan = titleSpanArr[rowIndex]
    const colspan = rowspan > 0 ? 1 : 0
    return { rowspan, colspan }
  } else if (columnIndex === 4) {
    // First level skill column
    const rowspan = firstSkillSpanArr[rowIndex]
    const colspan = rowspan > 0 ? 1 : 0
    return { rowspan, colspan }
  } else if (columnIndex === 5) {
    // Second level skill column
    const rowspan = secondSkillSpanArr[rowIndex]
    const colspan = rowspan > 0 ? 1 : 0
    return { rowspan, colspan }
  } else if (columnIndex === 9) {
    // Actions column
    const rowspan = titleSpanArr[rowIndex]
    const colspan = rowspan > 0 ? 1 : 0
    return { rowspan, colspan }
  }
}

// AI match handler
const handleMatch = async (row) => {
  try {
    matchLoading.value = true
    console.log('Matching...', row)
    // Use bizType and bizId directly from the row data
    const params: LearningContentMatchVO = {
      bizType: row.bizType || queryParams.bizType, // Fallback to queryParams if not available
      bizId: row.bizId, // Fallback to function if not available
      isNew: false // For existing content, set isNew to false
    }

    const result = await LearningContentRecommendApi.matchLearningContent(params)

    // Show match result dialog
    currentMatchResult.value = result
    matchResultDialogVisible.value = true
  } catch (error) {
    ElMessage.error('Failed to match content')
    console.error('Failed to match content:', error)
  } finally {
    matchLoading.value = false
  }
}

// Handle match result confirmation
const handleMatchConfirmed = (kept: boolean) => {
  if (kept) {
    // If kept, refresh the list to show updated match results
    getList()
    ElMessage.success('Match result has been saved successfully')
  } else {
    ElMessage.info('Match result has been discarded')
  }

  // Reset match result
  currentMatchResult.value = null
}

// Confirm match result
const handleConfirm = async (row) => {
  try {
    confirmLoading.value = true

    const idParts = row.id.toString().split('_')
    const confirmData = {
      typeId: Number(idParts[1]),
      skillId: idParts.length > 3 ? Number(idParts[3]) : undefined,
      matchDegree: row.matchDegree,
      matchReason: row.matchReason
    }

    // Simulate API call - need to call actual API in real project
    console.log('Confirm match data:', confirmData)

    // Update status, remove pending confirmation mark
    const currentRow = flatList.value.find((item) => item.id === row.id)
    if (currentRow) {
      currentRow.pendingMatch = false
    }

    ElMessage.success('Match result confirmed and saved')
  } catch (error: any) {
    ElMessage.error('Confirmation failed: ' + (error?.message || 'Unknown error'))
  } finally {
    confirmLoading.value = false
  }
}

// Handle unmatched content
const handleUnmatchedContent = () => {
  unmatchedDialogVisible.value = true
}

/** Query list */
const getList = async () => {
  loading.value = true
  try {
    const data = await LearningContentRecommendApi.getLearningContentRecommendPage(queryParams)
    list.value = data.list
    total.value = data.total
    console.log('data', data)
    // Convert to flattened structure
    flatList.value = flattenData(data.list)
  } finally {
    loading.value = false
  }
}

/** Search button operation */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** Reset button operation */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** Initialize */
onMounted(() => {
  getList()
})
</script>
