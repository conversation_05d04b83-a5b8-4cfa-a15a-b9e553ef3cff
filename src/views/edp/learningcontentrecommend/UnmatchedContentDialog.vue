<template>
  <el-dialog
    v-model="visible"
    title="Unmatched Content Management"
    width="80%"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="unmatched-content-dialog">
      <!-- Search Bar -->
      <el-form :inline="true" class="mb-4">
        <el-form-item label="Content Title">
          <el-input v-model="queryForm.title" placeholder="Enter content title" />
        </el-form-item>
        <el-form-item label="Keywords">
          <el-input v-model="queryForm.keywords" placeholder="Enter keywords" class="w-200px" />
        </el-form-item>
        <el-form-item label="Content Type">
          <el-select
            v-model="queryForm.type"
            placeholder="Select content type"
            clearable
            class="w-150px"
          >
            <el-option
              v-for="option in contentTypeOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button @click="handleSearch">
            <Icon icon="ep:search" class="mr-1" /> Search
          </el-button>
          <el-button @click="handleReset">
            <Icon icon="ep:refresh" class="mr-1" /> Reset
          </el-button>
        </el-form-item>
      </el-form>

      <!-- Unmatched Content List -->
      <el-table
        v-loading="loading"
        :data="unmatchedList"
        border
        stripe
        max-height="400px"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column label="Content Title" prop="title" min-width="200" />
        <!-- <el-table-column label="Content Type" prop="type" width="100">
          <template #default="{ row }">
            <el-tag :type="getTypeTagType(row.type)">
              {{ getTypeLabel(row.type) }}
            </el-tag>
          </template>
        </el-table-column> -->
        <el-table-column label="Keywords" min-width="150">
          <template #default="{ row }">
            <span v-if="row.keywords && row.keywords.length > 0">
              <el-tag
                v-for="keyword in row.keywords.slice(0, 3)"
                :key="keyword"
                size="small"
                class="mr-1"
              >
                {{ keyword }}
              </el-tag>
              <span v-if="row.keywords.length > 3" class="text-gray-400">
                +{{ row.keywords.length - 3 }}
              </span>
            </span>
            <span v-else class="text-gray-400">-</span>
          </template>
        </el-table-column>
        <!-- <el-table-column label="Difficulty Level" prop="level" width="100">
          <template #default="{ row }">
            {{ getDifficultyLabel(row.level) }}
          </template>
        </el-table-column> -->
        <el-table-column label="Actions" width="150">
          <template #default="{ row }">
            <el-button
              link
              type="primary"
              @click="handleMatchSingleLearningContent(row)"
              :loading="row.matching"
            >
              <Icon icon="ep:connection" class="mr-1" />
              Match
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      
    </div>

    <!-- Footer Action Buttons -->
    <template #footer>
      <!-- Pagination -->
      <div class="flex justify-center mt-4">
        <el-pagination
          v-model:current-page="pagination.pageNo"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
      <!-- <div class="flex justify-end items-center">
        <div>
          <el-button
            type="primary"
            @click="handleBatchMatch"
            :loading="batchMatching"
            :disabled="selectedRows.length === 0"
          >
            <Icon icon="ep:connection" class="mr-1" />
            Batch Match Selected ({{ selectedRows.length }})
          </el-button>
        </div>
        <div>
          <el-button @click="handleClose">Cancel</el-button>
          <el-button type="primary" @click="handleClose">Confirm</el-button>
        </div>
      </div> -->
    </template>
  </el-dialog>

  <!-- Match Result Dialog -->
  <MatchResultDialog
    v-model="matchResultVisible"
    :match-result="currentMatchResult"
    @confirmed="handleMatchConfirmed"
  />
</template>

<script setup lang="ts">
import { ElMessage } from 'element-plus'
import MatchResultDialog from './MatchResultDialog.vue'
import { Icon } from '@/components/Icon'
// apis
import {
  LearningContentRecommendApi,
  RecommendContentTypeEnum,
  LearningContentMatchVO,
  MatchResultVO
} from '@/api/edp/learningcontentrecommend'

interface UnmatchedContent {
  bizId: number
  bizType: number
  title: string
  keywords: string[]
  level: number
  matching?: boolean
}

// Props and Emits
const props = defineProps<{
  modelValue: boolean
}>()

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  refresh: []
}>()

// Reactive data
const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const loading = ref(false)
const unmatchedList = ref<UnmatchedContent[]>([])
const selectedRows = ref<UnmatchedContent[]>([])

// Match result dialog
const matchResultVisible = ref(false)
const currentMatchResult = ref<MatchResultVO | null>(null)

// Search conditions
const queryForm = reactive<{
  title: string
  keywords: string
  type: RecommendContentTypeEnum | ''
}>({
  title: '',
  keywords: '',
  type: ''
})

// Pagination data
const pagination = reactive({
  pageNo: 1,
  pageSize: 10,
  total: 0
})

// content type options
const contentTypeOptions = computed(() => [
  { label: 'Course', value: RecommendContentTypeEnum.ONLINE_TRAINING },
  { label: 'Training', value: RecommendContentTypeEnum.MLC_TRAINING }
])

// Get unmatched content list
const getUnmatchedList = async () => {
  loading.value = true
  try {
    if (queryForm.type === '') {
      queryForm.type = RecommendContentTypeEnum.ONLINE_TRAINING
    }
    // Simulate API call - need to call actual API in real project
    const params = {
      pageNo: pagination.pageNo,
      pageSize: pagination.pageSize,
      title: queryForm.title || undefined,
      keywords: queryForm.keywords || undefined,
      bizType: queryForm.type || undefined
    }
    const data = await LearningContentRecommendApi.getNewLearningContentList(params)
    unmatchedList.value = data.list.map((item) => ({
      ...item,
      keywords: item.keywords ? item.keywords.split('，') : [] // Split the comma-separated string into an array
    }))
    pagination.total = data.total
  } catch (error) {
    ElMessage.error('Failed to get unmatched content')
    console.error('Failed to get unmatched content:', error)
  } finally {
    loading.value = false
  }
}

const handleMatchSingleLearningContent = async (row: UnmatchedContent) => {
  try {
    // Add loading state to the row
    row.matching = true

    const params: LearningContentMatchVO = {
      bizType: row.bizType,
      bizId: row.bizId,
      isNew: true
    }

    const result = await LearningContentRecommendApi.matchLearningContent(params)

    // Show match result dialog
    currentMatchResult.value = result
    matchResultVisible.value = true
  } catch (error) {
    ElMessage.error('Failed to match content')
    console.error('Failed to match content:', error)
  } finally {
    row.matching = false
  }
}

// Handle match result confirmation
const handleMatchConfirmed = (kept: boolean) => {
  if (kept) {
    // If kept, refresh the list to remove the matched content
    getUnmatchedList()
  }
  // Always emit refresh to parent component
  emit('refresh')

  // Reset match result
  currentMatchResult.value = null
}

// Search operation
const handleSearch = () => {
  pagination.pageNo = 1
  getUnmatchedList()
}

// Reset search
const handleReset = () => {
  queryForm.title = ''
  queryForm.keywords = ''
  queryForm.type = ''
  pagination.pageNo = 1
  getUnmatchedList()
}

// Pagination operations
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  getUnmatchedList()
}

const handleCurrentChange = (page: number) => {
  pagination.pageNo = page
  getUnmatchedList()
}

// Batch match operation
// const handleBatchMatch = async () => {
//   if (selectedRows.value.length === 0) {
//     ElMessage.warning('Please select content to match')
//     return
//   }

//   try {
//     const confirmResult = await ElMessageBox.confirm(
//       `Are you sure you want to batch match the selected ${selectedRows.value.length} items?`,
//       'Batch Match Confirmation',
//       {
//         confirmButtonText: 'Confirm',
//         cancelButtonText: 'Cancel',
//         type: 'warning'
//       }
//     )

//     if (confirmResult === 'confirm') {
//       batchMatching.value = true

//       // Simulate batch match API call
//       for (const content of selectedRows.value) {
//         content.matching = true

//         // Simulate API call delay
//         await new Promise((resolve) => setTimeout(resolve, 1000))

//         content.matching = false
//       }

//       ElMessage.success(`Successfully matched ${selectedRows.value.length} items`)
//       emit('refresh')
//       getUnmatchedList() // Refresh list
//     }
//   } catch (error) {
//     // User cancelled operation
//   } finally {
//     batchMatching.value = false
//   }
// }

// Skill selection confirmation
// const handleSkillSelect = async (skillData: any) => {
//   if (!currentMatchContent.value) return

//   try {
//     currentMatchContent.value.matching = true

//     // Simulate AI match API call
//     const matchData = {
//       contentId: currentMatchContent.value.id,
//       skillId: skillData.skillId,
//       skillName: skillData.skillName
//     }

//     console.log('Match data:', matchData)

//     // Simulate API call
//     await new Promise((resolve) => setTimeout(resolve, 2000))

//     ElMessage.success(`Successfully matched content to skill: ${skillData.skillName}`)
//     emit('refresh')
//     getUnmatchedList() // Refresh list
//   } catch (error) {
//     ElMessage.error('Matching failed')
//     console.error('Matching failed:', error)
//   } finally {
//     if (currentMatchContent.value) {
//       currentMatchContent.value.matching = false
//     }
//     currentMatchContent.value = null
//   }
// }

// Table selection change
const handleSelectionChange = (selection: UnmatchedContent[]) => {
  selectedRows.value = selection
}

// Close dialog
const handleClose = () => {
  visible.value = false
  // Reset data
  queryForm.title = ''
  queryForm.keywords = ''
  queryForm.type = ''
  pagination.pageNo = 1
  selectedRows.value = []
}

// Watch dialog visibility
watch(visible, (newVal) => {
  if (newVal) {
    if (queryForm.type === undefined) {
      queryForm.type = RecommendContentTypeEnum.ONLINE_TRAINING
    }
    getUnmatchedList()
  }
})
</script>

<style scoped>
.unmatched-content-dialog {
  min-height: 500px;
}

.w-200px {
  width: 200px;
}

.w-150px {
  width: 150px;
}
</style>
