<template>
  <el-dialog
    v-model="visible"
    title="Content Matching Result"
    width="800px"
    :before-close="handleCancel"
    destroy-on-close
  >
    <div v-if="matchResult" class="match-result-dialog">
      <!-- Content Info -->
      <div class="mb-6">
        <h3 class="text-lg font-semibold mb-3">{{ matchResult.contentSkills.content_name }}</h3>
        <div class="bg-gray-50 p-4 rounded-lg">
          <div class="mb-2">
            <span class="font-medium">Keywords:</span>
            <span class="text-gray-700">{{ matchResult.keywords }}</span>
          </div>
          <div class="mb-2">
            <span class="font-medium">Level:</span>
            <el-tag :type="getLevelType(matchResult.level)" size="small">
              {{ getLevelText(matchResult.level) }}
            </el-tag>
          </div>
          <div>
            <span class="font-medium">Introduction:</span>
            <p class="text-gray-700 mt-1 leading-relaxed">{{ matchResult.introduction }}</p>
          </div>
        </div>
      </div>

      <!-- Skill Tags -->
      <div class="mb-6">
        <h4 class="text-md font-semibold mb-3">Matched Skills</h4>
        <div class="space-y-3">
          <div
            v-for="(skill, index) in matchResult.contentSkills.skill_tags"
            :key="index"
            class="border border-gray-200 rounded-lg p-4"
          >
            <div class="flex items-center justify-between mb-2">
              <h5 class="font-medium text-gray-900">{{ skill.skill_tag }}</h5>
              <div class="flex items-center">
                <span class="text-sm text-gray-500 mr-2">Compatibility:</span>
                <el-tag :type="getScoreType(skill.compatibility_score)" size="small">
                  {{ (skill.compatibility_score * 100).toFixed(0) }}%
                </el-tag>
              </div>
            </div>
            <div class="text-sm text-gray-600">
              <span class="font-medium">Reason:</span> {{ skill.reason }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="flex justify-end space-x-3">
        <el-button @click="handleDiscard" :loading="confirmLoading">
          <Icon icon="ep:delete" class="mr-1" />
          Discard
        </el-button>
        <el-button type="primary" @click="handleKeep" :loading="confirmLoading">
          <Icon icon="ep:check" class="mr-1" />
          Keep
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ElMessage } from 'element-plus'
import { Icon } from '@/components/Icon'
import {
  MatchResultVO,
  MatchConfirmVO,
  LearningContentRecommendApi,
  DifficultyLevelEnum
} from '@/api/edp/learningcontentrecommend'

// Props and Emits
const props = defineProps<{
  modelValue: boolean
  matchResult: MatchResultVO | null
}>()

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  confirmed: [kept: boolean]
}>()

// Reactive data
const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const confirmLoading = ref(false)

// Methods
const getLevelText = (level: number) => {
  switch (level) {
    case DifficultyLevelEnum.BEGINNER:
      return 'Beginner'
    case DifficultyLevelEnum.INTERMEDIATE:
      return 'Intermediate'
    case DifficultyLevelEnum.ADVANCED:
      return 'Advanced'
    default:
      return 'Suitable for all'
  }
}

const getLevelType = (level: number) => {
  switch (level) {
    case DifficultyLevelEnum.BEGINNER:
      return 'success'
    case DifficultyLevelEnum.INTERMEDIATE:
      return 'warning'
    case DifficultyLevelEnum.ADVANCED:
      return 'danger'
    default:
      return 'info'
  }
}

const getScoreType = (score: number) => {
  if (score >= 0.9) return 'success'
  if (score >= 0.7) return 'warning'
  return 'info'
}

const confirmMatch = async (keepFlag: boolean) => {
  if (!props.matchResult) return

  confirmLoading.value = true
  try {
    const params: MatchConfirmVO = {
      keepFlag,
      isNew: props.matchResult.isNew,
      uuid: props.matchResult.uuid,
      bizType: props.matchResult.bizType,
      bizId: props.matchResult.bizId
    }

    await LearningContentRecommendApi.confirmMatchResult(params)

    ElMessage.success(keepFlag ? 'Match result saved successfully' : 'Match result discarded')

    emit('confirmed', keepFlag)
    visible.value = false
  } catch (error) {
    ElMessage.error('Failed to confirm match result')
    console.error('Failed to confirm match result:', error)
  } finally {
    confirmLoading.value = false
  }
}

const handleKeep = () => {
  confirmMatch(true)
}

const handleDiscard = () => {
  confirmMatch(false)
}

const handleCancel = () => {
  // 根据文档要求，取消也视为丢弃
  confirmMatch(false)
}
</script>

<style scoped>
.match-result-dialog {
  max-height: 600px;
  overflow-y: auto;
}
</style>
