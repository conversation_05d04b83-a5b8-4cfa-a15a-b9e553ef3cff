<template>
  <el-dialog
    v-model="visible"
    title="Select Matching Skill"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="skill-select-dialog">
      <div class="mb-4">
        <h4 class="text-lg font-medium mb-2">Current Content Information</h4>
        <div class="bg-gray-50 p-4 rounded">
          <p><strong>Title:</strong> {{ currentContent?.title }}</p>
          <p><strong>Type:</strong> {{ getTypeLabel(currentContent?.type) }}</p>
          <p v-if="currentContent?.keywords?.length">
            <strong>Keywords:</strong>
            <el-tag
              v-for="keyword in currentContent.keywords"
              :key="keyword"
              size="small"
              class="mr-1"
            >
              {{ keyword }}
            </el-tag>
          </p>
        </div>
      </div>

      <div class="mb-4">
        <h4 class="text-lg font-medium mb-2">Select Matching Skill</h4>
        <el-cascader
          v-model="selectedSkill"
          :options="skillOptions"
          :props="skillProps"
          placeholder="Please select the skill to match"
          clearable
          filterable
          class="w-full"
        />
      </div>

      <!-- AI Matching Suggestions -->
      <div v-if="aiSuggestions.length > 0" class="mb-4">
        <h4 class="text-lg font-medium mb-2">AI Recommended Skills</h4>
        <div class="space-y-2">
          <div
            v-for="suggestion in aiSuggestions"
            :key="suggestion.skillId"
            class="border rounded p-3 cursor-pointer hover:bg-blue-50 transition-colors"
            :class="{ 'border-blue-400 bg-blue-50': selectedSkill === suggestion.skillId }"
            @click="selectedSkill = suggestion.skillId"
          >
            <div class="flex items-center justify-between">
              <div>
                <span class="font-medium">{{ suggestion.skillName }}</span>
                <span class="text-sm text-gray-500 ml-2"> ({{ suggestion.skillPath }}) </span>
              </div>
              <el-tag :type="getMatchLevelType(suggestion.matchLevel)" size="small">
                {{ suggestion.matchLevel }}% Match
              </el-tag>
            </div>
            <p class="text-sm text-gray-600 mt-1">{{ suggestion.reason }}</p>
          </div>
        </div>
      </div>

      <!-- If no AI suggestions -->
      <div v-else class="text-center py-4">
        <el-button @click="getAISuggestions" :loading="loadingSuggestions">
          <Icon icon="ep:magic-stick" class="mr-1" />
          Get AI Recommendations
        </el-button>
      </div>
    </div>

    <template #footer>
      <div class="flex justify-end space-x-2">
        <el-button @click="handleClose">Cancel</el-button>
        <el-button
          type="primary"
          @click="handleConfirm"
          :disabled="!selectedSkill"
          :loading="confirming"
        >
          Confirm Match
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ElMessage } from 'element-plus'

interface SkillSuggestion {
  skillId: number
  skillName: string
  skillPath: string
  matchLevel: number
  reason: string
}

interface ContentInfo {
  id: number
  title: string
  type: number
  keywords: string[]
  introduction?: string
}

// Props 和 Emits
const props = defineProps<{
  modelValue: boolean
  currentContent: ContentInfo | null
}>()

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  confirm: [data: { skillId: number; skillName: string; skillPath: string }]
}>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const currentContent = computed(() => props.currentContent)
const selectedSkill = ref<number | undefined>(undefined)
const loadingSuggestions = ref(false)
const confirming = ref(false)
const aiSuggestions = ref<SkillSuggestion[]>([])

// Skill options data (consistent with main component)
const skillOptions = ref([
  {
    id: 1,
    name: 'Programming Skills',
    children: [
      {
        id: 11,
        name: 'Java Development',
        children: [
          { id: 111, name: 'Spring Framework' },
          { id: 112, name: 'MyBatis' }
        ]
      },
      {
        id: 12,
        name: 'Python Development',
        children: [
          { id: 121, name: 'Django Framework' },
          { id: 122, name: 'Flask Framework' }
        ]
      }
    ]
  },
  {
    id: 2,
    name: 'Data Skills',
    children: [
      {
        id: 21,
        name: 'Data Analysis',
        children: [
          { id: 211, name: 'Excel Analysis' },
          { id: 212, name: 'SQL Query' }
        ]
      }
    ]
  }
])

const skillProps = {
  value: 'id',
  label: 'name',
  children: 'children',
  emitPath: false,
  checkStrictly: true
}

// Content type labels
const typeLabels = {
  1: 'Course',
  2: 'Video',
  3: 'Document'
}

const getTypeLabel = (type?: number) => {
  return type ? typeLabels[type] || type : '-'
}

// 匹配度等级类型
const getMatchLevelType = (level: number) => {
  if (level >= 80) return 'success'
  if (level >= 60) return 'warning'
  return 'info'
}

// 根据技能ID查找技能路径
const findSkillPath = (skillId: number, options: any[] = skillOptions.value, path = ''): string => {
  for (const option of options) {
    const currentPath = path ? `${path} > ${option.name}` : option.name

    if (option.id === skillId) {
      return currentPath
    }

    if (option.children) {
      const childPath = findSkillPath(skillId, option.children, currentPath)
      if (childPath) {
        return childPath
      }
    }
  }
  return ''
}

// 根据技能ID查找技能名称
const findSkillName = (skillId: number, options: any[] = skillOptions.value): string => {
  for (const option of options) {
    if (option.id === skillId) {
      return option.name
    }

    if (option.children) {
      const childName = findSkillName(skillId, option.children)
      if (childName) {
        return childName
      }
    }
  }
  return ''
}

// Get AI recommendations
const getAISuggestions = async () => {
  if (!currentContent.value) return

  loadingSuggestions.value = true
  try {
    // Simulate AI recommendation API call
    const mockSuggestions: SkillSuggestion[] = []

    // Generate mock recommendations based on content keywords
    if (currentContent.value.keywords?.includes('Vue')) {
      mockSuggestions.push({
        skillId: 12,
        skillName: 'Python Development',
        skillPath: 'Programming Skills > Python Development',
        matchLevel: 85,
        reason:
          'Content contains Vue-related tech stack, highly matches frontend development skills'
      })
    }

    if (currentContent.value.keywords?.includes('Python')) {
      mockSuggestions.push({
        skillId: 121,
        skillName: 'Django Framework',
        skillPath: 'Programming Skills > Python Development > Django Framework',
        matchLevel: 92,
        reason:
          'Content focuses on Python technology, perfectly matches Django framework development skills'
      })
    }

    if (currentContent.value.keywords?.includes('Data Analysis')) {
      mockSuggestions.push({
        skillId: 211,
        skillName: 'Excel Analysis',
        skillPath: 'Data Skills > Data Analysis > Excel Analysis',
        matchLevel: 88,
        reason:
          'Content involves data analysis technology, highly relevant to Excel analysis skills'
      })
    }

    // Default recommendations
    if (mockSuggestions.length === 0) {
      mockSuggestions.push(
        {
          skillId: 111,
          skillName: 'Spring Framework',
          skillPath: 'Programming Skills > Java Development > Spring Framework',
          matchLevel: 75,
          reason: 'Based on content analysis, this skill has certain relevance with course content'
        },
        {
          skillId: 212,
          skillName: 'SQL Query',
          skillPath: 'Data Skills > Data Analysis > SQL Query',
          matchLevel: 68,
          reason: 'Content may involve data processing, SQL skills have universality'
        }
      )
    }

    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 1500))

    aiSuggestions.value = mockSuggestions.sort((a, b) => b.matchLevel - a.matchLevel)
    ElMessage.success('AI recommendations retrieved successfully')
  } catch (error) {
    ElMessage.error('Failed to get AI recommendations')
    console.error('Failed to get AI recommendations:', error)
  } finally {
    loadingSuggestions.value = false
  }
}

// Confirm selection
const handleConfirm = async () => {
  if (!selectedSkill.value) {
    ElMessage.warning('Please select a skill to match')
    return
  }

  confirming.value = true
  try {
    const skillName = findSkillName(selectedSkill.value)
    const skillPath = findSkillPath(selectedSkill.value)

    emit('confirm', {
      skillId: selectedSkill.value,
      skillName,
      skillPath
    })

    handleClose()
  } catch (error) {
    ElMessage.error('Confirmation failed')
    console.error('Confirmation failed:', error)
  } finally {
    confirming.value = false
  }
}

// Close dialog
const handleClose = () => {
  visible.value = false
  selectedSkill.value = undefined
  aiSuggestions.value = []
}

// Watch dialog visibility
watch(visible, (newVal) => {
  if (newVal && currentContent.value) {
    // Automatically get AI recommendations
    nextTick(() => {
      getAISuggestions()
    })
  }
})
</script>

<style scoped>
.skill-select-dialog {
  max-height: 600px;
  overflow-y: auto;
}
</style>
