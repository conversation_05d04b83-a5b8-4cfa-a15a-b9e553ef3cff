<script lang="ts" setup>
import { onMounted, ref, computed } from 'vue'
import { PositionSkillReqVO, SkillApi } from '@/api/edp/skillTags'
import { useUserStore } from '@/store/modules/user'
import { useRoute } from 'vue-router'
import avatarImg from '@/assets/imgs/avatar.gif'
import { generateUUID } from '@/utils'
import MarkdownIt from 'markdown-it'
import markdownItSanitizer from 'markdown-it-sanitizer'
import { ElMessage } from 'element-plus'
import { Source, ChatTypes } from '@/enums/edp'
import Generate from '/src/assets/svgs/generate.svg'
import { useRouteQuery } from '@vueuse/router'
import * as _ from 'lodash-es'
import {
  DICT_TYPE,
  DictDataType,
  getDictKeyByLabel,
  getDictLabel,
  getDictOptions
} from '@/utils/dict'

// 核心技能结构
export interface Skill {
  id?: number
  name: string
  status: boolean
  updateTime?: number
  repeat?: boolean // 是否重复
}

defineOptions({ name: 'SkillGenerate' })

const firstLevelSkillOptions = getDictOptions(DICT_TYPE.EDP_FIRST_LEVEL_SKILL) as DictDataType[]

// 一级技能结构
export type FirstLevelSkills = Record<number, SecondLevelSkills>

// 二级技能结构
export type SecondLevelSkills = Record<number, Skill[]>

/* SET UP */
const route = useRoute()
const userStore = useUserStore()
const avatar = computed(() => userStore.user.avatar || avatarImg)
const userName = computed(() => userStore.user.nickname ?? 'Admin')
const conversationId = ref<number>() // 会话ID
const chatId = ref<number>() // 聊天ID
const welcome = ref<string>('') // 欢迎语
const jdId = ref(Number(route.query.jdId))
const skillId = ref<number>()
const positionId = ref<number>(0)
const positionName = ref((route.query.positionName as string) || '')
const deptName = ref((route.query.deptName as string) || '')
const positionReportsTo = ref<string>('')
const currentFirstSkillKey = ref<number>()
const secondLevelSkillInputModel = ref<Record<number, string>>({})
const shouldCreateChat = ref(route.query.createChat === 'true')
const loading = ref(true) // 加载状态
const historyLoading = ref(true) // 加载状态
const sending = ref(false) // 发送状态
const conversationInProgress = ref(false) // 对话是否正在进行中。目前只有【发送】消息时，会更新为 true，避免切换对话、删除对话等操作
const conversationInAbortController = ref<any>() // 对话进行中 abort 控制器(控制 stream 对话)
const hasData = ref(false) // 是否有数据标志
const chatScrollRef = ref<any>(null) // 聊天区域滚动引用
const typingQueue = ref<string[]>([]) // 打字队列
const isTyping = ref(false) // 是否正在打字

//根据字典，初始化含一级技能空对象
const skillMap = ref<FirstLevelSkills>(
    firstLevelSkillOptions.reduce((acc, item) => {
      acc[item.value as number] = {} // 初始化为一个空对象
      return acc
    }, {} as FirstLevelSkills)
)

// 创建Markdown解析器
const markdown = new MarkdownIt({
  html: false, // 禁用HTML标签
  linkify: true, // 自动转换URL为链接
  typographer: true // 美化排版
}).use(markdownItSanitizer) // 添加XSS防护插件

// 对话消息历史
const messageHistory = ref([
  {
    answer: welcome.value,
    fullAnswer: welcome.value,
    question: ''
  }
])

// 发送消息参数
const queryParams = reactive({
  positionId: '',
  positionName: '',
  conversationId: '',
  question: '',
  optimizeQuestion: '',
  contextAbove: '',
  content: '',
  contextBelow: '',
  source: Source.skillTagsGenerator,
  type: ChatTypes.skillTags
})

const addSkill = (skills: Skill[], secondLevelSkillKey: number) => {
  const skillName = secondLevelSkillInputModel.value[secondLevelSkillKey]
  if (!skillName) return

  skills.push({
    name: skillName,
    status: false,
    repeat: false
  })
  secondLevelSkillInputModel.value[secondLevelSkillKey] = ''
  // 调用重名检查
  checkSkillNameDuplicates()
}

// 删除标签
const removeSkill = (skills: Skill[], index: number) => {
  skills.splice(index, 1)

  // 调用重名检查
  checkSkillNameDuplicates()
}

// 处理新会话创建
const handleInitialChat = () => {
  // 如果URL中指定了创建新会话并且有职位名称，则创建新会话
  if (positionName.value && shouldCreateChat.value) {
    // 创建会话
    createConv(positionId.value, positionName.value)
  }
}

// 创建会话
const createConv = async (positionId?: number, positionName?: string) => {
  console.log('开始创建会话')
  console.log('Position ID:', positionId)
  console.log('Position Name:', positionName)
  console.log('Department Name:', deptName.value)
  try {
    loading.value = true

    // 构造请求参数
    const params = {
      positionId,
      deptName: deptName.value
    }

    const res = await SkillApi.getStreamAnswerAndSkills(params)
    conversationId.value = res.session_id // 保存会话ID
    console.log('res', res)
    welcome.value = res.answer // 保存欢迎语
    messageHistory.value = [
      {
        answer: welcome.value,
        fullAnswer: welcome.value,
        question: ''
      }
    ] // 初始化消息历史
    console.log('Conversation created successfully', res)
    console.log('Conversation ID:', conversationId.value)
  } catch (error) {
    console.error('Error creating chat:', error)
  } finally {
    loading.value = false
  }
}

// Chat发送预处理
const btnSendMessage = () => {
  if (queryParams.question && !hasData.value) {
    sendMessage()
  }
}

// 定义颜色主题数组
const colorThemes = [
  {
    // 第一套颜色 - 深绿色系
    primary: '#017B3D',    // 一级技能背景色
    secondary: '#91F1A4',  // 二级技能背景色
    tertiary: '#017B3D',   // 三级技能背景色
    tertiaryBg: '#C7F8CB80' // 三级技能容器背景色
  },
  {
    // 第二套颜色 - 浅绿色系
    primary: '#2F9E19',
    secondary: '#C3F5A2',
    tertiary: '#2F9E19',
    tertiaryBg: '#E3FAD080'
  },
  {
    // 第三套颜色 - 蓝色系
    primary: '#129680',
    secondary: '#12968040',
    tertiary: '#129680',
    tertiaryBg: '#CDF9E180'
  }
]

// 获取颜色主题的方法
const getColorTheme = (index: number = 0) => {
  // 按照定义的theme数组顺序分配颜色
  return colorThemes[index % colorThemes.length]
}

const checkSkillNameDuplicates = () => {
  const skillNameMap = new Map<string, { count: number }>()
  // 第一次遍历：统计每个 name 出现的次数
  Object.values(skillMap.value).forEach((secondLevelSkills: SecondLevelSkills) => {
    Object.values(secondLevelSkills).forEach((skills: Skill[]) => {
      skills.forEach((skill: Skill) => {
        if (!skillNameMap.has(skill.name)) {
          skillNameMap.set(skill.name, { count: 0 })
        }
        skillNameMap.get(skill.name)!.count += 1
      })
    })
  })

  // 第二次遍历：设置 repeat 属性
  Object.values(skillMap.value).forEach((secondLevelSkills: SecondLevelSkills) => {
    Object.values(secondLevelSkills).forEach((skills: Skill[]) => {
      skills.forEach((skill: Skill) => {
        const count = skillNameMap.get(skill.name)?.count || 0
        skill.repeat = count > 1
      })
    })
  })
}

const convertParsedContent = (
    parsedContent: Record<string, Record<string, string[]>>
): FirstLevelSkills => {
  const result: Partial<FirstLevelSkills> = {}

  Object.entries(parsedContent).forEach(([firstLevelLabel, secondLevelObj]) => {
    const firstLevelKey = getDictKeyByLabel(DICT_TYPE.EDP_FIRST_LEVEL_SKILL, firstLevelLabel)
    if (!firstLevelKey) return

    const secondLevelMap: Record<number, Skill[]> = {}

    Object.entries(secondLevelObj as Record<string, string[]>).forEach(
        ([secondLevelLabel, skillNames]) => {
          const secondLevelKey = getDictKeyByLabel(DICT_TYPE.EDP_SECOND_LEVEL_SKILL, secondLevelLabel)
          if (!secondLevelKey) return

          // 将字符串数组转成 Skill[] 数组
          secondLevelMap[secondLevelKey] = skillNames.map((name: string) => ({
            name,
            status: true,
            repeat: false
          }))
        }
    )

    result[firstLevelKey] = secondLevelMap
  })

  return result as FirstLevelSkills
}

// 完整的内容过滤函数 - 彻底移除JSON和不需要的内容
const filterContent = (content: string): string => {
  if (!content || content.trim() === '') return ''

  let filtered = content

  // 1. 移除所有JSON代码块（包括多个）以及前后的横线
  while (filtered.includes('```json')) {
    const jsonStartIndex = filtered.indexOf('```json')
    const jsonEndIndex = filtered.indexOf('```', jsonStartIndex + '```json'.length)

    if (jsonEndIndex > jsonStartIndex) {
      let beforeJson = filtered.substring(0, jsonStartIndex).trim()
      let afterJson = filtered.substring(jsonEndIndex + 3).trim() // +3 for ```

      // 移除JSON前面可能的横线
      beforeJson = beforeJson.replace(/[-_=*]{3,}\s*$/, '').trim()

      // 移除JSON后面可能的横线
      afterJson = afterJson.replace(/^\s*[-_=*]{3,}/, '').trim()

      filtered = beforeJson + (beforeJson && afterJson ? '\n\n' : '') + afterJson
    } else {
      // 如果没有找到结束标记，移除从开始到末尾的所有内容
      filtered = filtered.substring(0, jsonStartIndex).trim()
      // 也移除可能的横线
      filtered = filtered.replace(/[-_=*]{3,}\s*$/, '').trim()
      break
    }
  }

  // 2. 移除任何包含"json"关键字的代码块
  filtered = filtered.replace(/```[^`]*json[^`]*```/gs, '')

  // 3. 移除所有代码块标记（包括不完整的）
  filtered = filtered.replace(/```[a-zA-Z]*[\s\S]*?```/g, '')
  filtered = filtered.replace(/```[a-zA-Z]*/g, '')
  filtered = filtered.replace(/```/g, '')

  // 4. 处理Markdown代码块内容提取
  if (filtered.includes('```markdown')) {
    const markdownStartIndex = filtered.indexOf('```markdown') + '```markdown'.length
    const markdownEndIndex = filtered.indexOf('```', markdownStartIndex)
    if (markdownEndIndex > markdownStartIndex) {
      filtered = filtered.substring(markdownStartIndex, markdownEndIndex).trim()
    }
  }

  // 5. 移除思考过程
  if (filtered.includes('</think>')) {
    const thinkEndIndex = filtered.lastIndexOf('</think>') + 8
    filtered = filtered.substring(thinkEndIndex).trim()
  }

  // 6. 移除未完成的思考过程
  if (filtered.includes('<think>') && !filtered.includes('</think>')) {
    const thinkStartIndex = filtered.indexOf('<think>')
    filtered = filtered.substring(0, thinkStartIndex).trim()
  }

  // 7. 移除任何剩余的JSON相关内容
  filtered = filtered.replace(/\{[\s\S]*?\}/g, '') // 移除大括号内容
  filtered = filtered.replace(/\[[\s\S]*?\]/g, '') // 移除方括号内容

  // 8. 移除横线分隔符
  filtered = filtered.replace(/^-{3,}$/gm, '') // 移除独立行的横线（3个或更多）
  filtered = filtered.replace(/^_{3,}$/gm, '') // 移除独立行的下划线
  filtered = filtered.replace(/^={3,}$/gm, '') // 移除独立行的等号
  filtered = filtered.replace(/^\*{3,}$/gm, '') // 移除独立行的星号

  // 9. 移除包含特定关键字的行
  const lines = filtered.split('\n')
  const cleanLines = lines.filter((line) => {
    const trimmedLine = line.trim()
    const lowerLine = trimmedLine.toLowerCase()

    // 移除空行和只包含分隔符的行
    if (
        trimmedLine === '' ||
        /^[-_=*]{3,}$/.test(trimmedLine) ||
        lowerLine.includes('json') ||
        lowerLine.includes('{') ||
        lowerLine.includes('}') ||
        lowerLine.includes('[') ||
        lowerLine.includes(']') ||
        lowerLine.includes('```')
    ) {
      return false
    }
    return true
  })
  filtered = cleanLines.join('\n')

  // 10. 确保Markdown标题格式正确
  filtered = filtered.replace(/^##\s*(.*)/gm, '## $1')

  // 11. 清理多余的空行
  filtered = filtered.replace(/\n\s*\n\s*\n/g, '\n\n')
  filtered = filtered.replace(/^\n+/, '') // 移除开头的空行
  filtered = filtered.replace(/\n+$/, '') // 移除结尾的空行

  return filtered.trim()
}

// Chat网络请求发送
const sendMessage = async (firstLevelSkillKey: number) => {
  currentFirstSkillKey.value = firstLevelSkillKey
  sending.value = true // 设置发送状态
  conversationInProgress.value = true

  queryParams.question = `Please generate ${getDictLabel(DICT_TYPE.EDP_FIRST_LEVEL_SKILL, firstLevelSkillKey)!}.`

  // 添加新消息到消息列表
  const newMessage = {
    id: generateUUID(),
    question: queryParams.question,
    answer: '',
    fullAnswer: '',
    source: queryParams.source,
    type: queryParams.type
  }

  messageHistory.value.push(newMessage) // 将用户输入的消息添加到消息历史中
  queryParams.question = '' // 清空输入框
  scrollToBottom() // 滚动到底部

  // 调用 API 发送流式消息
  conversationInAbortController.value = new AbortController()

  let lastContent = '' // 上一次收到的完整内容

  const onMessage = (event: MessageEvent) => {
    const data = JSON.parse(event.data)
    const rawContent = data.data?.answer || ''

    // 检查特殊条件：当data.data为true时，更新chatId
    if (data?.data && data?.id) {
      chatId.value = data.id
    }

    // 跳过系统日志信息、空白行和思考过程
    if (
        rawContent.includes('is running...') ||
        rawContent.trim() === '' ||
        (rawContent.includes('<think>') && !rawContent.includes('</think>'))
    ) {
      return
    }

    // 先处理JSON数据提取（不显示）
    if (rawContent.includes('```json') && rawContent.includes('```')) {
      const jsonStartIndex = rawContent.indexOf('```json')
      const jsonEndIndex =
          rawContent.indexOf('```', jsonStartIndex + '```json'.length) + '```'.length

      if (jsonEndIndex > jsonStartIndex) {
        try {
          const jsonContent = rawContent
              .substring(jsonStartIndex + '```json'.length, jsonEndIndex - '```'.length)
              .trim()

          const parsedContent = JSON.parse(jsonContent) as Record<string, Record<string, string[]>>
          const parsedSkills = convertParsedContent(parsedContent)
          const filteredSkills: FirstLevelSkills = {}
          filteredSkills[firstLevelSkillKey] = parsedSkills[firstLevelSkillKey]
          _.merge(skillMap.value, filteredSkills)
          checkSkillNameDuplicates()
        } catch (error) {
          console.error('Error parsing JSON content:', error)
        }
      }
    }

    // 过滤内容用于显示
    const filteredContent = filterContent(rawContent)

    // 额外安全检查：确保没有JSON相关内容和横线
    if (
        !filteredContent ||
        filteredContent.includes('```json') ||
        filteredContent.includes('```') ||
        filteredContent.includes('{') ||
        filteredContent.includes('}') ||
        filteredContent.includes('[') ||
        filteredContent.includes(']') ||
        filteredContent.toLowerCase().includes('json') ||
        /^-{3,}$/m.test(filteredContent) ||
        /^_{3,}$/m.test(filteredContent) ||
        /^={3,}$/m.test(filteredContent) ||
        /^\*{3,}$/m.test(filteredContent)
    ) {
      return
    }

    // 如果是第一次接收内容，检查是否需要清理前导空白
    if (lastContent === '') {
      // 处理第一条消息 - 清除前导空格
      const trimmedContent = filteredContent.trimStart()
      lastContent = trimmedContent

      const lastMessage = messageHistory.value[messageHistory.value.length - 1]
      if (lastMessage) {
        if (!lastMessage.fullAnswer) {
          lastMessage.fullAnswer = ''
        }

        // 存储完整答案（已过滤JSON等）
        lastMessage.fullAnswer = trimmedContent

        if (trimmedContent.length > 0) {
          // 将已过滤的内容添加到打字队列
          typingQueue.value.push(trimmedContent)
        }

        if (!isTyping.value) {
          processTypingQueue(lastMessage)
        }
      }
      return // 直接返回，不做后续处理
    }

    // 获取新增部分
    if (filteredContent.startsWith(lastContent)) {
      // 正确计算新增部分
      const newPart = filteredContent.slice(lastContent.length)
      if (newPart.length === 0) return // 没有新内容，直接返回

      lastContent = filteredContent // 更新lastContent为最新的完整内容

      const lastMessage = messageHistory.value[messageHistory.value.length - 1]
      if (lastMessage) {
        // 更新完整答案（已过滤JSON等）
        lastMessage.fullAnswer = filteredContent

        if (newPart.length > 0) {
          // 只将新增部分加入打字队列（已过滤）
          typingQueue.value.push(newPart)
        }

        if (!isTyping.value) {
          processTypingQueue(lastMessage)
        }
      }
    } else {
      lastContent = filteredContent

      const lastMessage = messageHistory.value[messageHistory.value.length - 1]
      if (lastMessage) {
        // 更新完整答案为过滤后的内容
        lastMessage.fullAnswer = filteredContent

        // 清空现有队列
        typingQueue.value = []
        lastMessage.answer = ''

        // 将过滤后的全部内容加入队列重新打印
        typingQueue.value.push(filteredContent)

        if (!isTyping.value) {
          processTypingQueue(lastMessage)
        }
      }
    }
  }

  const onError = (error: Event) => {
    console.error('Stream error:', error)
    // debugger
    stopStream()
  }

  const onClose = () => {
    console.log('Stream closed')
    stopStream()
  }

  // 调用流式消息接口
  const requestData: any = {
    jdId: jdId.value,
    positionId: positionId.value,
    positionName: positionName.value,
    level: firstLevelSkillKey,
    question: messageHistory.value[messageHistory.value.length - 1]?.question || '',
    source: queryParams.source,
    type: queryParams.type
  }

  // 接口调用
  await SkillApi.getStreamAnswerAndSkills(
      requestData,
      conversationInAbortController.value,
      onMessage,
      onError,
      onClose
  )

  sending.value = false // 发送完成，重置发送状态
}

// 处理打字队列
const processTypingQueue = async (message: any) => {
  if (typingQueue.value.length === 0) {
    // 确保显示完整内容
    if (message.fullAnswer && message.answer !== message.fullAnswer) {
      message.answer = message.fullAnswer
      scrollToBottom()
    }
    isTyping.value = false
    return
  }

  isTyping.value = true
  const text = typingQueue.value.shift() || ''

  // 最后的安全检查：确保文本不包含JSON相关内容和横线
  if (
      text.includes('```json') ||
      text.includes('```') ||
      text.includes('{') ||
      text.includes('}') ||
      text.includes('[') ||
      text.includes(']') ||
      text.toLowerCase().includes('json') ||
      /^-{3,}$/m.test(text) ||
      /^_{3,}$/m.test(text) ||
      /^={3,}$/m.test(text) ||
      /^\*{3,}$/m.test(text)
  ) {
    // 如果包含JSON相关内容或横线，跳过这段文本，处理下一个
    await processTypingQueue(message)
    return
  }

  // 逐字打印文本
  for (let i = 0; i < text.length; i++) {
    message.answer += text.charAt(i)
    // 每添加一个字符就滚动到底部
    await nextTick(() => {
      scrollToBottom()
    })
    await new Promise((resolve) => setTimeout(resolve, 20)) // 略微加快打字速度
  }

  // 处理队列中的下一条消息
  await processTypingQueue(message)
}

// 停止 stream 流式调用
const stopStream = async () => {
  // tip：如果 stream 进行中的 message，就需要调用 controller 结束
  const controller = conversationInAbortController.value
  if (controller) {
    console.log('正在中止流式请求...', controller)
    controller.abort()
    conversationInAbortController.value = null
  } else {
    console.warn('无法中止，控制器为空')
  }
  conversationInProgress.value = false

  // 等待正在进行的打字完成
  if (isTyping.value) {
    const lastMessage = messageHistory.value[messageHistory.value.length - 1]

    // 清空队列，但确保完整内容被显示
    if (lastMessage && lastMessage.fullAnswer) {
      typingQueue.value = []
      lastMessage.answer = lastMessage.fullAnswer
    }

    // 等待当前打字完成
    await new Promise((resolve) => {
      const checkTyping = () => {
        if (!isTyping.value) {
          resolve(null)
        } else {
          setTimeout(checkTyping, 100)
        }
      }
      checkTyping()
    })
  }

  // 设置为 false
  conversationInProgress.value = false
  sending.value = false
}

// 滚动到底部
const scrollToBottom = () => {
  nextTick(() => {
    const chatScrollbarEl = chatScrollRef.value?.$el.querySelector('.el-scrollbar__wrap')
    if (chatScrollbarEl) {
      // 使用平滑滚动效果
      chatScrollbarEl.scrollTo({
        top: chatScrollbarEl.scrollHeight,
        behavior: 'smooth'
      })
    }

    // 在优化回复区域内查找滚动容器
    const optimizeScrollbar = document.querySelector('.bg-white.p-3.rounded-3 .el-scrollbar')
    if (optimizeScrollbar) {
      const scrollbarWrap = optimizeScrollbar.querySelector('.el-scrollbar__wrap')
      if (scrollbarWrap) {
        scrollbarWrap.scrollTop = scrollbarWrap.scrollHeight
      }
    }
  })
}

// 将 Markdown 转换为 HTML 的方法
const renderMarkdown = (text: string): string => {
  if (!text) return ''

  // 使用统一的过滤函数
  let processedText = filterContent(text)

  // 处理可能会被误解为h1的#文本 (如果不是标准markdown格式)
  // 找到不符合markdown格式但以#开头的行
  const lines = processedText.split('\n')
  for (let i = 0; i < lines.length; i++) {
    // 修正Markdown标题格式检测逻辑
    const trimmedLine = lines[i].trim()
    if (trimmedLine.startsWith('#')) {
      // 正确的Markdown标题应匹配：1个或多个#号，后面紧跟一个空格，然后是标题内容
      const isValidHeading = /^#+\s+.+/.test(trimmedLine)

      // 如果不是标准Markdown标题格式，添加转义字符
      if (!isValidHeading) {
        // 在首个#前加入转义字符
        lines[i] = lines[i].replace(/^(\s*)#/, '$1\\#')
      }
    }
  }
  processedText = lines.join('\n')

  // 处理markdown代码块
  // 这个正则表达式匹配```开头（可选地后跟markdown）和```之间的内容
  const codeBlockRegex = /```(?:markdown)?\s*([\s\S]*?)\s*```/g
  const matches = [...(processedText.matchAll(codeBlockRegex) || [])]

  let htmlContent: string

  // 如果找到了代码块，只渲染代码块内的内容
  if (matches && matches.length > 0) {
    // 获取第一个匹配的代码块内容
    const codeBlockContent = matches[0][1]
    htmlContent = markdown.render(codeBlockContent)
  } else {
    // 如果没找到代码块，直接渲染整个内容
    htmlContent = markdown.render(processedText)
  }

  return `<div class="markdown-content">${htmlContent}</div>`
}

// 重新生成
// const handleRegenerate = async (message) => {
//   if (!message || !message.question || sending.value) return // 如果没有消息、问题，或正在发送则不处理
//
//   // 保存原始问题
//   const originalQuestion = queryParams.question
//
//   // 设置要重新生成的问题
//   queryParams.question = message.question
//
//   // 调用已有的发送方法
//   await sendMessage()
//
//   // 恢复原始问题（以防止影响用户可能正在编辑的内容）
//   queryParams.question = originalQuestion
// }

// 获取岗位技能标签
const getSkillList = async () => {
  try {
    loading.value = true // 设置加载状态

    const res = await SkillApi.getPositionSkills(positionId.value)

    if (res) {
      skillId.value = res.id
      positionId.value = res.positionId
      positionName.value = res.positionName
      positionReportsTo.value = res.positionReportsTo

      _.merge(skillMap.value, res.skills)

      // 遍历skillMap，设置skill的repeat为false
      Object.values(skillMap.value).map((secondLevelSkills: SecondLevelSkills) => {
        Object.values(secondLevelSkills).map((skills: Skill[]) => {
          skills.map((_skill: Skill) => (_skill.repeat = false))
        })
      })
    }
  } catch (error) {
    console.log('获取岗位技能标签失败:', error)
    ElMessage.error('Failed to load skill tags')
  } finally {
    loading.value = false // 结束加载状态
  }
}

// 获取上次生成的技能标签内容
const getLastSkills = async () => {
  try {
    historyLoading.value = true // 设置加载状态为true

    // 调用接口
    const res = await SkillApi.getLastSkills(positionId.value)
    // console.log('获取上次生成的技能标签内容:', res)

    // 如果有历史数据，处理并显示
    if (res && res.answer) {
      // 使用统一的过滤函数
      const filteredAnswer = filterContent(res.answer)

      // 清空现有消息历史
      messageHistory.value = []

      // 如果有问题，先添加问题
      if (res.question) {
        const userMessage = {
              question: res.question,
              answer: '',
              fullAnswer: ''
            }
            // 使用类型断言添加额外的属性
        ;(userMessage as any).source = queryParams.source
        ;(userMessage as any).type = queryParams.type
        messageHistory.value.push(userMessage)
      }

      // 添加回答
      const aiMessage = {
            question: '',
            answer: filteredAnswer,
            fullAnswer: filteredAnswer
          }
          // 使用类型断言添加额外的属性
      ;(aiMessage as any).source = queryParams.source
      ;(aiMessage as any).type = queryParams.type
      messageHistory.value.push(aiMessage)

      // 更新conversationId
      if (res.session_id) {
        conversationId.value = res.session_id
      }

      // 滚动到底部
      nextTick(() => {
        scrollToBottom()
      }).then()
    }
  } catch (error) {
    console.log('获取上次生成的技能标签内容失败:', error)
  } finally {
    historyLoading.value = false // 结束加载状态
  }
}

const checkRepeat = () => {
  // 判断是否技能重复
  const skillNames = new Set()
  let repeat = false
  Object.values(skillMap.value).forEach((secondLevelSkills: SecondLevelSkills) => {
    Object.values(secondLevelSkills).forEach((skills: Skill[]) => {
      skills.forEach((_skill: Skill) => {
        const skillName = _skill.name
        // 如果 Set 中已存在该名称，说明重复
        if (skillNames.has(skillName)) {
          repeat = true
          return
        } else {
          skillNames.add(skillName)
        }
      })
    })
  })
  return repeat
}

const checkSkillsCountMoreThan = () => {
  // 校验每个skills技能数量是否超过20个，是则弹出提示，必须用for，不能用foreach
  for (const secondLevelSkills of Object.values(skillMap.value)) {
    for (const skills of Object.values(secondLevelSkills)) {
      if (skills.length > 20) {
        return true // 一旦发现长度超过20，立即返回 true
      }
    }
  }
  return false
}

// 处理提交
const handleSubmit = async () => {
  // 检查技能是否为空
  const hasSkills = Object.values(skillMap.value).some((secondLevelSkills) =>
      Object.values(secondLevelSkills).some((skills) => skills.length > 0)
  )

  if (!hasSkills) {
    ElMessage.warning('Skills cannot be empty. Please add at least one skill.')
    return
  }

  // 检查重复
  if (checkRepeat()) {
    ElMessage.warning('Skill repetition, please check the red skill.')
    return
  }

  if (!positionReportsTo.value || positionReportsTo.value.length > 60) {
    ElMessage.warning('Position Reports To is incorrect.')
    return
  }

  if (checkSkillsCountMoreThan()) {
    ElMessage.warning('You can only add up to 20 skills. Please check.')
    return
  }

  // 显示确认对话框
  try {
    await ElMessageBox.confirm(
        "Your characterization of the position's skills will have a meaningful impact on the generation of the Learning Map.",
        'Confirmation',
        {
          confirmButtonText: 'OK',
          cancelButtonText: 'Cancel',
          type: 'warning'
        }
    )
  } catch {
    return // 用户取消，直接返回
  }

  // 扁平化技能数据结构
  const flattenSkillMap = () => {
    const result: { firstSkill: number; secondSkill: number; thirdSkill: string }[] = []
    Object.entries(skillMap.value).forEach(([firstSkillStr, secondLevelSkills]) => {
      const firstSkill = Number(firstSkillStr)
      Object.entries(secondLevelSkills).forEach(([secondSkillStr, skills]) => {
        const secondSkill = Number(secondSkillStr)
        if (Array.isArray(skills)) {
          skills.forEach((skill) => {
            result.push({
              firstSkill,
              secondSkill,
              thirdSkill: skill.name
            })
          })
        }
      })
    })
    return result
  }

  // 定义请求体
  const params: PositionSkillReqVO = {
    positionId: positionId.value,
    positionName: positionName.value,
    positionReportsTo: positionReportsTo.value,
    skills: flattenSkillMap()
  }

  if (skillId.value) {
    // 使用已有的技能ID 更新操作
    params.id = skillId.value
    await SkillApi.updatePositionSkill(params)
    ElMessage.success('Skills submitted successfully')
  } else {
    await SkillApi.createPositionSkill(params)
    ElMessage.success('Skills created successfully')
  }

  skillMap.value = {}
  getSkillList().then()
}

/* Mounted */
onMounted(() => {
  console.log('kjslkdfjs', jdId.value)
  positionId.value = useRouteQuery('positionId', 0, { transform: Number }).value
  handleInitialChat() // 如果有查询参数，创建新会话
  getSkillList() // 获取岗位技能
  getLastSkills() // 获取上次生成的技能内容
})
</script>

<template>
  <ContentWrap class="!h-full" v-loading="loading">
    <el-row class="relative flex items-stretch">
      <!--技能表单-->
      <el-col :span="16">
        <el-scrollbar class="!h-full p-3">
          <div class="flex flex-col gap-4">
            <el-descriptions title="Positon Skills" border :column="2">
              <!--部门名称-->
              <el-descriptions-item label="Department">{{ deptName }}</el-descriptions-item>
              <!--岗位和报告-->
              <el-descriptions-item label="Position">{{ positionName }}</el-descriptions-item>
              <!--岗位汇报线-->
              <el-descriptions-item label="Position reports to">
                <el-input
                    minlength="1"
                    maxlength="60"
                    show-word-limit
                    v-model="positionReportsTo"
                />
              </el-descriptions-item>
            </el-descriptions>

            <!--技能标签-->
            <div
                v-for="[firstLevelSkillKey, secondLevelSkills] in Object.entries(skillMap)"
                :key="firstLevelSkillKey"
                class="flex flex-col gap-2"
            >
              <!--一级Tag标题-->
              <div class="flex items-center justify-between w-full">
                <!--标题-->
                <div class="flex items-center gap-1">
                  <icon icon="ep:collection-tag" :size="20" class="text-gray" />

                  <!--标题文本-->
                  <div class="flex gap-2 items-baseline">
                    <span class="text-xl">
                      {{ getDictLabel(DICT_TYPE.EDP_FIRST_LEVEL_SKILL, firstLevelSkillKey) }}
                    </span>
                  </div>
                </div>

                <!--生成按钮-->
                <el-button
                    :disabled="isTyping || loading || historyLoading || sending"
                    :loading="
                    currentFirstSkillKey === Number(firstLevelSkillKey) && (sending || isTyping)
                  "
                    text
                    class="!w-8"
                    @click="() => sendMessage(Number(firstLevelSkillKey))"
                >
                  <img :src="Generate" alt="Generate" />
                </el-button>
              </div>

              <!--标签体 - 一个容器包含所有子项-->
              <div class="border-[1px] border-[#dedfe6] border-solid rounded-1 p-3">
                <!-- 遍历所有二级标签 -->
                <!--                <div v-if="Object.keys(secondLevelSkills).length === 0" class="text-light">No Skills</div>-->
                <el-empty
                    v-if="Object.keys(secondLevelSkills).length === 0"
                    :image-size="40"
                    description="No Skills"
                />
                <template
                    v-for="([secondLevelSkillKey, skills], index) in Object.entries(
                    secondLevelSkills
                  )"
                    :key="secondLevelSkillKey"
                >
                  <!-- 如果不是第一项，添加分隔线 -->
                  <el-divider v-if="index > 0" class="!my-3" />

                  <div class="flex flex-col justify-start items-start space-y-sm">
                    <div class="w-full flex items-center justify-between">
                      <el-text type="info" tag="b" size="large"
                      >{{ getDictLabel(DICT_TYPE.EDP_SECOND_LEVEL_SKILL, secondLevelSkillKey) }}
                      </el-text>
                      <el-text v-if="skills.length > 20" type="danger" tag="b">
                        * Maximum 20
                      </el-text>
                    </div>

                    <div class="w-full flex gap-2 items-center flex-wrap">
                      <div class="flex gap-2 items-center flex-wrap">
                        <template v-for="(skill, skillIndex) in skills" :key="skill.name">
                          <el-badge is-dot :hidden="skill.id !== undefined">
                            <el-tag
                                :type="skill.repeat ? 'danger' : 'primary'"
                                closable
                                @close="removeSkill(skills, skillIndex)"
                            >
                              {{ skill.name }}
                            </el-tag>
                          </el-badge>
                        </template>
                      </div>

                      <div class="w-full flex items-center">
                        <el-input
                            v-model="secondLevelSkillInputModel[secondLevelSkillKey]"
                            :maxlength="60"
                            class="no-border-input"
                            placeholder="Enter the skill & press the confirm button"
                            show-word-limit
                            @keyup.enter="addSkill(skills, Number(secondLevelSkillKey))"
                        />
                        <!--                        <input-->
                        <!--                            :maxlength="60"-->
                        <!--                            bizType="text"-->
                        <!--                            class="flex-1 border-none shadow-none h-8 hover:border-none focus:shadow-none outline-none"-->
                        <!--                            placeholder="Add Skill"-->
                        <!--                            @keyup.enter="-->
                        <!--                            ($event) => {-->
                        <!--                              addSkill(skills, $event)-->
                        <!--                            }-->
                        <!--                          "-->
                        <!--                        />-->
                        <!--<span class="text-xs text-gray">100 limit</span>-->
                      </div>
                    </div>
                  </div>
                </template>
              </div>
            </div>

            <!-- 技能标签汇总 -->
            <div class="flex flex-col">
              <!--标题-->
              <el-descriptions title="Skill Tags" :column="1" />

              <!--标签体-->
              <div
                  class="grid grid-cols-12 gap-3 border-[1px] border-[#dedfe6] border-solid rounded-1 p-3"
              >
                <!-- 遍历所有一级技能 -->
                <template
                    v-for="([firstLevelSkillKey, secondLevelSkills], index) in Object.entries(skillMap)"
                    :key="firstLevelSkillKey"
                >
                  <!-- 只显示有数据的一级技能 -->
                  <template v-if="Object.keys(secondLevelSkills).length > 0">
                    <!-- 一级技能标签 - 占2个栅格 -->
                    <div
                        class="col-span-2 flex items-center justify-center text-white p-3 rounded-md text-center"
                        :style="{ backgroundColor: getColorTheme(index).primary }"
                    >
                      <span class="text-sm font-medium">
                        {{ getDictLabel(DICT_TYPE.EDP_FIRST_LEVEL_SKILL, firstLevelSkillKey) }}
                      </span>
                    </div>

                    <!-- 二级技能容器 - 占4个栅格 -->
                    <div class="col-span-4">
                      <div class="flex flex-col h-full gap-2">
                        <template
                            v-for="[secondLevelSkillKey, skills] in Object.entries(secondLevelSkills)"
                            :key="secondLevelSkillKey"
                        >
                          <!-- 只显示有技能的二级分类 -->
                          <div
                              v-if="skills.length > 0"
                              class="flex-1 flex items-center justify-center p-2 rounded-md text-center"
                              :style="{ backgroundColor: getColorTheme(index).secondary }"
                          >
                            <span class="text-sm">
                              {{ getDictLabel(DICT_TYPE.EDP_SECOND_LEVEL_SKILL, secondLevelSkillKey) }}
                            </span>
                          </div>
                        </template>
                      </div>
                    </div>

                    <!-- 三级技能容器 - 占6个栅格 -->
                    <div class="col-span-6">
                      <div class="flex flex-col gap-2">
                        <template
                            v-for="[secondLevelSkillKey, skills] in Object.entries(secondLevelSkills)"
                            :key="secondLevelSkillKey"
                        >
                          <!-- 显示该二级分类下的所有三级技能 -->
                          <div
                              v-if="skills.length > 0"
                              class="flex flex-wrap gap-1 border border-[#dedfe6] border-solid rounded-1 p-3"
                              :style="{ backgroundColor: getColorTheme(index).tertiaryBg }"
                          >
                            <template v-for="skill in skills" :key="skill.name">
                              <div
                                  class="flex items-center justify-center text-white px-2 py-1 rounded text-xs"
                                  :style="{
                                  backgroundColor: skill.repeat ? '#ef4444' : getColorTheme(index).tertiary,
                                  borderColor: skill.repeat ? '#ef4444' : getColorTheme(index).tertiary,
                                  color: skill.repeat ? 'white' : 'white'
                                }"
                                  :class="{ 'border': true }"
                              >
                                {{ skill.name }}
                              </div>
                            </template>
                          </div>
                        </template>
                      </div>
                    </div>
                  </template>
                </template>

                <!-- 如果没有任何技能数据，显示空状态 -->
                <div
                    v-if="!Object.values(skillMap).some(secondLevel => Object.keys(secondLevel).length > 0)"
                    class="col-span-12 flex items-center justify-center py-8 text-gray-500"
                >
                  <el-empty :image-size="60" description="No skills added yet" />
                </div>
              </div>
            </div>
          </div>

          <div class="flex w-full items-center justify-end mt-20 mb-3">
            <el-button
                type="primary"
                :disabled="isTyping || loading || historyLoading || sending"
                @click="handleSubmit"
            >
              Accept
            </el-button>
          </div>
        </el-scrollbar>
      </el-col>

      <!-- Chat -->
      <el-col
          :span="8"
          class="p-3 relative bg-light"
          style="border-left: 1px solid var(--el-border-color-lighter)"
      >
        <el-affix :offset="120">
          <div class="w-full h-[80vh] bg-white rounded-lg">
            <div
                class="absolute top-0 left-0 right-0 z-10 py-3 px-3"
                style="
                background: linear-gradient(
                  to bottom,
                  rgba(255, 255, 255, 1) 0%,
                  rgba(255, 255, 255, 0.95) 20%,
                  rgba(255, 255, 255, 0.8) 40%,
                  rgba(255, 255, 255, 0.6) 60%,
                  rgba(255, 255, 255, 0.3) 80%,
                  rgba(255, 255, 255, 0) 100%
                );
                backdrop-filter: blur(2px);
                padding-bottom: 2.5rem;
              "
            >
              <div class="flex items-baseline justify-start">
                <h1> {{ positionName }} </h1>
              </div>
            </div>

            <div class="h-full flex flex-col" v-loading="historyLoading">
              <el-scrollbar ref="chatScrollRef" class="flex-1">
                <!--对话界面-->
                <div class="flex flex-col gap-4 my-17 mx-4">
                  <!-- 遍历消息历史 -->
                  <template v-for="(message, index) in messageHistory" :key="index">
                    <!-- 用户消息 -->
                    <div v-if="message.question" class="flex flex-col gap-2 self-end max-w-[80%]">
                      <div class="flex items-center self-end gap-2">
                        <!--用户名-->
                        <span class="text-14px text-gray <lg:hidden">
                          {{ userName }}
                        </span>

                        <!--头像-->
                        <ElAvatar :src="avatar" alt="" class="!w-7 !h-7 rounded-full" />
                      </div>

                      <!--消息气泡-->
                      <div class="w-fit flex flex-col gap-2 p-3 rounded-lg bg-[#017B3D] ml-auto">
                        <span class="text-white">{{ message.question }}</span>
                      </div>
                    </div>

                    <!-- AI消息 -->
                    <div v-if="message.answer" class="flex flex-col gap-2 self-start max-w-[80%]">
                      <div class="flex gap-2">
                        <!--AI助手头像-->
                        <div
                            class="flex justify-center align-center pt-1 border-solid border-[1px] border-gray-200 rounded-full w-7 h-7"
                        >
                          <Icon icon="ep:promotion" color="#017B3D" />
                        </div>

                        <!--名称-->
                        <span class="flex justify-center align-center text-gray <lg:hidden"
                        >AI Assistant</span
                        >
                      </div>

                      <!--消息气泡-->
                      <div
                          class="flex flex-col gap-2 p-3 border-solid border-[1px] border-gray-200 rounded-lg markdown-bubble"
                      >
                        <div v-if="message.answer" class="markdown-wrapper">
                          <div v-html="renderMarkdown(message.answer)"></div>
                        </div>
                      </div>
                    </div>
                  </template>
                </div>
              </el-scrollbar>

              <!--Chat输入框-->
              <div class="w-full flex flex-col gap-2 p-4">
                <el-button
                    :disabled="isTyping || loading || historyLoading || sending"
                    :loading="
                    currentFirstSkillKey === Number(skillOption.value) && (sending || isTyping)
                  "
                    v-for="skillOption in firstLevelSkillOptions"
                    :key="skillOption.label"
                    @click="sendMessage(Number(skillOption.value))"
                >
                  Generate {{ skillOption.label }}
                </el-button>
              </div>
            </div>
          </div>
        </el-affix>
      </el-col>
    </el-row>
  </ContentWrap>
</template>

<style scoped>
/* 去除按钮左外边距 */
:deep(.el-button + .el-button) {
  margin-left: 0 !important;
}

/* 去除el-card__body内边距 */
:deep(.el-card__body) {
  padding: 0 !important;
}

/* JD版本草稿状态黄点样式 */
.draft-dot {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #f9a825; /* 黄色 */
}

/* Apply border-radius to the input */
.chat-input :deep(.el-input__wrapper) {
  border-radius: 12px;
  border: 1px solid #e7e7e7;
  box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
}

/* Apply border only when focused */
.chat-input :deep(.el-input__wrapper.is-focus) {
  box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.1);
  border: 1px solid #017b3d !important;
}

/* Remove border from collapse component */
.border-none :deep(.el-collapse-item__header),
.border-none :deep(.el-collapse-item__wrap) {
  border: none;
}

/* Markdown 样式 */
:deep(.markdown-content) {
  line-height: 1.6;
  width: 100%;
  overflow-wrap: break-word;
}

/* 确保标题遵循正确的层级关系 */
:deep(.markdown-content h1) {
  font-size: 1.4em;
  margin: 0.5em 0;
  font-weight: bold;
}

:deep(.markdown-content h2) {
  font-size: 1.2em;
  margin: 0.5em 0;
  font-weight: bold;
}

:deep(.markdown-content h3) {
  font-size: 1em;
  margin: 1em 0 0.5em;
  font-weight: bold;
}

:deep(.markdown-content h4) {
  font-size: 0.8em;
  margin: 0.5em 0;
  font-weight: bold;
}

:deep(.markdown-content h5) {
  font-size: 0.6em;
  margin: 0.5em 0;
  font-weight: bold;
}

:deep(.markdown-content h6) {
  font-size: 0.4em;
  margin: 0.5em 0;
  font-weight: bold;
}

:deep(.markdown-content p) {
  margin: 0.5em 0;
}

:deep(.markdown-content a) {
  color: #017b3d;
  text-decoration: none;
}

:deep(.markdown-content code) {
  background-color: #f5f5f5;
  padding: 0.2em 0.4em;
  border-radius: 3px;
  font-family: monospace;
}

:deep(.markdown-content pre) {
  background-color: #f5f5f5;
  padding: 1em;
  border-radius: 5px;
  overflow-x: auto;
}

:deep(.markdown-content ul, .markdown-content ol) {
  padding-left: 2em;
  margin: 0.5em 0;
  box-sizing: border-box;
}

:deep(.markdown-content ol) {
  list-style-position: inside;
}

:deep(.markdown-content li) {
  margin-bottom: 0.3em;
  position: relative;
}

:deep(.markdown-content blockquote) {
  border-left: 4px solid #ddd;
  padding-left: 1em;
  margin-left: 0;
  color: #666;
}

/* 确保编辑器内容区域有足够的内边距 */
.tiptap-editor-container :deep(.ProseMirror) {
  padding: 0 !important;
  outline: none !important; /* 去除蓝色边框 */
}

/* 移除所有编辑区域的原生蓝色边框 */
.tiptap-editor-container :deep(*:focus),
.tiptap-editor-container :deep(*:focus-visible) {
  outline: none !important;
  box-shadow: none !important;
  border-color: transparent !important;
}

/* 移除特定工具栏输入框的边框和阴影 */
.toolbar-input :deep(.el-input__wrapper) {
  border: none !important;
  box-shadow: none !important;
  border-radius: 12px;
}

.toolbar-input :deep(.el-input__wrapper.is-focus) {
  border: none !important;
  box-shadow: none !important;
}

/* 移除技能标签输入框的边框和阴影 */
.skill-tag-input :deep(.el-input__wrapper) {
  padding: 0 !important;
  border: none !important;
  box-shadow: none !important;
}

.skill-tag-input :deep(.el-input__wrapper.is-focus) {
  border: none !important;
  box-shadow: none !important;
  box-shadow: 0 0 0 0 transparent !important;
}

.skill-tag-input :deep(.el-input__wrapper:hover) {
  border: none !important;
  box-shadow: none !important;
}

/* 只对有 no-border-input 的 el-input 生效 */
:deep(.no-border-input .el-input__wrapper) {
  border: none !important;
  box-shadow: none !important;
  padding-left: 0;
  padding-right: 0;
}
</style>