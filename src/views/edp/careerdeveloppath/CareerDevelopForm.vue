<script setup lang="ts">
import { CareerDevelopApi } from '@/api/edp/careerdevelop'

/** ----- INTERFACE ----- */
interface AvailableDept {
  id: number
  name: string
  code: string
  deptName: string
  sectName: string
}

/** ----- SETUP ----- */
/** 职业发展 表单 */
defineOptions({ name: 'CareerDevelopForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const dialogVisible = ref(false) // 弹窗的是否展示
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formRef = ref() // 表单 Ref
const formType = ref('') // 表单类型
const formData = ref({
  deptId: undefined as number | undefined,
  deptName: undefined as string | undefined,
  parentId: undefined as number | undefined,
  pathIndex: 0,
  root: undefined as boolean | undefined
})
const formRules = reactive({
  deptId: [{ required: true, message: 'Please select the department', trigger: 'change' }]
}) // 表单校验
const availableDept = ref<AvailableDept[]>([]) // 可选择的岗位列表
const queryParams = reactive<any>({}) // 查询参数

/** ----- FUNCTIONS ----- */
/** 打开弹窗 */
const open = async (pathIndex: number, parentId?: number, params?: any) => {
  formData.value.pathIndex = pathIndex
  formData.value.parentId = parentId
  formData.value.root = params?.root || false // 设置是否为根节点
  Object.assign(queryParams, params || {})

  dialogVisible.value = true
  resetForm()

  // 获取可选择的岗位列表
  await getCareerDevelopPosition()
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调

const handleCreate = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = {
      positionId: formData.value.deptId, // 选中的职位ID
      positionName: formData.value.deptName, // 选中的职位名称
      parentId: formData.value.parentId || 0, // 根节点传0，子节点传实际的父节点ID
      root: formData.value.root // 是否为根节点
    }
    await CareerDevelopApi.createCareerDevelop(data)
    message.success(t('common.createSuccess'))
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    deptId: undefined,
    deptName: undefined,
    parentId: formData.value.parentId, // 保持父节点ID
    pathIndex: formData.value.pathIndex, // 保持路径索引
    root: formData.value.root // 保持根节点标识
  }
  formRef.value?.resetFields()
}

/** 查询同部门可添加的其他岗位 */
const getCareerDevelopPosition = async () => {
  try {
    const params = {
      deptId: queryParams.deptId,
      sectId: queryParams.sectId,
      ...(queryParams.firstNodeId && { firstNodeId: queryParams.firstNodeId })
    }

    // 根据是否为根节点调用不同API
    const res = formData.value.root
      ? await CareerDevelopApi.getPositionsForNewPath(params)
      : await CareerDevelopApi.getPositionsForNewNode(params)

    console.log('查询岗位列表:', res)
    availableDept.value = Array.isArray(res) ? res : []
    // availableDept.value = [
    //   {
    //     id: 699,
    //     name: 'Final',
    //     code: 'cccc',
    //     deptName: 'dept123',
    //     sectName: 'sec123'
    //   }
    // ]
  } catch (error) {
    console.log('获取岗位列表失败:', error)
    availableDept.value = []
  }
}

/** 处理职位选择变化 */
const handleDeptChange = (id: number) => {
  const selectedPosition = availableDept.value.find((p) => p.id === id)
  if (selectedPosition) {
    formData.value.deptId = selectedPosition.id
    formData.value.deptName = selectedPosition.name
  }
}
</script>

<template>
  <Dialog :title="'Create Node'" v-model="dialogVisible" :fullscreen="false">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="Position" prop="deptId">
        <el-select
          v-model="formData.deptId"
          placeholder="Please select the position"
          clearable
          filterable
          style="width: 100%"
          @change="handleDeptChange"
        >
          <el-option
            v-for="position in availableDept"
            :key="position.id"
            :label="position.name"
            :value="position.id"
            class="!h-auto !leading-normal !py-2 !px-5"
          >
            <div class="flex items-center justify-between w-full">
              <span class="flex-1 truncate mr-2">{{ position.name }}</span>
              <div class="flex gap-1 flex-shrink-0">
                <!-- 子部门名称 -->
                <el-tag
                  v-if="position.sectName"
                  size="small"
                  type="warning"
                  class="!text-[11px] !h-[18px] !leading-4"
                >
                  {{ position.sectName }}
                </el-tag>
                <el-tag
                  v-if="position.code"
                  size="small"
                  type="info"
                  class="!text-[11px] !h-[18px] !leading-4"
                >
                  {{ position.code }}
                </el-tag>
              </div>
            </div>
          </el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="dialogVisible = false"> Cancel </el-button>
      <el-button @click="handleCreate" type="primary" :disabled="formLoading"> Create </el-button>
    </template>
  </Dialog>
</template>
