<script setup lang="ts">
import { ref, computed } from 'vue'
import { TrainingApplyApi, TrainingApplyDetailVO, TrainingNeedStatusEnum } from '@/api/edp/trainingapply'
import { DICT_TYPE, getDictLabel } from '@/utils/dict'

/** ----- SETUP ----- */
const props = defineProps<{
  id?: number // 培训申请ID
  action: 'approve' | 'reject' // 审批动作
}>()

const emit = defineEmits<{
  success: []
}>()

const dialogVisible = ref(false)
const data = ref<TrainingApplyDetailVO>()
const loading = ref(false)
const approveReason = ref('') // 审批原因
const selectedStatus = ref<number | null>(null) // 审批状态：未选则默认 APPROVED
const message = useMessage()

/** ----- COMPUTED ----- */
const dialogTitle = computed(() => {
  return props.action === 'approve' ? 'Approve Training Need' : 'Reject Training Need'
})

const buttonText = computed(() => {
  return props.action === 'approve' ? 'Approve' : 'Reject'
})

const buttonType = computed(() => {
  return props.action === 'approve' ? 'primary' : 'danger'
})

const buttonIcon = computed(() => {
  return props.action === 'approve' ? 'ep:check' : 'ep:close'
})

// 检查是否可以提交
const canSubmit = computed(() => {
  if (props.action === 'reject') {
    return approveReason.value.trim() !== ''
  } else if (props.action === 'approve') {
    return approveReason.value.trim() !== ''
  }
  return true
})

/** ----- FUNCTIONS ----- */
/** 获取培训申请 */
const getTrainingNeed = async (id: number) => {
  try {
    loading.value = true
    data.value = await TrainingApplyApi.getTrainingApply(id)
    console.log('获取培训申请成功😊:', data.value)
  } catch (error) {
    console.log('获取培训申请失败😫:', error)
  } finally {
    loading.value = false
  }
}

/** 打开对话框 */
const openDialog = () => {
  dialogVisible.value = true
  approveReason.value = '' // 重置审批原因
  selectedStatus.value = null // 重置审批状态
  if (props.id) {
    getTrainingNeed(props.id)
  }
}

/** 审批操作 */
const handleApprove = async () => {
  if (!props.id) return
  
  // 如果是拒绝操作且没有填写原因，则不执行
  if (props.action === 'reject' && !approveReason.value.trim()) {
    message.warning('Please provide a reason for rejection')
    return
  }
  
  try {
    loading.value = true
    const status = props.action === 'approve'
      ? (selectedStatus.value ?? TrainingNeedStatusEnum.APPROVED)
      : TrainingNeedStatusEnum.REJECTED

    const params = {
      id: props.id,
      status,
      approveReason: approveReason.value.trim() || null
    }
    
    await TrainingApplyApi.approveTrainingApply(params)
    message.success(props.action === 'approve' ? 'Approved successfully' : 'Rejected successfully')
    
    // 关闭对话框并通知父组件刷新列表
    dialogVisible.value = false
    emit('success')
  } catch (error) {
    message.error('Operation failed')
  } finally {
    loading.value = false
  }
}

/** 解析contentCatalog JSON字符串 */
const parseContentCatalog = (contentCatalog: string) => {
  try {
    return JSON.parse(contentCatalog)
  } catch (error) {
    console.error('解析contentCatalog失败:', error)
    return null
  }
}

/** 格式化创建时间 */
const formatDate = (timestamp: number) => {
  if (!timestamp) return ''
  const date = new Date(timestamp)
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const year = date.getFullYear()
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  return `${month}-${day}-${year} ${hours}:${minutes}`
}

// 暴露方法给插槽使用
defineExpose({
  openDialog
})
</script>

<template>
  <!-- 插槽：让父组件自定义触发按钮 -->
  <slot :open-dialog="openDialog"></slot>

  <Dialog v-model="dialogVisible" :title="dialogTitle" width="800" :z-index="3000" append-to-body>
    <template #default>
      <div v-loading="loading">
        <div v-if="data" class="training-need-content">
          <!-- 基本信息 -->
          <div class="basic-info mb-4">
            <el-descriptions title="Basic Information" :column="2" border>
              <el-descriptions-item label="Title">{{ data.title }}</el-descriptions-item>
              <el-descriptions-item label="BadgeNo">{{ data.badgeNumber }}</el-descriptions-item>
              <el-descriptions-item label="Position">{{ data.positionName || '-' }}</el-descriptions-item>
              <el-descriptions-item label="Applicant">{{ data.applicantName || '-' }}</el-descriptions-item>
              <el-descriptions-item label="Create Time">{{ formatDate(Number(data.createTime)) }}</el-descriptions-item>
              <el-descriptions-item label="Status">
                <el-tag>{{ getDictLabel(DICT_TYPE.EDP_TRAINING_NEED_STATUS, data.status) || '-' }}</el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="Approve Reason" v-if="data.approveReason">
                {{ data.approveReason }}
              </el-descriptions-item>
            </el-descriptions>
          </div>

          <!-- 培训内容卡片 -->
          <div class="content-cards">
            <h3 class="mb-3 text-[#303133]">Training Content</h3>
            <el-card v-for="(content, index) in data.content" :key="content.id" class="content-card mb-4" shadow="hover">
              <template #header>
                <div class="card-header">
                  <span class="card-title">Content {{ index + 1 }}</span>
                </div>
              </template>
              
              <div class="card-content">
                <!-- 内容基本信息 -->
                <el-descriptions :column="2" size="small" border class="mb-3">
                  <el-descriptions-item label="Skill Name">{{ content.skillName }}</el-descriptions-item>
                  <el-descriptions-item label="Training Type">
                      {{ getDictLabel(DICT_TYPE.EDP_TRAINING_TYPE, content.trainingType) || '-' }}
                  </el-descriptions-item>
                </el-descriptions>

                <!-- 课程推荐表格 -->
                <div v-if="parseContentCatalog(content.contentCatalog)?.course_recommendations" class="course-table">
                  <h4 class="mb-2">Course Catalog</h4>
                  <el-table 
                    :data="parseContentCatalog(content.contentCatalog).course_recommendations" 
                    size="small" 
                    border
                    style="width: 100%"
                  >
                    <el-table-column prop="Course Name" label="Course Name" min-width="150" />
                    <el-table-column prop="Skill Level" label="Skill Level" min-width="100" />
                    <el-table-column prop="Duration" label="Duration" min-width="100" />
                    <el-table-column prop="Target Audience/Role" label="Target Audience" min-width="200" show-overflow-tooltip />
                    <el-table-column prop="Key Skills Gained" label="Key Skills" min-width="200" show-overflow-tooltip />
                    <el-table-column prop="Detailed Content/Covered Topics" label="Topics" min-width="200" show-overflow-tooltip />
                  </el-table>
                </div>

                <!-- 合理性分析 -->
                <div v-if="parseContentCatalog(content.contentCatalog)?.reasonableness_analysis" class="analysis mt-3">
                  <h4 class="mb-2">Reasonableness Analysis</h4>
                  <el-descriptions :column="1" size="small" border>
                    <el-descriptions-item label="Judgment">
                      <el-tag :type="parseContentCatalog(content.contentCatalog).reasonableness_analysis.judgment === 'Reasonable' ? 'success' : 'danger'">
                        {{ parseContentCatalog(content.contentCatalog).reasonableness_analysis.judgment }}
                      </el-tag>
                    </el-descriptions-item>
                    <el-descriptions-item label="Reason" v-if="parseContentCatalog(content.contentCatalog).reasonableness_analysis.unreasonable_reason">
                      {{ parseContentCatalog(content.contentCatalog).reasonableness_analysis.unreasonable_reason }}
                    </el-descriptions-item>
                    <el-descriptions-item label="Suggestions" v-if="parseContentCatalog(content.contentCatalog).reasonableness_analysis.suggestions?.length">
                      <ul>
                        <li v-for="suggestion in parseContentCatalog(content.contentCatalog).reasonableness_analysis.suggestions" :key="suggestion">
                          {{ suggestion }}
                        </li>
                      </ul>
                    </el-descriptions-item>
                  </el-descriptions>
                </div>
              </div>
            </el-card>
          </div>

          <!--审批状态选择-->
          <div class="approve-status mt-4" v-if="action === 'approve'">
            <h3 class="mb-3 text-[#303133]">Approval Status</h3>
            <el-radio-group v-model="selectedStatus">
              <el-radio :label="TrainingNeedStatusEnum.PURCHASING">Purchasing</el-radio>
              <el-radio :label="TrainingNeedStatusEnum.IN_PRODUCTION">In Production</el-radio>
            </el-radio-group>
          </div>

          <!-- 审批原因输入框 -->
          <div class="approve-reason mt-4">
            <h3 class="mb-3 text-[#303133]">
              {{ action === 'approve' ? 'Approval Comment' : 'Rejection Reason' }}
            </h3>
            <el-input
              v-model="approveReason"
              type="textarea"
              :placeholder="action === 'approve' ? 'Please provide approval comments...' : 'Please provide rejection reason...'"
              :rows="4"
              maxlength="500"
              show-word-limit
              :required="action === 'reject'"
            />
          </div>
        </div>
        
        <div v-else class="loading-container">
          <el-empty description="No data available" />
        </div>
      </div>
    </template>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">Cancel</el-button>
        <el-button 
          :type="buttonType"
          @click="handleApprove"
          :disabled="!canSubmit || data?.status === TrainingNeedStatusEnum.APPROVED || data?.status === TrainingNeedStatusEnum.REJECTED"
          :loading="loading"
        >
          <Icon v-if="!loading" :icon="buttonIcon" class="mr-1" />
          {{ buttonText }}
        </el-button>
      </div>
    </template>
  </Dialog>
</template>