<template>
  <el-button text class="!w-8" @click="dialogVisible = true" :disabled="!content || props.disabled">
    <Icon icon="ep:switch" />
  </el-button>

  <el-dialog
    v-model="dialogVisible"
    :title="props.positionName + ' ' + 'JD Version Diff'"
    align-center
    width="90%"
  >
    <!--左右编辑器对比-->
    <div class="flex gap-4" style="height: 70vh">
      <!--左侧编辑器 - LatestVersion-->
      <div class="flex-1 border border-gray-300 rounded-md p-3">
        <!--编辑时间-->
        <span v-if="props.jDEditTime" class="flex items-center gap-1 text-xs text-gray mb-2">
          <Icon icon="ep:edit" :size="12" />
          {{ props.jDEditTime }}
        </span>

        <div class="tiptap-editor-container relative h-full">
          <el-scrollbar height="100%" v-loading="isPreviewLoading">
            <Editor
              ref="leftEditorRef"
              v-model:selectedContent="leftEditorSelectedContent"
              :dir="textDirection"
            />
          </el-scrollbar>
        </div>
      </div>

      <!--右侧编辑器 - 当前内容-->
      <div class="editor-panel flex-1 border border-gray-300 rounded">
        <div class="panel-header py-2">
          <span class="font-medium text-sm"> {{ props.positionName }} </span>
        </div>
        <div class="tiptap-editor-container relative h-full">
          <el-scrollbar height="100%" v-loading="isPreviewLoading">
            <Editor
              ref="rightEditorRef"
              v-model:selectedContent="rightEditorSelectedContent"
              :dir="textDirection"
            />

            <!-- ToolBar 只在右侧编辑器显示 -->
            <bubble-menu
              v-if="rightEditorRef?.editor"
              class="flex flex-col sticky bottom-5 !w-full bg-white border-solid border-[#EFEFEF] border-[1px] rounded-3 shadow-[0px_2px_8px_0px_rgba(0,0,0,0.1)]"
              :tippy-options="{ duration: 100 }"
              :editor="rightEditorRef.editor"
            >
              <!--AI回复-->
              <div v-if="optimizeAnswer" class="bg-white p-3 rounded-3">
                <!--AI回复内容-->
                <el-scrollbar class="!h-32 mb-2">
                  <div v-html="renderMarkdown(optimizeAnswer)" class="markdown-wrapper"></div>
                </el-scrollbar>

                <!--Actions-->
                <div class="flex justify-between h-full">
                  <el-button
                    type="primary"
                    class="!px-2 !bg-[#017B3D] !border-none flex items-center justify-center"
                    @click="handleReplace"
                  >
                    <Icon icon="ep:top" />
                    <span> Replace </span>
                  </el-button>

                  <div class="flex gap-1">
                    <!--复制到剪贴板-->
                    <el-button text class="!w-8" @click="handleCopyOptimize">
                      <Icon :icon="isCopied ? 'ep:check' : 'ep:copy-document'" />
                    </el-button>

                    <!--重新生成-->
                    <el-button text class="!w-8" @click="handleRegenerateOptimize">
                      <Icon icon="ep:refresh" />
                    </el-button>
                  </div>
                </div>
              </div>

              <!--分割线-->
              <el-divider v-if="optimizeAnswer" class="!border-t-[#EFEFEF] !m-0" />

              <!--JD优化快捷提示词-->
              <div class="flex flex-wrap gap-2 px-3 pt-3">
                <el-button
                  size="small"
                  class="!px-2"
                  @click="applyOptimizePrompt('Polish')"
                  :disabled="optimizing"
                >
                  Polish
                </el-button>
                <el-button
                  size="small"
                  class="!px-2"
                  @click="applyOptimizePrompt('Expand')"
                  :disabled="optimizing"
                >
                  Expand
                </el-button>
                <el-button
                  size="small"
                  class="!px-2"
                  @click="applyOptimizePrompt('Shorten')"
                  :disabled="optimizing"
                >
                  Shorten
                </el-button>
                <el-button
                  size="small"
                  class="!px-2"
                  @click="applyOptimizePrompt('Summarize')"
                  :disabled="optimizing"
                >
                  Summarize
                </el-button>
              </div>

              <!--JD优化提示词输入框-->
              <div class="flex items-center">
                <el-input
                  class="chat-input toolbar-input !h-14 flex-1 !py-3"
                  placeholder="Type message"
                  v-model="optimizeQuestion"
                  maxlength="500"
                  show-word-limit
                  @keydown.enter.exact.prevent="btnJDOptimize"
                />

                <!--发送按钮-->
                <div class="flex justify-center items-center mr-3">
                  <el-button
                    v-if="!optimizing"
                    type="primary"
                    :class="[
                      '!w-8 !h-8 !rounded-full !border-none flex items-center justify-center',
                      optimizeQuestion.trim() && !optimizing ? '!bg-[#017B3D]' : '!bg-gray-300'
                    ]"
                    @click="btnJDOptimize"
                  >
                    <Icon icon="ep:top" />
                  </el-button>

                  <el-button
                    v-if="optimizing"
                    type="primary"
                    :class="[
                      '!w-8 !h-8 !rounded-full !border-none flex items-center justify-center',
                      '!bg-gray-300'
                    ]"
                  >
                    <el-icon class="is-loading"><Loading /></el-icon>
                  </el-button>
                </div>
              </div>
            </bubble-menu>
          </el-scrollbar>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="flex gap-3 justify-end">
        <el-button @click="dialogVisible = false"> Close </el-button>
        <el-button type="primary" @click="handleSubmit"> Update </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, computed, nextTick, watch } from 'vue'
import MarkdownIt from 'markdown-it'
import markdownItSanitizer from 'markdown-it-sanitizer'
import { Languages } from '@/enums/edp'
import Editor from '@/views/edp/chatbot/Editor/index.vue'
import { BubbleMenu } from '@tiptap/vue-3'
import { JDOptimizeApi } from '@/api/edp/chat'
import { useClipboard } from '@vueuse/core'
import { ElMessage } from 'element-plus'
import { Loading } from '@element-plus/icons-vue'
import { formatDate } from '@/utils/formatTime'

const dialogVisible = ref(false)
const leftEditorRef = ref(null)
const rightEditorRef = ref(null)
const leftEditorSelectedContent = ref('')
const rightEditorSelectedContent = ref('')
const optimizeAnswer = ref('')
const optimizeQuestion = ref('')
const optimizing = ref(false)
const optimizationInProgress = ref(false)
const optimizationInAbortController = ref<AbortController | null>(null)
const isPreviewLoading = ref(false)
const lastOptimizePrompt = ref('')
const { copy } = useClipboard()
const isCopied = ref(false)

const emit = defineEmits(['update:content'])

const props = defineProps({
  content: {
    type: String,
    default: ''
  },
  latestVersion: {
    type: Object,
    default: null
  },
  language: {
    type: Number,
    default: Languages.English
  },
  disabled: {
    type: Boolean,
    default: false
  },
  positionId: {
    type: [String, Number, Object],
    default: null
  },
  positionName: {
    type: String,
    default: ''
  },
  jDEditTime: {
    type: String,
    default: ''
  }
})

// 创建Markdown解析器
const markdown = new MarkdownIt({
  html: false,
  linkify: true,
  typographer: true
}).use(markdownItSanitizer)

// 根据语言设置文本方向
const textDirection = computed(() => {
  return props.language === Languages.Arabic ? 'rtl' : 'ltr'
})

// 根据当前语言获取LatestVersion的对应内容
const latestVersionContent = computed(() => {
  if (!props.latestVersion) return ''

  switch (props.language) {
    case Languages.English:
      return props.latestVersion.enContent || ''
    case Languages.Chinese:
      return props.latestVersion.chContent || ''
    case Languages.Arabic:
      return props.latestVersion.arContent || ''
    default:
      return props.latestVersion.enContent || ''
  }
})

// 将 Markdown 转换为 HTML 的方法
const renderMarkdown = (text: string) => {
  if (!text) return ''
  const html = markdown.render(text)
  return `<div class="markdown-content">${html}</div>`
}

// 监听dialog显示状态，设置编辑器内容
watch(dialogVisible, async (visible) => {
  if (visible) {
    await nextTick()

    // 设置左侧编辑器内容 - LatestVersion
    if (leftEditorRef.value && latestVersionContent.value) {
      const leftEditorInstance = leftEditorRef.value as any
      if (leftEditorInstance.editor) {
        const isHtml =
          latestVersionContent.value.startsWith('<') && latestVersionContent.value.includes('</')
        const content = isHtml
          ? latestVersionContent.value
          : markdown.render(latestVersionContent.value)
        leftEditorInstance.editor.commands.setContent(content)
      }
    }

    // 设置右侧编辑器内容 - 当前内容
    if (rightEditorRef.value && props.content) {
      const rightEditorInstance = rightEditorRef.value as any
      if (rightEditorInstance.editor) {
        const isHtml = props.content.startsWith('<') && props.content.includes('</')
        const content = isHtml ? props.content : markdown.render(props.content)
        rightEditorInstance.editor.commands.setContent(content)
      }
    }
  }
})

// JD优化发送预处理
const btnJDOptimize = () => {
  if (optimizeQuestion.value) {
    handleJDOptimize()
  }
}

// 一键提示词：设置提示词并立即触发优化
const applyOptimizePrompt = (prompt: string) => {
  if (optimizing.value) return
  optimizeQuestion.value = prompt
  btnJDOptimize()
}

// 处理JD优化
const handleJDOptimize = async () => {
  if (!rightEditorSelectedContent.value || optimizeQuestion.value === '') return

  optimizing.value = true
  optimizationInProgress.value = true

  const optimizeInput = optimizeQuestion.value
  lastOptimizePrompt.value = optimizeInput
  optimizeQuestion.value = ''

  optimizeAnswer.value = ''

  optimizationInAbortController.value = new AbortController()

  let lastContent = ''

  const onMessage = (event: MessageEvent) => {
    try {
      const data = JSON.parse(event.data)
      const rawContent = data.data.answer || ''

      if (rawContent.includes('is running...') || rawContent.trim() === '') {
        return
      }

      optimizeAnswer.value = rawContent
      lastContent = rawContent
    } catch (error) {
      console.error('处理优化返回数据失败:', error)
    }
  }

  const onError = (error: Event) => {
    console.error('优化请求错误:', error)
    ElMessage.error('Optimization failed')
    stopOptimization()
  }

  const onClose = () => {
    stopOptimization()
  }

  try {
    const requestData: any = {
      positionId: props.positionId,
      positionName: props.positionName,
      content: rightEditorSelectedContent.value,
      question: optimizeInput,
      source: 2,
      type: 2
    }

    await JDOptimizeApi.getJDOptimization(
      requestData,
      optimizationInAbortController.value,
      onMessage,
      onError,
      onClose
    )
  } catch (error) {
    console.error('优化过程发生错误:', error)
    ElMessage.error('Optimization process failed')
    stopOptimization()
  }
}

// 停止优化流
const stopOptimization = () => {
  if (optimizationInAbortController.value) {
    optimizationInAbortController.value.abort()
  }
  optimizationInProgress.value = false
  optimizing.value = false
}

// JD优化内容替换
const handleReplace = () => {
  if (!optimizeAnswer.value || !rightEditorRef.value) return

  try {
    const editorInstance = rightEditorRef.value as any
    if (editorInstance.editor && rightEditorSelectedContent.value) {
      const optimizedHtml = renderMarkdown(optimizeAnswer.value)
        .replace('<div class="markdown-content">', '')
        .replace('</div>', '')

      editorInstance.editor.commands.deleteSelection()
      editorInstance.editor.commands.insertContent(optimizedHtml)

      rightEditorSelectedContent.value = ''
      optimizeAnswer.value = ''

      ElMessage.success('Content replaced successfully')
    } else {
      ElMessage.warning('Editor not initialized or no selected content')
    }
  } catch (error) {
    console.error('替换内容失败', error)
    ElMessage.error('Content replacement failed')
  }
}

// 处理优化复制方法
const handleCopyOptimize = async () => {
  if (!optimizeAnswer.value) return

  try {
    const optimizedHtml = renderMarkdown(optimizeAnswer.value)
    const tempDiv = document.createElement('div')
    tempDiv.innerHTML = optimizedHtml

    const paragraphs = tempDiv.getElementsByTagName('p')
    let plainText = ''
    for (let i = 0; i < paragraphs.length; i++) {
      const text = paragraphs[i].textContent || ''
      if (i < paragraphs.length - 1) {
        plainText += text + '\n'
      } else {
        plainText += text
      }
    }

    plainText = plainText.trim()
    await copy(plainText)
    isCopied.value = true
    ElMessage.success('Optimization content copied')
    setTimeout(() => {
      isCopied.value = false
    }, 2000)
  } catch (error) {
    console.error('Failed to copy optimization content:', error)
    ElMessage.error('Copy failed')
  }
}

// 重新生成JD优化内容
const handleRegenerateOptimize = async () => {
  if (!rightEditorSelectedContent.value) {
    ElMessage.warning('Please select content to optimize')
    return
  }

  if (!lastOptimizePrompt.value && !optimizeQuestion.value) {
    ElMessage.warning('No previous optimization prompt found')
    return
  }

  if (optimizing.value) return

  if (optimizeQuestion.value) {
    lastOptimizePrompt.value = optimizeQuestion.value
    optimizeQuestion.value = ''
  }

  ElMessage.info('Regenerating optimization...')
  optimizeAnswer.value = ''

  const tempQuestion = optimizeQuestion.value
  optimizeQuestion.value = lastOptimizePrompt.value

  await handleJDOptimize()
  optimizeQuestion.value = tempQuestion
}

// 处理提交更新
const handleSubmit = () => {
  if (rightEditorRef.value) {
    const editorInstance = rightEditorRef.value as any
    if (editorInstance.editor) {
      const updatedContent = editorInstance.editor.getHTML()
      emit('update:content', updatedContent)
      dialogVisible.value = false
      ElMessage.success('Content updated successfully')
    }
  }
}
</script>

<style lang="scss" scoped>
/* 去除按钮左外边距 */
:deep(.el-button + .el-button) {
  margin-left: 0 !important;
}

/* Apply border-radius to the input */
.chat-input :deep(.el-input__wrapper) {
  border-radius: 12px;
  border: 1px solid #e7e7e7;
  box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
}

/* Apply border only when focused */
.chat-input :deep(.el-input__wrapper.is-focus) {
  box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.1);
  border: 1px solid #017b3d !important;
}

/* Markdown 样式 */
:deep(.markdown-content) {
  line-height: 1.6;
  width: 100%;
  overflow-wrap: break-word;
}

:deep(.markdown-content p) {
  margin: 0.5em 0;
}

:deep(.markdown-content a) {
  color: #017b3d;
  text-decoration: none;
}

:deep(.markdown-content code) {
  background-color: #f5f5f5;
  padding: 0.2em 0.4em;
  border-radius: 3px;
  font-family: monospace;
}

:deep(.markdown-content pre) {
  background-color: #f5f5f5;
  padding: 1em;
  border-radius: 5px;
  overflow-x: auto;
}

:deep(.markdown-content ul, .markdown-content ol) {
  padding-left: 2em;
  margin: 0.5em 0;
  box-sizing: border-box;
}

:deep(.markdown-content ol) {
  list-style-position: inside;
}

:deep(.markdown-content li) {
  margin-bottom: 0.3em;
  position: relative;
}

:deep(.markdown-content blockquote) {
  border-left: 4px solid #ddd;
  padding-left: 1em;
  margin-left: 0;
  color: #666;
}

/* 确保编辑器内容区域有足够的内边距 */
.tiptap-editor-container :deep(.ProseMirror) {
  padding: 0 !important;
  outline: none !important; /* 去除蓝色边框 */
}

/* 移除所有编辑区域的原生蓝色边框 */
.tiptap-editor-container :deep(*:focus),
.tiptap-editor-container :deep(*:focus-visible) {
  outline: none !important;
  box-shadow: none !important;
  border-color: transparent !important;
}

/* 移除特定工具栏输入框的边框和阴影 */
.toolbar-input :deep(.el-input__wrapper) {
  border: none !important;
  box-shadow: none !important;
  border-radius: 12px;
}

.toolbar-input :deep(.el-input__wrapper.is-focus) {
  border: none !important;
  box-shadow: none !important;
}

/* Loading dots 波浪动画 */
.loading-dot {
  animation: wave 1s ease-in-out infinite;
}

@keyframes wave {
  0%,
  60%,
  100% {
    transform: translateY(0);
  }
  30% {
    transform: translateY(-4px);
  }
}

/* 针对 Diff 组件的特殊样式调整 */
.tiptap-editor-container {
  width: 100%;
  height: 100%;
}

/* 左右对比布局样式 */
.diff-container {
  gap: 16px;
}

.editor-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.panel-header {
  flex-shrink: 0;
  font-size: 14px;
  font-weight: 600;
}

.editor-panel .tiptap-editor-container {
  flex: 1;
  height: calc(100% - 40px);
}

/* 编辑器的markdown包装器样式 */
:deep(.markdown-wrapper) {
  line-height: 1.6;
  width: 100%;
  overflow-wrap: break-word;
}

:deep(.markdown-wrapper p) {
  margin: 0.5em 0;
}

:deep(.markdown-wrapper a) {
  color: #017b3d;
  text-decoration: none;
}

:deep(.markdown-wrapper code) {
  background-color: #f5f5f5;
  padding: 0.2em 0.4em;
  border-radius: 3px;
  font-family: monospace;
}

:deep(.markdown-wrapper pre) {
  background-color: #f5f5f5;
  padding: 1em;
  border-radius: 5px;
  overflow-x: auto;
}

:deep(.markdown-wrapper ul, .markdown-wrapper ol) {
  padding-left: 2em;
  margin: 0.5em 0;
  box-sizing: border-box;
}

:deep(.markdown-wrapper ol) {
  list-style-position: inside;
}

:deep(.markdown-wrapper li) {
  margin-bottom: 0.3em;
  position: relative;
}

:deep(.markdown-wrapper blockquote) {
  border-left: 4px solid #ddd;
  padding-left: 1em;
  margin-left: 0;
  color: #666;
}
</style>