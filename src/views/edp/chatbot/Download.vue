<script lang="ts" setup>
import { ref, computed } from 'vue'

const props = defineProps({
  // 是否显示对话框
  centerDialogVisible: {
    type: Boolean,
    required: true
  }
})

const emit = defineEmits(['update:centerDialogVisible', 'download'])

// 文件类型选择
const selectedFileType = ref('word')

// 文件类型选项配置
const fileTypeOptions = [
  {
    label: 'Word',
    value: 'word',
    description: 'Download as a Word document. Best for editing and making further modifications.',
    icon: 'ep:tickets'
  },
  {
    label: 'PDF',
    value: 'pdf',
    description:
      'Download as a PDF document. Best for viewing and printing with preserved formatting.',
    icon: 'ep:document'
  }
]

// 计算属性处理 v-model
const dialogVisible = computed({
  get: () => props.centerDialogVisible,
  set: (value) => emit('update:centerDialogVisible', value)
})

// 取消操作
const handleCancel = () => {
  emit('update:centerDialogVisible', false)
}

// 下载操作
const handleDownload = () => {
  // 向父组件发射download事件，传递选择的文件类型
  emit('download', selectedFileType.value)
  // 关闭对话框
  emit('update:centerDialogVisible', false)
}
</script>

<template>
  <el-dialog v-model="dialogVisible" width="800" align-center class="!rounded-xl !p-6">
    <template #header>
      <div class="flex flex-col items-center justify-center text-center p-8 !pr-1 !pb-0">
        <span class="text-lg font-semibold text-black"> Job Description Download </span>
        <span class="text-gray-500 mt-2">
          Please select the type of file you would like to download
        </span>
      </div>
    </template>

    <template #default>
      <!-- 文件选择类型卡片 -->
      <div class="flex flex-col md:flex-row gap-4 mt-8">
        <el-card
          v-for="option in fileTypeOptions"
          :key="option.value"
          :class="[
            'w-full md:flex-1 cursor-pointer !rounded-lg transition-all duration-300',
            selectedFileType === option.value
              ? '!border-[#017B3D] border-2 !shadow-[0_2px_8px_rgba(1,123,61,0.15)]'
              : 'hover:!border-gray-300'
          ]"
          shadow="never"
          @click="selectedFileType = option.value"
        >
          <div class="flex gap-5 p-6 min-h-[120px] items-start">
            <!--Icon-->
            <Icon
              :icon="option.icon"
              :size="24"
              :class="[
                'flex-shrink-0 mt-[3px]',
                selectedFileType === option.value ? 'text-[#017B3D]' : 'text-gray-400'
              ]"
            />

            <!--文件类型信息-->
            <div class="flex-1 flex flex-col justify-between h-full">
              <div
                :class="[
                  'text-lg font-medium mb-2',
                  selectedFileType === option.value ? 'text-[#017B3D]' : 'text-gray-800'
                ]"
              >
                {{ option.label }}
              </div>
              <div class="text-xs text-gray-600 text-justify">
                {{ option.description }}
              </div>
            </div>
          </div>
        </el-card>
      </div>
    </template>

    <template #footer>
      <div class="dialog-footer mt-1">
        <el-button class="mr-2" @click="handleCancel"> Cancel </el-button>
        <el-button type="primary" @click="handleDownload">
          <Icon icon="ep:download" class="mr-1" />
          Download
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped>
:deep(.el-step__description) {
  padding-right: 1% !important;
}
</style>