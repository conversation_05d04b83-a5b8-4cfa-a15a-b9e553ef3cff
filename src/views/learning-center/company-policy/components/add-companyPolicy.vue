<script setup lang="ts" name="AddCompanyPolicy">
import type { FormInstance } from 'element-plus'
import type { ComponentInternalInstance } from 'vue'
import { formatChapterType } from '@/views/learning-center/course/course-form/components/CourseCatalogue/CourseDialog/utils'
import Assign from './assign.vue'
import { getAccessToken } from '@/utils/auth'
import { addCompanyPolicy, detailCompanyPolicy, editCompanyPolicy } from '@/api/topicMgt/company-policy'
import { getOnboardingCategory } from '@/api/topicMgt/onboarding'
import { PDF, Video } from '@/components/LargeFileUpload/script/FileAllowTypes'
import { getDuration, secondToTime, switchSecond } from '@/api/category/tool'
import CourseResource from '@/components/CourseResource/index.vue'
import type { CourseOrigin } from '@/enums/resource'
import { MediaType } from '@/enums/resource'
import { useTagsViewStore } from "@/store/modules/tagsView"
const { delView } = useTagsViewStore() // 视图操作
const route = useRoute()
const { router, push, currentRoute } = useRouter() // 路由
interface SelectedResource {
  origin: CourseOrigin//
  format: string | undefined
  mediaType: MediaType
  lang: string[]
  duration: number
  fileId: number | undefined
  size: number | undefined
  url: string | undefined
  name: string | undefined
  resourceId: number | undefined
}
interface FileListData {
  type?: string | undefined
  fileId?: string | undefined
  fileUrl?: string | undefined
  name?: string | undefined
  fileName?: string | undefined
  orientationId?: string | undefined
  duration?: number | undefined
}
interface FormData {
  title?: string | undefined
  cover?: string | undefined
  file?: string | undefined
  ack: boolean
  sort?: number | undefined
  // estimateTime: Date | undefined
  // estimateTimeTemp: Date | undefined
  declaration?: string | undefined
  content?: string | undefined
  attachmentList?: [] | undefined
  muploader?: FileListData[] | undefined
  keywords: string | []
  fileId?: string | undefined
  fileUrl?: string | undefined
  fileName?: string | undefined
  duration?: number | undefined
  origin?: string | number | undefined
  lang?: string | undefined
  size?: number | undefined
  format?: string | undefined
  mediaType?: string | undefined
}
interface ResourceItem {
  type?: string | undefined
  fileId?: string | undefined
  fileUrl?: string | undefined
  name?: string | undefined
  fileName?: string | undefined
  orientationId?: string | undefined
  duration?: number | undefined
  origin?: string | number | undefined
  lang?: string | undefined
  size?: number | undefined
  format?: string | undefined
  mediaType?: string | undefined
}

const form = ref<FormData>({
  title: '',
  cover: '',
  file: '',
  ack: true,
  // estimateTime: undefined,
  sort: 1,
  declaration: '',
  content: '',
  attachmentList: [],
  muploader: [],
  // estimateTimeTemp: undefined,
  keywords: [],
  fileId: '',
  fileUrl: '',
  fileName: '',
  origin: 0,
  lang: '1',
  duration: 0,
  size: 0,
  format: '',
  mediaType: '',
})

const { t } = useI18n()
const message = useMessage() // 消息弹窗
const formRef = ref<FormInstance>()
const rules = {
  ack: [{ required: false, message: t('learningCenter.companyPolicy.ackRule'), trigger: 'change' }],
  declaration: [{ required: true, message: t('learningCenter.companyPolicy.declarationRule'), trigger: 'blur' }],
  title: [{ required: true, message: t('learningCenter.companyPolicy.titleRule'), trigger: 'blur' }],
  categoryId: [{ required: true, message: t('learningCenter.companyPolicy.categoryIdRule'), trigger: 'change' }],
  cover: [{ required: true, message: t('learningCenter.companyPolicy.coverRule'), trigger: ['blur', 'change'] }],
  muploader: [{ required: true, message: t('learningCenter.companyPolicy.muploaderRule'), trigger: ['blur', 'change'] }],
  duration: [{ required: true, message: t('learningCenter.companyPolicy.durationRule'), trigger: ['blur', 'change'] }]
}
const categoryList = ref()
const menu_step2_Btndisabled = ref(true)
const activeIndex = ref('1')
const canSetDuration = ref(false)
/**
 * isInit 区分新建、编辑;新建true
 * 新建时，需要对时间值进行转换
 * 编辑时，不需要转换，直接赋值即可
 *
 */
const isInit = ref(true)
const resqID = ref()
const detailId = route.params.id ? route.params.id : resqID.value

const inputValue = ref('')
const inputVisible = ref()
const InputRef = ref()
const repeatKeyword = ref(false)
const ackDuration = ref(false)

const resourceCompData = ref<SelectedResource[]>([])

// 使用 MediaType 枚举的键来获取对应的标签值
const getMediaTypeLabel = (mediaType: number): string => {
  return Object.keys(MediaType).find(key => MediaType[key as keyof typeof MediaType] === mediaType) ?? ''
}

const showInput = () => {
  inputVisible.value = true
  nextTick(() => {
    InputRef.value!.input!.focus()
  })
}
const handleInputConfirm = () => {
  if (form.value.keywords.includes(inputValue.value)) {
    repeatKeyword.value = true
    return
  }
  repeatKeyword.value = false
  if (inputValue.value) {
    form.value.keywords.push(inputValue.value)
  }
  inputVisible.value = false
  inputValue.value = ''
}

const handleCloseTag = (tag: string) => {
  form.value.keywords.splice(form.value.keywords.indexOf(tag), 1)
}
/** 表单重置 */
const reset = () => {
  form.value = {
    title: undefined,
    cover: undefined,
    file: undefined,
    ack: false,
    // estimateTime: undefined,
    sort: 1,
    declaration: undefined,
    content: undefined,
    attachmentList: [],
    muploader: [],
    keywords: [],
    // estimateTimeTemp: undefined,
    fileId: '',
    fileName: '',
    fileUrl: '',
    origin: 0,
    lang: '1',
    duration: 0,
    size: 0,
    format: '',
    mediaType: '',
  }
  formRef.value?.resetFields()
}
const handleSelect = (key: string) => {
  if (key === '1') {
    if (route.params.id || resqID.value) {
      const paramId = route.params.id ? route.params.id : resqID.value
      getDetailList(paramId)
    }
    else {
      reset()
    }
  }
  activeIndex.value = key
}
// 获取编辑回显的详细信息
const getDetailList = async (paramId: number | string) => {
  reset()
  const data = await detailCompanyPolicy(paramId)
  isInit.value = true
  form.value = data
  if (data.attachmentList && data.attachmentList.length > 0) {
    form.value.file = data.attachmentList[0].fileName
    /** Start:Upload资源回显 */
    resourceCompData.value = [
      {
        origin: data.origin,
        format: data.format,
        mediaType: data.mediaType,
        lang: data.lang.split(','),
        duration: data.duration,
        fileId: data.attachmentList[0].fileId,
        size: data.attachmentList[0].size,
        url: data.attachmentList[0].fileUrl,
        name: data.attachmentList[0].fileName,
        resourceId: data.attachmentList[0].resourceId,
      },
    ]
    /** End:Upload资源回显 */
  }
  form.value.muploader = resourceCompData.value
  form.value.keywords = data.keywords ? data.keywords.split(',') : []
  menu_step2_Btndisabled.value = false
}
/** 查询category列表，没有分页 */
const getOnboardingCategoryList = async () => {
  categoryList.value = await getOnboardingCategory()
}
/** 获取封面图片字符串 */
const handleCover = (event: string) => {
  form.value.cover = event
}
// 上传的文件参数-内层参数
const fileParamsFun = () => {
  const fileListValue = resourceCompData.value?.map((item: SelectedResource) => {
    // if (!item.duration) {
    //   if (item[0].mediaType === 1) {
    //     item[0].duration = 600
    //   }
    //   else if (item[0].mediaType === 3) {
    //     item[0].duration = 300
    //   }
    // }
    return {
      origin: item?.origin,
      format: item?.format ?? getMediaTypeLabel(item?.mediaType),
      mediaType: formatChapterType(item?.url),
      lang: (item?.lang ?? ['1']).toString(),
      duration: item?.duration, // duration没有值的话，默认赋值5分钟
      fileId: item.fileId ?? item.id,
      size: item?.size,
      fileUrl: item.fileUrl ? item.fileUrl : item.url,
      fileName: item.fileName ? item.fileName : item.name,
      resourceId: item?.resourceId,
      fileType: item?.format, // 学员端列表页图片上的标签值，仅OOC使用
    }
  })

  form.value.attachmentList = fileListValue

  return form.value
}
/** 提交 */
const handleConfirm = async () => {
  const valid = await formRef.value.validate()
  if (!valid) return
  try {
    const keywords = form.value.keywords.join(',')
    // keywords有重复的，提交时，去掉重复值
    if (repeatKeyword.value) {
      inputVisible.value = false
      repeatKeyword.value = false
      inputValue.value = ''
    }
    /** Start-提交的上传资源的参数 */
    form.value.origin = resourceCompData.value[0]?.origin
    form.value.format = resourceCompData.value[0]?.format ?? getMediaTypeLabel(resourceCompData.value[0]?.mediaType)
    form.value.mediaType = formatChapterType(resourceCompData.value[0]?.url)
    form.value.lang = (resourceCompData.value[0]?.lang ?? ['1']).toString()
    form.value.duration = resourceCompData.value[0]?.duration
    form.value.fileId = resourceCompData.value[0]?.fileId
    form.value.size = resourceCompData.value[0]?.size
    form.value.fileUrl = resourceCompData.value[0]?.url
    form.value.fileName = resourceCompData.value[0]?.name
    form.value.resourceId = resourceCompData.value[0]?.resourceId

    form.value.fileType = resourceCompData.value[0]?.format // 学员端列表页图片上的标签值，仅OOC使用
    /** End-提交的上传资源的参数 */

    // delete form.value.muploader

    if (form.value.ack && !form.value.duration) {
      ackDuration.value = true
      return
    }
    if (form.value.ack && !form.value.duration) {
      ackDuration.value = true
      return
    }
    if (route.params.id || resqID.value) {
      fileParamsFun()
      await editCompanyPolicy({ ...form.value, keywords })
      message.success(t('common.updateSuccess'))
    }
    else {
      fileParamsFun()
      const data = await addCompanyPolicy({ ...form.value, keywords })
      message.success(t('common.createSuccess'))
      handleSelect('2')
      resqID.value = data
      menu_step2_Btndisabled.value = false
    }
  } finally {}
}
/** 跳转添加页面 */
const handleCancel = () => {
  delView(unref(currentRoute))
  push('/learning-center/companyPolicy')
}
watch(() => resourceCompData, (newProps: any) => {
  form.value.muploader = newProps
}, {
  immediate: true,
  deep: true,
})

onMounted(() => {
  // 表单重置
  reset()
  resqID.value = ''
  handleSelect('1')
  menu_step2_Btndisabled.value = true
  if (route.params.id) {
    menu_step2_Btndisabled.value = false
  }
})
onMounted(() => {
  getOnboardingCategoryList()
})

</script>

<template>
  <div class="app-container">
    <!-- 顶部返回 -->
    <div class="w-full h-[140px] bg-[#E4F4EE] flex items-center rounded-[10px] px-4">
      <span class="text-2xl">
        {{ detailId ? t('learningCenter.companyPolicy.editCompanyPolicy') : t('learningCenter.companyPolicy.addCompanyPolicy') }}
      </span>
      <el-button class="ms-auto" type="primary" @click="handleCancel">
        {{ t('action.back') }}
      </el-button>
    </div>
    <div class="flex mt-6 gap-5">
      <ContentWrap>
        <div class="w-[220px] shrink-0 rounded-x p-2.5 border border-[#CDDBF1]">
          <el-menu :default-active="activeIndex" @select="handleSelect">
            <el-menu-item index="1">
              <span>{{ t('learningCenter.course.basicInfo') }}</span>
            </el-menu-item>
            <el-menu-item index="2" :disabled="menu_step2_Btndisabled">
              <span>{{ t('learningCenter.course.assignScope') }}</span>
            </el-menu-item>
          </el-menu>
        </div>
      </ContentWrap>
      <div class="flex-1 rounded-x border border-[#CDDBF1]">
        <!-- Basic Information -->
        <ContentWrap v-if="activeIndex === '1'">
          <div class="py-5 px-7">
            <h1 class="text-[#222] text-[20px]">
              {{ t('learningCenter.course.basicInfo') }}
            </h1>
            <el-form ref="formRef" :rules="rules" :model="form" class="mt-5 w-3/4" label-width="130px" label-position="left">
              <el-form-item :label="t('learningCenter.journey.title')" prop="title">
                <el-input v-model="form.title" show-word-limit maxlength="200" />
              </el-form-item>
              <el-form-item :label="t('learningCenter.journey.cover')" prop="cover">
                <ImageUpload :model-value="form.cover" :limit="1" @update:model-value="handleCover" />
              </el-form-item>
              <el-form-item :label="t('action.uploadFile')" prop="muploader">
                <CourseResource v-model="resourceCompData" class="w-full" :page-name-type="[...Video, ...PDF]" :ooc="true" :limit="1" :edit="menu_step2_Btndisabled" />
              </el-form-item>
              <!-- <el-form-item v-if="ackDuration">
                <div class=" text-red-500 text-[13px]">
                  <span class="">Please fill in the duration!</span>
                </div>
              </el-form-item> -->

              <el-form-item :label="t('learningCenter.companyPolicy.ack')" prop="ack">
                <el-radio-group v-model="form.ack">
                  <el-radio :value="true" size="large">
                    {{ t('common.yes') }}
                  </el-radio>
                  <el-radio :value="false" size="large">
                    {{ t('common.no') }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>

              <el-form-item :label="t('setting.banner.sort')" prop="sort">
                <el-input-number v-model="form.sort" class="w-full" type="number" :min="0" :max="9999" clearable controls-position="right" />
              </el-form-item>
              <el-form-item prop="keywords" :label="t('category.topic.keyWords')">
                <div>
                  <div class="flex flex-wrap gap-2">
                    <el-tag v-for="tag in form.keywords" :key="tag" closable :disable-transitions="false" @close="handleCloseTag(tag)">
                      {{ tag }}
                    </el-tag>
                    <el-input v-if="inputVisible" ref="InputRef" v-model="inputValue" class="!w-60" size="small" maxlength="50" show-word-limit @keyup.enter="handleInputConfirm" @blur="handleInputConfirm" />
                    <el-button v-else-if="!inputVisible && (form.keywords.length < 10)" class="button-new-tag" size="small" @click="showInput">
                      {{ t('action.addKeyWord') }}
                    </el-button>
                  </div>
                  <div v-if="repeatKeyword" class="text-[#f56c6c] text-xs mt-2">
                    {{ t('common.keyWords') }}
                  </div>
                </div>
              </el-form-item>
              <!-- <el-form-item v-if="form.ack" label="Estimated Time" prop="estimateTime">
                <el-time-picker v-model="form.estimateTimeTemp" placeholder="Acknowledge-Estimated Time" style="width: 500px" :disabled="canSetDuration" :default-value="new Date(0, 0, 0, 0, 0, 0)" />
              </el-form-item> -->
              <el-form-item v-if="form.ack" :label="t('learningCenter.companyPolicy.declaration')" :prop="form.ack ? 'declaration' : ''">
                <el-input v-model="form.declaration" type="textarea" :rows="5" show-word-limit maxlength="5000" />
              </el-form-item>
              <el-form-item :label="t('learningCenter.boarding.description')" prop="content">
                <el-input v-model="form.content" type="textarea" :rows="5" show-word-limit maxlength="5000" />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="handleConfirm">
                  {{ t('global.confirm') }}
                </el-button>
                <el-button type="primary" plain @click="handleCancel">
                  {{ t('global.cancel') }}
                </el-button>
              </el-form-item>
            </el-form>
          </div>
        </ContentWrap>
        <!-- Assign -->
        <Assign v-if="activeIndex === '2'" :step-id="resqID" />
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.el-menu-item {
  height: 34px;
}
:deep(.el-menu) {
  border-right: 0 !important;
  .el-menu-item {
    &.is-active {
      background-color: #E4F4EE;
      border-radius: 4px;
    }
  }
}
</style>
