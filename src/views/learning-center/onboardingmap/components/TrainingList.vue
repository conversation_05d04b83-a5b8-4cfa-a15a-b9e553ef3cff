<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="Category" prop="categoryId">
        <el-tree-select
          v-model="queryParams.categoryId"
          :data="categoryList"
          :props="defaultProps"
          filterable
          clearable
          check-strictly
          default-expand-all
          value-key="deptId"
          placeholder="Please select"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="Language" prop="language">
        <el-select
          v-model="queryParams.language"
          placeholder="Please select"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.TRAINING_COURSE_LANGUAGE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="Type" prop="trainerType">
        <el-select
          v-model="queryParams.trainerType"
          placeholder="Please select"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.TRAINING_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="Title" prop="title">
        <el-input
          v-model="queryParams.title"
          placeholder="Please input"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> Search </el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> Reset </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" ref="trainingRef" :show-overflow-tooltip="true" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" fixed="left" />
      <el-table-column label="Course Title" align="center" prop="title" min-width="180px" />
      <el-table-column label="Course Code" align="center" prop="code" min-width="180px">
        <template #default="scope">
          MJN-{{ getCode(scope.row.categoryFullPath) }}-{{ scope.row.code }}
        </template>
      </el-table-column>
      <el-table-column label="Category" align="center" prop="categoryFullPath" min-width="180px" />
      <el-table-column label="Language" align="center" prop="language" min-width="180px">
        <template #default="scope">
          <el-tag v-for="item in scope.row.languageStr" :key="item" type="primary" class="me-1">
            {{ item }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="Trainer Type" align="center" prop="trainerType" min-width="180px">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.TRAINING_TYPE" :value="scope.row.trainerType" min-width="180px" />
        </template>
      </el-table-column>
      <el-table-column label="Approvals" align="center" min-width="180px">
        <template #default="scope">
          {{ scope.row.isApprovals ? 'Yes' : 'No' }}
        </template>
      </el-table-column>
      <el-table-column label="Validity" align="center" prop="validity" min-width="180px">
        <template #default="scope">
          {{ scope.row.validity }} Months
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->

  <Dialog v-model="showCourse" title="New Course Title">
    <el-form
      ref="formCourseRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
    >
      <el-form-item label="Course Title" prop="title">
        <el-input v-model="formData.title" placeholder="Please input course title" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="showCourse = false">Cancel</el-button>
      <el-button type="primary" :loading="formLoading" @click="submitForm">Save</el-button>
    </template>
  </Dialog>

  <!--  选择人员-->
  <el-drawer v-model="showStudent" :size="800" title="Select Students">
<!--    <el-row :gutter="20">-->
<!--      <el-col :span="24">-->
<!--        <el-radio-group id="exam" v-model="formStudentData.scope" name="exam" @change="changeScope">-->
<!--          <el-radio name="examTrue" :value="1">-->
<!--            All Company Employees-->
<!--          </el-radio>-->
<!--          <el-radio name="examFalse" :value="2">-->
<!--            Specific Employees-->
<!--          </el-radio>-->
<!--        </el-radio-group>-->
<!--      </el-col>-->
<!--      <el-col :span="24" v-show="formStudentData.scope === 2">-->
<!--        <el-input placeholder="Please select the scope" disabled>-->
<!--          <template #suffix>-->
<!--            <Icon icon="ep:more" class="mr-5px cursor-pointer" @click="openScope" />-->
<!--          </template>-->
<!--        </el-input>-->
<!--      </el-col>-->
<!--    </el-row>-->
    <ScopeSelect
      ref="scopeRef"
      v-model="scopeList"
      v-loading="loading"
      :employee-table-props="{
          height: 420,
        }"
      :show-type="false"
      @confirm="handleConfirm"
      @delete="handleStudentDelete"
    />
  </el-drawer>

</template>

<script setup lang="ts">
import { CourseApi, CourseRespVO, CourseSaveVO } from '@/api/academy/course'
// import CourseForm from './CourseForm.vue'
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { listTopic } from "@/api/category/training"
import { handlePhaseTree, handleTree, defaultProps } from "@/utils/tree"
import { NodeType } from "@/components/ScopeSelect/enums/NodeType"
import { deptTreeSelect, getUsers} from "@/api/system/user"
import { listCompany } from "@/api/system/company"
import { ScopeConfirm, ScopeData } from "@/components/ScopeSelect/typings"
import { CourseScopeVO } from "@/api/topicMgt/elearning"
import ScopeSelect from '@/components/ScopeSelect/index.vue'
import {TempContent} from "@/views/learning-center/onboardingmap/components/type";
/** 学院课程信息 列表 */
defineOptions({ name: 'TrainingList' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(false) // 列表的加载中
const list = ref<CourseRespVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  title: undefined,
  categoryId: undefined,
  language: undefined,
  trainerType: undefined
})
const formData = ref({
  title: undefined,
})
const formRules = reactive({
  title: [{ required: true, message: 'Course name cannot be empty', trigger: 'blur' }],
})
const queryFormRef = ref() // 搜索的表单
const formCourseRef = ref()
const formLoading = ref(false)
const showCourse = ref(false)
const showStudent = ref(false)
/** 添加/修改操作 */
const formRef = ref()
const exportLoading = ref(false) // 导出的加载中
const courseInfo = ref({}) // 存储课程详情信息
const categoryList = ref([]) // 课程分类信息

let inited = false
const emits = defineEmits<{ (e: 'selectChange')}>()
const selectedContents = ref<Record<number, CourseRespVO[]>>([])

const trainingRef = ref()

// 监听tempContents的改变，刷新table的selection
const changeTempContents = (newTempContents: TempContent[]) => {
  trainingRef.value!.clearSelection()

  const rowsToSelect = list.value.filter(_training =>
      newTempContents.some(temp => Number(temp.contentId) === Number(_training.id))
  )
  toggleSelection(rowsToSelect, true) // 选中新项
}

const toggleSelection = (rows: CourseRespVO[], isSelected: boolean = true) => {
  if (rows.length === 0) return
  rows.forEach((row) => {
    trainingRef.value!.toggleRowSelection(row, isSelected)
  })
}

function getDifference(
    newItems: TempContent[],
    oldItems: TempContent[]
): { added: TempContent[], removed: TempContent[] } {
  const newIds = new Set(newItems.map(i => i.contentId))
  const oldIds = new Set(oldItems.map(i => i.contentId))

  const added = newItems.filter(item => !oldIds.has(item.contentId))
  const removed = oldItems.filter(item => !newIds.has(item.contentId))

  return { added, removed }
}

/**  已选择的课程ID */
const checkCourse = ref([])
// 分配人form表单
const formStudentData = ref({
  scope: 1,
  courseAssignVO: {
    scope: undefined,
    scopeDetails: [],
  },
})

const companyList = ref([]) // 部门信息
const userList = ref([]) // 用户信息
const scopeList = ref<ScopeData[]>([])
const dialogLoading = ref(false)
const trainingCourseId = ref()
/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await CourseApi.getCoursePage(queryParams)
    list.value = data.list
    list.value = data.list.map((item: CourseRespVO) => {
      return {
        ...item,
        isApprovals: item.ifmsApprovalKey || item.contractorApprovalKey ? 1 : 0,
      }
    })
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 复制课程操作 */
const handleCopy = async (id: number) => {
  try {
    formData.value.title = ''
    const data = await CourseApi.getCourse(id)
    // 删除详情信息中的id值
    delete data.id
    courseInfo.value = data
    showCourse.value = true
  } catch {}
}

const submitForm = async () => {
  // 校验表单
  await formCourseRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    courseInfo.value.title = formData.value.title
    // 调用新增接口
    await CourseApi.createCourse(courseInfo.value)
    message.success(t('common.copySuccess'))
    await getList()
    showCourse.value = false
  } finally {
    formLoading.value = false
  }
}

// 分类列表
const getCategory = async () => {
  const data = await listTopic()
  categoryList.value = handleTree(data)
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 查询公司列表  */
const getCompanyList = async () => {
  const data = await listCompany({ pageNum: 1, pageSize: 99999 })
  const companyIds = data.list.map((v) => v.companyId)
  // Todo 是逗号拼接还是整个数组传过去，待和后端沟通
  const userList = await getUsers({ companyIds, pageNum: 1, pageSize: 99999})
  userList.value = userList
}
/** 选择All Company Employees 调用公司接口取出所有id 暂时先不需要*/
const changeScope = async (val: number) => {
  if (val === 1) {
    const modelArray = userList.value.map((v: any) => ({
      relevanceId: v.userId,
      relevanceName: v.nickName,
      scope: NodeType.Employee,
    }))
    formStudentData.value.courseAssignVO.scopeDetails = modelArray
  } else {
    formStudentData.value.courseAssignVO = undefined
  }
}
// 查询学院课程已分配列表
const getAssignList = async (courseId: number) => {
  scopeList.value = await CourseApi.getCourseAssignPage(courseId)
}

// 打开分配人员弹框
const openStudent = (courseId: number) => {
  trainingCourseId.value = courseId
  getAssignList(courseId)
  showStudent.value = true
}

const handleConfirm = async (data: ScopeConfirm) => {
  const dataParams = data.scopes.map(item => ({
    id: trainingCourseId.value,
    relevanceId: item.relevanceId,
    relevanceName: item.relevanceName,
    scope: item.scope,
    type: 0,
  }))
  dialogLoading.value = true
  try {
    await CourseApi.AssignFun(trainingCourseId.value,data.scope, 0, dataParams)
    message.success(t('common.updateSuccess'))
    getAssignList(trainingCourseId.value)
  } finally {
    dialogLoading.value = false
  }
}

const handleStudentDelete = async (list: CourseScopeVO[] | CourseScopeVO) => {
  let ids: any
  try {
    // 删除的二次确认
    await message.delConfirm(t('common.delOption'))
    ids = Array.isArray(list) ? list.map(item => item.id).join(',') : list.id
    await CourseApi.delAssign(ids)
    message.success(t('common.delSuccess'))
    getAssignList(trainingCourseId.value)
  } catch {}
}


// 截取code值(根据分类名称第一个 - 进行截取拼接)
const getCode = (val: string) => {
  const parts = val.split('-')
  return parts.length > 0 ? parts[0] : val
}


/** 选择条数  */
const handleSelectionChange = (selection: CourseRespVO[]) => {
  selectedContents.value[queryParams.pageNo] = selection
  emits('selectChange')
}
const initList = async () => {
  if (!inited) {
    await getList()
    await getCategory()
    inited = true
  }
}

defineExpose({initList, selectedContents: selectedContents.value, changeTempContents})

/** 初始化 **/
onMounted(() => {
  // 暂时隐藏
  // getCompanyList()

})
</script>