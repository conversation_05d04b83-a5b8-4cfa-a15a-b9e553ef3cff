<script setup lang="ts">
import type { ComponentInternalInstance } from 'vue'
import type { FormInstance } from 'element-plus'
import { omit, toUpper } from 'lodash-es'
import { deleteCompanyPolicy, getCompanyPolicy, updateCompanyPolicy } from '@/api/topicMgt/company-policy'
import { formatImgUrl } from '@/utils'
import { formatBytes, formatSecond } from '@/utils/ruoyi'
import { MediaType } from '@/enums/resource'
import { dateFormatter } from '@/utils/formatTime'
import { getResource } from '@/api/resource/list'
import { DICT_TYPE, getIntDictOptions, getStrDictOptions } from '@/utils/dict'
import {TempContent} from "@/views/learning-center/onboardingmap/components/type";

defineOptions({name:  'PolicyList'})
const { t } = useI18n()
const message = useMessage() // 消息弹窗
const router = useRouter()
const resource_list_type_file_video = [
  {
    label: t('global.video'),
    value: MediaType.Video,
  },
  {
    label: t('global.file'),
    value: MediaType.File,
  },
]
const table = ref<any[]>([])
const queryParams = ref({})
const total = ref(0)
const loading = ref(false)
const formRef = ref<FormInstance>()

let inited = false
const emits = defineEmits<{ (e: 'selectChange')}>()
const selectedContents = ref<Record<number, any[]>>([])

const policyRef = ref()

// 监听tempContents的改变，刷新table的selection
const changeTempContents = (newTempContents: TempContent[]) => {
  policyRef.value!.clearSelection()

  const rowsToSelect = table.value.filter(_policy =>
      newTempContents.some(temp => Number(temp.contentId) === Number(_policy.id))
  )
  toggleSelection(rowsToSelect, true) // 选中新项
}

const toggleSelection = (rows: any[], isSelected: boolean = true) => {
  if (rows.length === 0) return
  rows.forEach((row) => {
    policyRef.value!.toggleRowSelection(row, isSelected)
  })
}

function getDifference(
    newItems: TempContent[],
    oldItems: TempContent[]
): { added: TempContent[], removed: TempContent[] } {
  const newIds = new Set(newItems.map(i => i.contentId))
  const oldIds = new Set(oldItems.map(i => i.contentId))

  const added = newItems.filter(item => !oldIds.has(item.contentId))
  const removed = oldItems.filter(item => !newIds.has(item.contentId))

  return { added, removed }
}

const ackList = [
  {
    value: 'Yes',
    label: t('common.yes'),
  },
  {
    value: 'No',
    label: t('common.no'),
  },
]
// 预览相关
const preview = reactive({
  previewShow: false,
  previewData: undefined,
})
const { previewShow, previewData } = toRefs(preview)
const DURATIONLIST = [
  { id: 1, label: '<15min', durationLower: 0, durationUpper: 900 },
  { id: 2, label: '15 - 30min', durationLower: 900, durationUpper: 1800 },
  { id: 3, label: '30 - 60min', durationLower: 1800, durationUpper: 3600 },
  { id: 4, label: '>60min', durationLower: 3600, durationUpper: 999999 },
]

const formatBg = (type: MediaType) => {
  const color = {
    [MediaType.Video]: 'bg-[#36A5D8]',
    [MediaType.Audio]: 'bg-[#B858F0]',
    [MediaType.File]: 'bg-[#F2A353]',
    [MediaType.Scorm]: 'bg-[#21AC6E]',
  }
  return color[type]
}
/** 查询列表 */
const getCompanyPolicyList = async () => {
  loading.value = true
  try {
    const params = {
      ...omit(queryParams.value, ['duration']),
      durationLower: queryParams.value.duration?.durationLower,
      durationUpper: queryParams.value.duration?.durationUpper,
    }
    const data = await getCompanyPolicy(params)
    table.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}
/** 删除 */
const handleDelete = async (event) => {
  try {
    await message.confirm(`${t('global.deleteTip') + event.title}?`)
    await deleteCompanyPolicy(event.id)
    await getCompanyPolicyList()
    message.success(t('global.deleteSuccess'))
  } catch {}
}
/**
 * 跳转学习记录页面
 */
const handleLearning = (event: any) => {
  router.push({ name: 'CompanyLearningRecords', query: { id: event.id, name: 'company' }})
}
/** 跳转到新增 */
const handleAdd = () => {
  router.push({ name: 'AddCompanyPolicy' })
}
const handleEdit = (event: any) => {
  router.push({ name: 'AddCompanyPolicy', params: { id: event.id } })
}
const handleSearch = () => {
  getCompanyPolicyList()
}
const handleRefresh = () => {
  formRef.value?.resetFields()
  queryParams.value.pageNo = 1
  queryParams.value.pageSize = 10
  handleSearch()
}

const handlePreview = async (row: any) => {
  loading.value = true
  try {
    previewData.value = await getResource(row.id)
    preview.previewShow = true
  } finally {
    loading.value = false
  }
}

/** 选择条数  */
const handleSelectionChange = (selection: any[]) => {
  selectedContents.value[queryParams.value.pageNo] = selection
  emits('selectChange')
}

const initList = async () => {
  if (!inited) {
    await getCompanyPolicyList()
    inited = true
  }
}

defineExpose( {initList, selectedContents: selectedContents.value, changeTempContents})

onMounted(() => {
})
</script>

<template>
  <ContentWrap>
    <el-form ref="formRef" :model="queryParams" inline label-position="right" label-width="120px" @submit.prevent>
      <!-- <el-row :gutter="24">
        <el-col :span="8"> -->
      <el-form-item :label="t('learningCenter.boarding.title')" prop="title">
        <el-input v-model="queryParams.title" :placeholder="t('common.inputText')" clearable class="!w-300px" @keyup.enter="handleSearch" />
      </el-form-item>
      <!-- </el-col>
        <el-col :span="8"> -->
      <el-form-item :label="t('learningCenter.companyPolicy.acknowledgement')" prop="ack">
        <el-select v-model="queryParams.ack" :placeholder="t('common.selectText')" clearable class="!w-300px">
          <el-option v-for="item in ackList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <!-- </el-col>
      <el-col :span="8"> -->
      <el-form-item :label="t('learningCenter.course.format')" prop="mediaType">
        <el-select v-model="queryParams.mediaType" :placeholder="t('common.selectText')" clearable class="!w-300px">
          <el-option v-for="item in getIntDictOptions(DICT_TYPE.RESOURCE_LIST_TYPE)" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <!-- </el-col>
      <el-col :span="8"> -->
      <el-form-item :label="t('learningCenter.course.language')" prop="lang">
        <el-select v-model="queryParams.lang" :placeholder="t('common.selectText')" clearable class="!w-300px">
          <el-option v-for="item in getIntDictOptions(DICT_TYPE.RESOURCE_LIST_LANG)" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-form-item :label="t('learningCenter.course.duration')" prop="duration">
          <el-select v-model="queryParams.duration" clearable value-key="id" class="!w-300px">
            <el-option v-for="item in DURATIONLIST" :key="item.id" :label="item.label" :value="item" />
          </el-select>
        </el-form-item>
        <el-button type="primary" @click="handleSearch">
          <Icon class="mr-5px" icon="ep:search" />
          {{ t('action.search') }}
        </el-button>
        <el-button @click="handleRefresh">
          <Icon class="mr-5px" icon="ep:refresh" />
          {{ t('action.reset') }}
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>
  <ContentWrap>
    <el-table v-loading="loading" ref="policyRef" :data="table" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="50" align="center" />
      <el-table-column :label="t('learningCenter.boarding.title')" prop="title">
        <template #default="{ row }">
          <div>
            <div class="flex flex-row">
              <el-image :src="formatImgUrl(row.cover)" class="w-[86px] h-[50px]" />
              <div class="pl-5 w-[230px]">
                <el-tooltip
                  :content="row.title" popper-class="tooltip-mywidth"
                  effect="dark" placement="top"
                >
                  <div class="break-all line-clamp-3">
                    {{ row.title }}
                  </div>
                </el-tooltip>
              </div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column :label="t('learningCenter.course.duration')" align="center" :width="100">
        <template #default="{ row }">
          <span>{{ row.duration !== null && row.duration > 0 ? formatSecond(row.duration) : '' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="t('learningCenter.course.language')" align="left" width="100">
        <template #default="{ row }">
          <dict-tag :type="DICT_TYPE.RESOURCE_LIST_LANG" :value="row.lang" />
        </template>
      </el-table-column>
      <el-table-column prop="mediaType" :label="t('learningCenter.course.format')" align="center" :width="90">
        <template #default="{ row }">
          <div
            v-if="row?.mediaType"
            class="size-11 text-sm text-white rounded-full flex items-center justify-center flex-shrink-0 m-auto relative overflow-hidden group"
            :class="formatBg(row.mediaType)"
          >
            <!-- @click="handlePreview(row)" -->
            {{ toUpper(row.format) }}
            <!--   <div class="absolute inset-0 opacity-0 bg-[#131313]/[.5] flex items-center justify-center group-hover:opacity-100 transition-opacity duration-200">
                <el-icon>
                  <View />
                </el-icon>
              </div> -->
          </div>
        </template>
      </el-table-column>
      <el-table-column :label="t('setting.banner.sort')" prop="sort" :width="80" />
      <el-table-column :label="t('category.journey.creator')" prop="createBy" :width="130" />
      <el-table-column :label="t('category.journey.creationTime')" prop="createTime" :width="200" :formatter="dateFormatter" />
    </el-table>
    <pagination v-show="total > 0" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize" :total="total" @pagination="getCompanyPolicyList" />
  </ContentWrap>
</template>

<style scoped lang="scss">
:deep .el-tag + .el-tag{
  margin-left: 0;
  margin-top:2px
  }
</style>
