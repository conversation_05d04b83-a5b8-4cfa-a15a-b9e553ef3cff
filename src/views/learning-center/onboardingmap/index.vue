<template>
<!--  <ContentWrap>
    &lt;!&ndash; 搜索工作栏 &ndash;&gt;
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item :label="t('learningCenter.onboardingMap.name')" prop="name">
        <el-input
          v-model="queryParams.name"
          :placeholder="t('learningCenter.onboardingMap.pleaseInputName')"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item :label="t('learningCenter.onboardingMap.mode')" prop="mode">
        <el-select v-model="queryParams.mode" class="!w-240px" clearable :placeholder="t('learningCenter.onboardingMap.mode')">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.LEARNING_ONBOARDING_MAP_MODE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="t('learningCenter.onboardingMap.effectiveVersion')" prop="effectiveVersion">
        <el-input
          v-model="queryParams.effectiveVersion"
          :placeholder="t('learningCenter.onboardingMap.pleaseInputVersion')"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item :label="t('learningCenter.onboardingMap.createTime')" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          :start-placeholder="t('learningCenter.onboardingMap.startDate')"
          :end-placeholder="t('learningCenter.onboardingMap.endDate')"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-220px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> {{ t('learningCenter.onboardingMap.search') }}</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> {{ t('learningCenter.onboardingMap.reset') }}</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['learning:onboarding-map:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> {{ t('learningCenter.onboardingMap.add') }}
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['learning:onboarding-map:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>-->

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column type="index" :label="t('learningCenter.onboardingMap.index')" width="100" />
      <el-table-column :label="t('learningCenter.onboardingMap.name')" align="center" prop="name" />
      <el-table-column :label="t('learningCenter.onboardingMap.mode')" align="center" prop="mode">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.LEARNING_ONBOARDING_MAP_MODE" :value="scope.row.mode" />
        </template>
      </el-table-column>
<!--      <el-table-column :label="t('learningCenter.onboardingMap.effectiveVersion')" align="center" prop="effectiveVersion" />-->
      <el-table-column
        :label="t('learningCenter.onboardingMap.createTime')"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column :label="t('learningCenter.onboardingMap.action')" align="center" min-width="120px">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="router.push({name: 'OnboardingMapDetail', query: {id: scope.row.id}})"
            v-hasPermi="['learning:onboarding-map:update']"
          >
            {{ t('learningCenter.onboardingMap.detail') }}
          </el-button>
<!--          <el-button-->
<!--            link-->
<!--            type="danger"-->
<!--            @click="handleDelete(scope.row.id)"-->
<!--            v-hasPermi="['learning:onboarding-map:delete']"-->
<!--          >-->
<!--            删除-->
<!--          </el-button>-->
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
<!--    <Pagination-->
<!--      :total="total"-->
<!--      v-model:page="queryParams.pageNo"-->
<!--      v-model:limit="queryParams.pageSize"-->
<!--      @pagination="getList"-->
<!--    />-->
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <OnboardingMapForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { OnboardingMapApi, OnboardingMapVO } from '@/api/learning/onboardingmap'
import {DICT_TYPE, getIntDictOptions} from "@/utils/dict";
import router from "@/router";

/** 学习地图 列表 */
defineOptions({ name: 'OnboardingMap' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<OnboardingMapVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  name: undefined,
  mode: undefined,
  effectiveVersion: undefined,
  meta: undefined,
  createTime: []
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await OnboardingMapApi.getOnboardingMapPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await OnboardingMapApi.deleteOnboardingMap(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await OnboardingMapApi.exportOnboardingMap(queryParams)
    download.excel(data, '学习地图.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
