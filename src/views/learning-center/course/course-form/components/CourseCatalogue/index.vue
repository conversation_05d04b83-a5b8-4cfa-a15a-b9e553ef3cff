<script setup lang="ts">
import type { ComponentInternalInstance } from 'vue'
import { ChapterTypeIcons as Icons } from '../../scripts/ChapterTypeIcons'
import ChoosePaper from '@/views/exam/exam-list/components/ChoosePaper.vue'
import TaskManagement from './CourseDialog/index.vue'
import ExamManagement from './ExamDialog/index.vue'
import { CourseChapterType, ExamChapterType, ScormFileStatus } from '@/enums/chapter'

import { delChaptere, listChapterData, sortChapter, unzipScorm } from '@/api/topicMgt/elearning'
import type { ChapterData } from '@/typings/views/topicMgt/elearning'

type ChapterType = CourseChapterType
const props = defineProps<{
  courseId: string
  basicInformation: { name: string, cover: string, topic: string, status: number }
}>()
const { t } = useI18n()
const message = useMessage() // 消息弹窗
const RefChoosePaper = ref()
const RefTaskManagement = ref()
const RefExamManagement = ref()
const show = ref(false)
const showExam = ref(false)
const showChoosePaper = ref(false)
const paperChoosed = ref()
const list = ref()
const filteredArray = ref()
const remainingArray = ref()
const loading = ref(true)
// const Icons = {
//   [CourseChapterType.Video]: 'ChapterVideo',
//   [CourseChapterType.Audio]: 'ChapterAudio',
//   [CourseChapterType.Document]: 'ChapterDoc',
//   [CourseChapterType.Scorm]: 'ChapterScorm',
//   [CourseChapterType.ZIP]: 'ChapterScorm',
//   [CourseChapterType.Exam]: 'ChapterExam',
// }
// aicc和scorm状态共用一个
const scormStatus: Record<any, { text: string, color: string }> = {
  [ScormFileStatus.Unziping]: {
    text: t('common.unzip'),
    color: '#D78710',
  },
  [ScormFileStatus.Failure]: {
    text: t('common.failure'),
    color: '#DE2525',
  },
  [ScormFileStatus.Success]: {
    text: t('common.success'),
    color: '#007943',
  },
  [ScormFileStatus.NotScorm]: {
    text: t('common.nonScorm'),
    color: '#234688',
  },
}
const selectedChapter = ref<ChapterData | undefined>()
const getList = async () => {
  loading.value = true
  try {
    const data = await listChapterData({ courseId: props.courseId })
    const array = ref()
    array.value = data
    filteredArray.value = array.value?.filter(item => item.type === 6)
    list.value = data
  } finally {
    loading.value = false
  }
}

const handleAddTask = () => {
  RefTaskManagement.value.show = true
  selectedChapter.value = undefined
}

const handleAddExam = () => {
  RefExamManagement.value.show = true
  selectedChapter.value = undefined
}
const handleEdit = (row: ChapterData) => {
  selectedChapter.value = row
  if (row.type === CourseChapterType.Exam) {
    RefExamManagement.value.show = true
  }
  else {
    RefTaskManagement.value.show = true
  }
}

const handleDelete = async (row: ChapterData) => {
  if (row.type === CourseChapterType.Exam) {
    await message.confirm(t('global.deleteExam'))
    await delChaptere(row.id)
    message.success(t('global.deleteSuccess'))
    await getList()
  }
  else {
    await message.confirm(`${t('confirm.deleteChapter')} ${row.title}?`)
    await delChaptere(row.id)
    message.success(t('global.deleteSuccess'))
    await getList()
  }
}
/**
 * 重新发起当前课程的scorm解析
 * @param row 章节信息
 */
const handleUnzip = async (row: ChapterData) => {
  await unzipScorm(row.id)
  message.success(t('common.delSuccess'))
  await getList()
}
// 上移
const handleMoveUp = (row: any, index: any) => {
  const chapters = list.value
  chapters[index] = chapters.splice(index - 1, 1, chapters[index])[0]
  list.value = [...chapters]
  updateChaptersSort()
}
// 下移
const handleMoveDown = (row: any, index: any) => {
  const chapters = list.value
  chapters[index] = chapters.splice(index + 1, 1, chapters[index])[0]
  list.value = [...chapters]
  updateChaptersSort()
}
// 更新章节排序
const updateChaptersSort = async () => {
  const sorts = []
  for (let index = 0; index < list.value.length; index++) {
    const item = list.value[index]
    const sort = { id: 0, sort: 0 }
    sort.id = item.id
    sort.sort = index + 1
    sorts.push(sort)
  }
  await sortChapter(sorts)
}
// 选择试卷确认
const handlePaperChoose = () => {
  RefChoosePaper.value.handleOpen(paperChoosed.value?.id)
}
// 选择试卷确认
const handlePaperChooseConfirm = (val) => {
  paperChoosed.value = val
  RefExamManagement.value.handlePaperChoosed(val)
}
// const timeInter = setInterval(() => {
//   getList()
// }, 3000)

onMounted(() => {
  getList()
})

onBeforeUnmount(() => {
  // clearInterval(timeInter)
})
</script>

<template>
  <ContentWrap>
    <div class="rounded-x border border-[#CDDBF1] py-5 px-7">
      <div class="flex items-start">
        <span class="text-[#222222] text-xl leading-[20px]"> {{ t('learningCenter.course.courseCatalogue') }} </span>
        <el-button class="ms-2.5 !text-xl" type="primary" link size="large" @click="getList">
          <Icon icon="ep:refresh" />
        </el-button>
      </div>

      <div class="mt-5 flex items-center">
        <div class="h-9 border-[2px] border-primary bg-[#E4F4EE] rounded-[4px] px-4 leading-9">
          <span class="text-primary font-bold text-xl">{{ list?.length }}</span>
          <span class="text-[#222222] text-sm ms-0.5">{{ t('learningCenter.task.totalTasks') }}</span>
        </div>
        <div class="ms-auto">
          <el-tooltip class="box-item" effect="dark" :content="t('common.updateFailure')" placement="top-end" :disabled="props.basicInformation?.status !== 1">
            <el-button type="primary" :disabled="props.basicInformation?.status === 1" @click="handleAddTask">
              {{ t('learningCenter.task.addTask') }}
            </el-button>
          </el-tooltip>
          <el-tooltip class="box-item" effect="dark" :content="t('common.updateFailure')" placement="top-end" :disabled="props.basicInformation?.status !== 1">
            <el-button type="primary" :disabled="props.basicInformation?.status === 1" @click="handleAddExam">
              {{ t('learningCenter.exam.addExam') }}
            </el-button>
          </el-tooltip>
        </div>
      </div>
      <!-- 下方资源列表 -->
      <div v-loading="loading" class="mt-5">
        <template v-for="item in list" :key="item.id">
          <div class="flex mt-4">
            <!-- 左侧类型图标 -->
            <div class="relative w-[40px] h-[40px]">
              <svg-icon icon-class="ChapterType" class="text-[40px]" />
              <svg-icon :icon-class="Icons[item.type as ChapterType]" class="text-base !absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2" />
            </div>
            <!-- 右侧内容 -->
            <div class="flex-1 flex items-center ms-4">
              <span v-if="props.basicInformation?.status === 1 || item.type === 6" class="text-sm text-[#233A35]">{{ item.title }}</span>
              <span v-else class="text-sm text-[#233A35] cursor-pointer hover:text-primary" @click="handleEdit(item)">{{ item.title }}</span>
              <!-- 如果是zip类型或者scorm课程需要特殊展示一个标识，通过type判断当前是scorm课程还是aicc课程 7.aicc -->
              <div v-if="[CourseChapterType.ZIP, CourseChapterType.Scorm, CourseChapterType.Aicc].includes(item.type)">
                <el-tag
                  effect="dark"
                  :color="item.type === 7 ? scormStatus[`${item.aiccParseStatus}`].color : item.type === 4 ? scormStatus[`${item.scormParseStatus}`].color : ''"
                  :style="{
                  '--el-tag-text-color': '#ffffff',
                  '--el-tag-border-color': item.type === 7 ? scormStatus[`${item.aiccParseStatus}`].color : item.type === 4 ? scormStatus[`${item.scormParseStatus}`].color : '',
                }"
                  class="ms-2.5"
                >
                  <!--                当是type为5的话就是一个普通的zip文件,显示这是一个不符合要求的文件-->
                  {{ item.type === 7 ? scormStatus[`${item.aiccParseStatus}`].text : item.type === 4 ? scormStatus[`${item.scormParseStatus}`].text : t('common.incompatible') }}
                </el-tag>
              </div>
              <!-- 课程状态以及图标 -->
              <div class="ms-auto flex items-center">
                <!-- 在课程为ZIP类型，并且状态为失败时，显示此按钮 -->
                <el-button v-if="[CourseChapterType.ZIP, CourseChapterType.Scorm].includes(item.type) && item.scormParseStatus === ScormFileStatus.Failure" type="primary" link @click="handleUnzip(item)">
                  <template #icon>
                    <svg-icon icon-class="Unzip" />
                  </template>
                  <span> {{ t('action.unzip') }} </span>
                </el-button>

                <el-tooltip class="box-item" effect="dark" :content="t('common.updateFailure')" placement="top-end" :disabled="props.basicInformation?.status !== 1">
                  <el-button link type="primary" :disabled="props.basicInformation?.status === 1" @click="handleEdit(item)">
                    <Icon icon="ep:edit" />
                    {{ t('action.edit') }}
                  </el-button>
                </el-tooltip>
                <el-button link type="primary" @click="handleDelete(item)">
                  <Icon icon="ep:delete" />
                  {{ t('action.delete') }}
                </el-button>
                <!-- type=考试，不显示 Move up/Move Down -->
                <el-button v-if="(![CourseChapterType.Exam].includes(item.type))" link type="primary" :disabled="list.indexOf(item) === 0" @click="handleMoveUp(item, list.indexOf(item))">
                  <el-icon><SortUp /></el-icon> {{ t('action.moveUp') }}
                </el-button>
                <el-button v-if="(![CourseChapterType.Exam].includes(item.type))" link type="primary" :disabled="list.indexOf(item) === list.length - filteredArray.length - 1" @click="handleMoveDown(item, list.indexOf(item))">
                  <el-icon><SortDown /></el-icon> {{ t('action.moveDown') }}
                </el-button>
              </div>
            </div>
          </div>
          <!-- type=Exam 显示Subject、通过分数等字段 -->
          <div v-if="[CourseChapterType.Exam].includes(item.type)" class="text-[13px]">
            <div class="flex text-[#ABACAE]  ml-14">
              <!-- Icons[item.type as CourseChapterType ] -->
              <!-- <span>Subject:{{ item.score ? item.score : '--' }}</span> -->
              <span>{{ t('common.type')}} {{ item.examType === ExamChapterType.CustomPaper ? t('learningCenter.exam.customizedPaper') : item.examType === ExamChapterType.AutoPaper ? t('learningCenter.exam.autoPaper') : '--' }}</span>
              <span class="ml-4 min-w-8">Score:{{ item.totalScore ? item.totalScore : '--' }}</span>
              <span class="ml-4 min-w-8">Duration:{{ item.answerTime ? item.answerTime : '--' }} mins</span>
              <span class="ml-4 min-w-8">Pass Score:{{ item.passScore ? item.passScore : '--' }}</span>
            </div>
          </div>
          <!-- 分割线 -->
          <div class="h-[1px] bg-[#DDDDDD]  ms-14"></div>
        </template>
        <el-empty v-if="!list?.length" />
      </div>
      <TaskManagement ref="RefTaskManagement" :course-id="props.courseId" :task-max-sort="list?.length + 1" :data-id="selectedChapter?.id" @refresh="getList" />
      <ExamManagement ref="RefExamManagement" :course-id="props.courseId" :task-max-sort="list?.length + 1" :content-exam-id="selectedChapter?.contentExamId" :data-id="selectedChapter?.id" @refresh="getList" @choose-paper-action="handlePaperChoose" />
      <ChoosePaper ref="RefChoosePaper" v-model="showChoosePaper" :title-name="t('action.chooseExamPaper')" :course-id="props.courseId" @paper-choose="handlePaperChooseConfirm" />
    </div>
  </ContentWrap>
</template>

<style scoped lang="scss"></style>
