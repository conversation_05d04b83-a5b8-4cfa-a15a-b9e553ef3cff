<script setup lang="ts" name="Elearning">
import type { ComponentInternalInstance } from 'vue'
import { delImportCourse, importingCourse, restartImportCourse} from '@/api/topicMgt/elearning'
import { formatImgUrl } from '@/utils'
import { useTagsViewStore } from "@/store/modules/tagsView"
import { dateFormatter } from '@/utils/formatTime'

interface DataItem {
  area: string
  assetUuid: string
  category: string
  channel: string
  contentId: string
  contentTitle: number
  courseFileName: number
  courseFormat: string
  courseSource: string
  creator: string
  description: string
  errorMessage: string
  estimatedDuration: string
  exam: string
  fileLocation: string
  id: number
  imageUrl: string
  importTime: string
  keywords: string
  language: string
  level: string
  pageNo: number
  pageSize: number
  status: number
  subject: string
  subtitle: string
  taskId: string
}
const { delView } = useTagsViewStore() // 视图操作
const { router, push, currentRoute } = useRouter() // 路由
const { t } = useI18n()
const message = useMessage() // 消息弹窗
const loading = ref(false)
const tableData = ref<Array<DataItem>>([])
const total = ref(0)
const queryParams = ref({
  pageNo: 1,
  pageSize: 10,
})
// 重新导入课程
const handleRestart = async (item: DataItem) => {
  try {
    await message.confirm(t('confirm.reImportCourse'))
    loading.value = true
    // 根据产品需求,前端控制改变一下状态,两秒后再调用接口
    item.status = 1
    setTimeout(async () => {
      await restartImportCourse(item.id)
      await getList()
    }, 2000)
  } finally {
    loading.value = false
  }
}
// 删除正在导入的课程
const handleDelete = async (id: number) => {
  try {
    await message.confirm(t('confirm.deImportCourse'))
    loading.value = true
    await delImportCourse(id)
    await getList()
    message.success(t('global.deleteSuccess'))
  } finally {
    loading.value = false
  }
}
/*
fileName: txt文件名称
content：文件内容(string)
*/
const downloadTxt = (content: string) => {
  let a = document.createElement('a');
  a.href = 'data:text/plain;charset=utf-8,' + content
  a.download = 'Error content'
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
}

const handleToList = () => {
  delView(unref(currentRoute))
  push('/learning-center/course')
}
const getList = async () => {
  loading.value = true
  try {
    const res = await importingCourse(queryParams.value)
    tableData.value = res.list
    total.value = res.total
  } finally {
    loading.value = false
  }
}
onMounted(() => {
  getList()
})
// 轮询获取数据
// const timeInter = setInterval(() => {
//   getList()
// }, 5000)
onBeforeUnmount(() => {
  // clearInterval(timeInter)
})
</script>

<template>
  <div class="app-container">
    <!-- 顶部返回 -->
    <div class="w-full h-[140px] bg-[#E4F4EE] flex items-center rounded-[10px] px-4">
      <span class="text-2xl"> {{ t('action.importingList') }} </span>
      <div class="ms-auto flex-shrink-0">
        <div>
          <el-button type="primary" class="w-[80px]" @click="handleToList">
            {{ t('action.back') }}
          </el-button>
        </div>
      </div>
    </div>
    <el-table v-loading="loading" :data="tableData" border height="600">
      <el-table-column prop="taskId" width="75" :label="t('learningCenter.course.taskId')" show-overflow-tooltip />
      <el-table-column prop="contentTitle" width="120" :label="t('learningCenter.course.contentTitle')" show-overflow-tooltip />
      <el-table-column prop="contentId" width="95" :label="t('learningCenter.course.contentId')" show-overflow-tooltip align="center" />
      <el-table-column prop="assetUuid" width="120" :label="t('learningCenter.course.assetUUID')" show-overflow-tooltip align="center" />
      <el-table-column prop="category" width="120" :label="t('learningCenter.course.categoryL1')" show-overflow-tooltip align="center" />
      <el-table-column prop="area" width="95" :label="t('learningCenter.course.areaL2')" show-overflow-tooltip align="center" />
      <el-table-column prop="subject" width="95" :label="t('learningCenter.course.subjectL3')" show-overflow-tooltip align="center" />
      <el-table-column prop="channel" width="120" :label="t('learningCenter.course.channelL4')" show-overflow-tooltip align="center" />
      <el-table-column :label="t('learningCenter.course.language')" align="center" width="120" show-overflow-tooltip>
        <template #default="{ row }">
          <el-tag type="success" v-if="row.language">{{ row.language }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column key="estimatedDuration" :label="t('learningCenter.course.estimatedDuration')" show-overflow-tooltip align="center" prop="estimatedDuration" width="150" />
      <el-table-column prop="level" width="95" :label="t('learningCenter.course.level')" show-overflow-tooltip align="center" />
      <el-table-column prop="exam" width="120" :label="t('learningCenter.course.isExam')" show-overflow-tooltip align="center" />
      <el-table-column prop="subtitle" width="120" :label="t('learningCenter.course.isSubtitle')" show-overflow-tooltip align="center" />
      <el-table-column prop="courseSource" width="120" :label="t('learningCenter.course.isLocal')" show-overflow-tooltip align="center" />
      <el-table-column prop="imageUrl" width="95" :label="t('learningCenter.course.imageUrl')" show-overflow-tooltip align="center">
        <template #default="{ row }">
          <div>
            <div class="flex flex-row items-center justify-center">
              <el-image :src="formatImgUrl(row.imageUrl)" class="w-[86px] h-[50px]" />
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="keywords" width="95" :label="t('learningCenter.course.keywords')" show-overflow-tooltip align="center">
        <template #default="{ row }">
          <el-tooltip
              :content="row.keywords" popper-class="tooltip-mywidth"
              effect="dark" placement="top"
          >
            <div class="line-clamp-3">
              {{ row.keywords }}
            </div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column prop="description" width="120" :label="t('learningCenter.boarding.description')" show-overflow-tooltip align="center" />
      <el-table-column prop="courseFileName" width="150" :label="t('learningCenter.course.courseFileName')" show-overflow-tooltip align="center" />
      <el-table-column prop="fileLocation" width="120" :label="t('learningCenter.course.fileLocation')" show-overflow-tooltip align="center" />
      <el-table-column prop="status" width="120" :label="t('common.status')" show-overflow-tooltip  align="center">
        <template #default="{ row }">
          <el-tag v-if="row.status === 0" type="info">{{ t('learningCenter.course.notStarted') }}</el-tag>
          <el-tag v-else-if="row.status === 1" type="info">{{ t('learningCenter.course.inProcess') }}</el-tag>
          <el-tag v-else-if="row.status === 2" type="success">{{ t('learningCenter.course.successfully') }}</el-tag>
          <el-tag v-else-if="row.status === 3" type="danger">{{ t('learningCenter.course.fail') }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="creator" width="95" :label="t('category.journey.creator')" show-overflow-tooltip align="center" />
      <el-table-column prop="importTime" width="180" :label="t('learningCenter.course.importTime')" :formatter="dateFormatter" show-overflow-tooltip align="center" />
      <el-table-column fixed="right" :label="t('global.action')" min-width="180">
        <template #default="{ row }">
<!--          "状态(0.未开始 1.正在处理 2.成功 3.失败)")-->
<!--          仅当Status为Failed时，Restart按钮可用-->
          <el-button :disabled="row.status !==3" link type="primary" @click="handleRestart(row)">
            {{ t('action.restart') }}
          </el-button>
          <el-button :disabled="!row.errorMessage" link type="primary" @click="downloadTxt(row.errorMessage)">
            {{ t('action.report') }}
          </el-button>
<!--          仅当Status为Failed时，该按钮可用。-->
          <el-button :disabled="row.status !==3" link type="primary" class="ms-0" @click="handleDelete(row.id)">
            {{ t('action.delete') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize" :total="total" @pagination="getList" />
  </div>
</template>

<style scoped lang="scss">
/** table中superior company的样式 */
:deep .custom-wrapper-disabled .el-input__wrapper{
  border: none;
  box-shadow: none;
  color: #007943;
  @apply cursor-text
}
:deep .custom-wrapper-disabled .el-input__inner {
    color: #fff;
}
:deep .custom-wrapper-disabled .el-input__inner:hover{
  border: none;
  color: #fff;
  cursor: text;
}
:deep .custom-wrapper-disabled .el-input.is-disabled .el-input__inner{
  -webkit-text-fill-color: #000000b3;
}
:deep .el-input__suffix {
    display: none;
}
:deep .custom-wrapper-disabled  .el-input.is-disabled .el-input__wrapper {
    background-color: #fff;
}
:deep .el-table--border .el-table__row:hover {
  background-color:#f5f7fa;
    .custom-wrapper-disabled .el-input__wrapper {
      background-color:#f5f7fa;
  }
}
:deep .el-tag + .el-tag{
  margin-left: 0;
  margin-top:2px
  }
</style>

<style  lang="scss">
// 设置tooltip-mywidth时，style不能添加scope
.tooltip-mywidth{
  width: 240px;
}
</style>
