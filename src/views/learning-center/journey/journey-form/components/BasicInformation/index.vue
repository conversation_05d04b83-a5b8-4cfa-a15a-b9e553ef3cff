<script setup lang="ts" name="Basic Information">
import type { FormInstance, FormRules } from 'element-plus'
import type { ComponentInternalInstance } from 'vue'
import { listTopic, listTopicAll } from '@/api/category/topic'
import { deepClone } from '@/utils'
import {getJourney, add<PERSON>ourney, updateJourney, JourneyStatusEnum} from "@/api/topicMgt/journey"
import { getJourneyCategory, getJourneyCategoryAll } from "@/api/category/journey"
import CustomDialog from '@/components/CustomDialog/index.vue'
import { listCourse } from "@/api/topicMgt/elearning"
import { formatImgUrl } from '@/utils'
import SearchCascader from '@/components/SearchCascader/index.vue'
import { useTagsViewStore } from "@/store/modules/tagsView"
const props = defineProps<{
  journeyId: string
}>()
interface FormData {
  id?: string
  title: string
  cover: string
  courseIds: string | number[] | undefined
  categoryId: number
  keywords: string | []
  introduction: string
  status: number
}
interface DataItem {
  createBy: string
  createTime: Date
  updateBy?: string | null
  updateTime?: string | null
  remark?: string | null
  pageNo: number
  pageSize: number
  id: number
  name: string
  keywords?: string | null
  introduction: string
  departmentId: string
  isChecked?: boolean
}
const { delView } = useTagsViewStore() // 视图操作
const { t } = useI18n()
const message = useMessage() // 消息弹窗
const loading = ref(false)
const { router, push, currentRoute } = useRouter() // 路由
const courseLoading = ref(false)
const okLoading = ref(false)
const formRef = ref<FormInstance>()
/** initStatus 初始化状态值，与Subject切换值时配合使用 */
const initStatus = ref(false)
const isFull = ref(false)
const isMini = ref(false)
const inputValue = ref('')
const inputVisible = ref()
const queryRef = ref()
const InputRef = ref()
const repeatKeyword = ref(false)
const journeyCategoryList = ref([])
const showCourse = ref(false)
const activeName = ref('first')
const courseItem = ref<Array<DataItem>>([])  // 弹框内存储课程信息的值
const total = ref(0)
const checkCourseList = ref([]) // 存储选取课程信息(弹框)
// form中课程信息列表
const courseList = ref([])
// 保存使用
const formData = ref<FormData>({
  id: undefined,
  title: '',
  cover: '',
  courseIds: [],
  categoryId: undefined,
  keywords: [],
  introduction: '',
  status: JourneyStatusEnum.MOVE_OFF_SHELF,
})
const topicId = ref()
// 弹框查询课程信息使用
const queryParams = ref({
  name: '',
  categoryId: undefined,
  topicId: undefined,
  keywords: '',
  // 默认选择上架的课程
  status: 1
})
// 必填校验
const formRules = ref<FormRules<FormData>>({
  categoryId: [
    {
      required: true,
      message: t('common.selectText'),
      trigger: 'change',
    },
  ],
  title: [
    {
      required: true,
      message: t('learningCenter.journey.journeyTitleRule'),
      trigger: 'change',
    },
  ],
  cover: [
    {
      required: true,
      message: t('common.uploadText'),
      trigger: 'change',
    },
  ],
  courseIds: [
    {
      required: true,
      message: t('learningCenter.course.selectCourseRule'),
      trigger: 'change',
    },
  ]
})
const showInput = () => {
  inputVisible.value = true
  nextTick(() => {
    InputRef.value!.input!.focus()
  })
}
const handleInputConfirm = () => {
  if (formData.value.keywords.includes(inputValue.value)) {
    repeatKeyword.value = true
    return
  }
  repeatKeyword.value = false
  if (inputValue.value) {
    formData.value.keywords.push(inputValue.value)
  }
  inputVisible.value = false
  inputValue.value = ''
}

const handleCloseTag = (tag: string) => {
  formData.value.keywords.splice(formData.value.keywords.indexOf(tag), 1)
  if (formData.value.keywords.includes(inputValue.value)) {
    repeatKeyword.value = true
  }
  else {
    repeatKeyword.value = false
  }
}
const handleAddTag = (tag: string) => {
  inputValue.value = tag
  if (formData.value.keywords.length >= 5) {
    return
  }
  handleInputConfirm()
}

const getDetail = async () => {
  const data = await getJourney(props.journeyId)
  if (data) {
    const resData = data as FormData
    // 特殊处理keywords参数
    const keywords = resData.keywords ? (resData.keywords as string).split(',') : undefined
    formData.value.keywords = keywords
    initStatus.value = true
    formData.value.cover = resData.cover
    formData.value.title = resData.title
    formData.value.introduction = resData.introduction
    formData.value.id = resData.id
    formData.value.categoryId = resData.categoryId
    if (resData.courseList) {
      courseList.value = resData.courseList
      // 保存使用
      formData.value.courseIds = courseList.value.map((item) => item.id).join(',')
    }
    /**
     * 解决：
     * 1、添加keywords时，报push is not function的问题 keywords不能为空；
     * 2、编辑返回的keywords没有值，页面上不显示Key Words的问题。
     */
    if (!formData.value.keywords) {
      formData.value.keywords = []
    }
  }
}
// 保存学习地图
const handleConfirm = async () => {
  await formRef.value.validate()
  loading.value = true
  try {
    // 特殊处理keywords参数
    const keywords = formData.value.keywords ? (formData.value.keywords as string[]).join(',') : undefined
    // keywords有重复的，提交时，去掉重复值
    if (repeatKeyword.value) {
      inputVisible.value = false
      repeatKeyword.value = false
      inputValue.value = ''
    }
    const dataParams = {
      ...formData.value,
      keywords,
    }
    if (props.journeyId) {
      await updateJourney(dataParams)
      message.success(t('global.editSuccess'))
      await getDetail()
    }
    else {
      const data = await addJourney(dataParams)
      message.success(t('global.addSuccess'))
      delView(unref(currentRoute))
      push(`/journey-form/index/${data}?active=2`)
    }
  } finally {
    loading.value = false
  }
}

const handleClose = () => {
  delView(unref(currentRoute))
  push('/learning-center/journey')
}

// 学习地图分类全量
const getListCategory = async () => {
  journeyCategoryList.value = await getJourneyCategoryAll()
}
const handleSelectCourse = () => {
  showCourse.value = true
  reset()
  getCourseList()
  // 打开弹框时先清空一遍选择的旧值信息
  checkCourseList.value = courseList.value.map((item: DataItem) => {
    return {
      ...item
    }
  })
}
// 关闭弹框
const handleCancel = () => {
  showCourse.value = false
  isFull.value = false
  isMini.value = false
}
/** 表单弹框课程重置 */
const reset = () => {
  queryParams.value = {
    pageNo: 1,
    pageSize: 10,
    name: undefined,
    categoryId: undefined,
    keywords: '',
    status: 1,
    topicId: undefined,
  }
  topicId.value = undefined
  queryRef.value?.resetFields()
}

// 弹框选择课程信息
const getCourseList = async () => {
  courseLoading.value = true
  try {
    if (topicId.value) {
      queryParams.value.topicId = topicId.value.join(',')
    }
    const data = await listCourse(queryParams.value)
    courseItem.value = data.list = data.list.map((item: DataItem) => {
      return {
        ...item,
        isChecked: false
      }
    })
    // 每次请求数据的时候要判断请求回来的数据是否在boxList中,如果在则高亮展示
    courseItem.value.forEach((item: DataItem) => {
      if (checkCourseList.value.some((archive: DataItem) => archive.id === item.id)) {
        item.isChecked = true
      }
    })
    total.value = data.total
  } finally {
    courseLoading.value = false
  }
}
const handleSearch = () => {
  getCourseList()
}
const handleReset = () => {
  reset()
  getCourseList()
}

// 高亮
const hightLightRow = (data: { row: any; rowIndex: number }) => {
  if (data.row.isChecked) {
    return 'highlight-row'
  } else {
    return ''
  }
}
// 单独选择课程信息
const handleRowClick = (row: DataItem) => {
  row.isChecked = !row.isChecked
  if (row.isChecked) {
    // 如果选中则添加到选中数组中
    checkCourseList.value.push(row)
  } else {
    // 如果取消选中则从选中数组中移除
    deleCheckedArchive(row)
  }
}
// 删除选择的课程信息
const deleCheckedArchive = (course: DataItem) => {
  const index = checkCourseList.value.findIndex(
      (item: DataItem) => item.id == course.id
  )
  if (index != -1) {
    checkCourseList.value.splice(index, 1)
    // 列表中也需要删除
    courseList.value.splice(index, 1)
  }
}
//  确定所有选择的课程信息,然后去重
const determineCourse = () => {
  okLoading.value = true
  try {
    // 判断如果弹框选择了值并且列表展示也有值则可以直接点击ok，反之，不允许点击ok
    if (checkCourseList.value.length === 0 && courseList.value.length === 0) {
      message.alertWarning(t('global.pleaseSelectCourse'))
      okLoading.value = false
      return
    }
    courseList.value = checkCourseList.value.concat(courseList.value).map((item: DataItem) => ({
      ...item
    }))
    // 使用集合来跟踪已经出现的 id
    const uniqueIds = new Set()
    const uniqueArray = courseList.value.filter((obj: DataItem) => {
      // 如果当前对象的 id 还没有出现，将其添加到集合中，并保留该对象
      if (!uniqueIds.has(obj.id)) {
        uniqueIds.add(obj.id)
        return true
      }
      return false
    })
    // 将去重后的数组赋给新的变量,当前赋值给的courseItem变量选择完列表所展示的所有信息，然后还要单独取id、unit使用在保存的时候传值
    courseList.value = uniqueArray
    // 组装传参key:value
    const resultArray = courseList.value.map((item: DataItem) => item.id).join(',')
    // 保存使用
    formData.value.courseIds = resultArray
    showCourse.value = false
  } finally {
    okLoading.value = false
  }
}
// 删除已选中的档案信息
const deleteCourse = (index: number, item: DataItem) => {
  courseList.value.splice(index, 1)
  // 保存使用
  formData.value.courseIds = courseList.value.map((item: DataItem) => item.id).join(',')
}

// 上移/下移课程
function moveElement(arr: any[], fromIndex: number, toIndex: number): any[] {
  const [element] = courseList.value.splice(fromIndex, 1);

  courseList.value.splice(toIndex, 0, element);
  formData.value.courseIds = courseList.value.map((item) => item.id).join(',');
  return courseList.value;
}

// 删除tag标签
const deleteClose = (tag: DataItem) => {
  const index = courseItem.value.findIndex((obj: DataItem) => obj.id === tag.id)
  if (index != -1) {
    courseItem.value[index].isChecked = !courseItem.value[index].isChecked
  }
  deleCheckedArchive(tag)
}

onMounted(() => {
  getListCategory()
  if (props.journeyId) {
    getDetail()
  }
})
</script>

<template>
  <ContentWrap>
    <div class="rounded-x py-5 px-7">
      <span class="text-[#222222] text-xl"> {{ t('learningCenter.course.basicInfo') }} </span>
      <!-- 表单区域 -->
      <el-form ref="formRef" :model="formData" label-width="auto" label-position="left" class="mt-5 w-3/4" :rules="formRules">
        <el-form-item :label="t('learningCenter.journey.title')" prop="title">
          <el-input v-model="formData.title" show-word-limit maxlength="200" />
        </el-form-item>
        <el-form-item prop="cover" :label="t('learningCenter.journey.cover')">
          <ImageUpload v-model="formData.cover" :limit="1" :is-show-tip="true" :file-size="5" />
        </el-form-item>
        <el-form-item prop="categoryId" :label="t('learningCenter.journey.categoryTitle')">
          <el-select v-model="formData.categoryId" clearable>
            <el-option v-for="item in journeyCategoryList" :key="item.id" :label="item.title" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item prop="courseIds" :label="t('common.choose')">
          <div class="h-[100px] flex flex-col justify-center gap-2.5 items-center basis-72 border border-dashed border-[#D6DEE3] cursor-pointer active:border-primary duration-300 transition-all mb-8" @click="handleSelectCourse">
            <svg-icon icon-class="SelectResource" class="text-[22px]" />
            <span class="text-sm text-primary">{{ t('learningCenter.course.chooseCourse') }}</span>
          </div>
        </el-form-item>
        <el-form-item v-show="courseList.length > 0">
          <el-table :data="courseList" style="width: 100%" height="300" :show-overflow-tooltip="true" :empty-text="t('common.noData')" :destroy-on-close="true">
            <el-table-column :label="t('learningCenter.course.course')" prop="name" min-width="300">
              <template #default="{ row }">
                <div>
                  <div class="flex flex-row">
                    <el-image :src="formatImgUrl(row.cover)" class="w-[86px] h-[50px]" />
                    <div class="pl-5 w-[230px]">
                      <el-tooltip
                        :content="row.name" popper-class="tooltip-mywidth"
                        effect="dark" placement="top"
                      >
                        <div class="break-all line-clamp-3">
                          {{ row.name }}
                        </div>
                      </el-tooltip>
                    </div>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column fixed="right" :label="t('global.action')" min-width="150">
              <template #default="scope">
                <el-button link type="primary" @click="deleteCourse(scope.$index,scope.row)">
                  <Icon icon="ep:delete" />
                  {{ t('action.delete') }}
                </el-button>
                <el-button :disabled="scope.$index === 0" link type="primary" @click="moveElement(scope.row,scope.$index,scope.$index-1)">
                  <Icon icon="ep:upload" class="mr-5px"/>
                  {{ t('action.moveUp') }}
                </el-button>
                <el-button :disabled="scope.$index === courseList.length -1" link type="primary" class="ms-0" @click="moveElement(scope.row,scope.$index,scope.$index+1)">
                  <Icon class="mr-5px" icon="ep:download" />
                  {{ t('action.moveDown') }}
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-form-item>
        <el-form-item prop="keywords" :label="t('category.topic.keyWords')">
          <div>
            <div class="flex flex-wrap gap-2 w-full">
              <el-tag v-for="tag in formData.keywords" :key="tag" closable :disable-transitions="false" @close="handleCloseTag(tag)">
                {{ tag }}
              </el-tag>
              <el-input v-if="inputVisible" ref="InputRef" v-model="inputValue" class="!w-60" size="small" maxlength="300" show-word-limit @keyup.enter="handleInputConfirm" @blur="handleInputConfirm" />
              <el-button v-else-if="!inputVisible && (formData.keywords && formData.keywords.length < 5)" class="button-new-tag" size="small" @click="showInput">
                {{ t('action.addKeyWord') }}
              </el-button>
            </div>
            <div v-if="repeatKeyword" class="text-[#f56c6c] text-xs my-2">
              {{ t('common.keyWords') }}
            </div>
            <div class="text-[#ABACAE] text-xs my-2">
              {{ t('common.keyWordsLength') }}
            </div>
          </div>
        </el-form-item>
        <el-form-item prop="introduction" :label="t('learningCenter.boarding.description')">
          <el-input v-model="formData.introduction" type="textarea" :rows="5" show-word-limit maxlength="5000" />
        </el-form-item>
        <el-form-item label=" ">
          <el-button type="primary" :loading="loading" @click="handleConfirm">
            {{ t('global.confirm') }}
          </el-button>
          <el-button type="primary" plain @click="handleClose">
            {{ t('global.cancel') }}
          </el-button>
        </el-form-item>
      </el-form>
    </div>

  </ContentWrap>

<!--  弹框选择课程信息-->
  <Dialog v-model="showCourse" :title="t('learningCenter.course.chooseCourse')" @close="handleCancel">
    <div class="h-full w-full pb-5">
      <div class="min-h-80px h-[80px] overflow-auto ps-2 pe-2 bg-[#F9FAFB] rounded-md">
        <div>
          {{ t('learningCenter.course.chooseCourse') }}
        </div>
        <div>
          <el-tag
              class="tag mt-1 me-2"
              v-for="(tag,tagIndex) in checkCourseList"
              v-show="checkCourseList"
              :key="tag.id"
              closable
              type="primary"
              :disable-transitions="false"
              @close="deleteClose(tag)"
          >
            {{ tagIndex+1}}.{{ tag.name }}
          </el-tag>
        </div>
      </div>
    </div>
    <div class="h-full w-full"><el-tabs
        v-model="activeName" class="custom-resource-tabs"
    >
      <el-tab-pane :label="t('learningCenter.course.courses')" name="first">
        <template #label>
          <span>{{ t('learningCenter.course.courses') }}</span>
        </template>
        <div style="overflow: auto">
          <el-scrollbar relative h-full custom-resource-scrollbar>
            <ContentWrap>
              <el-form ref="queryRef" :inline="true" :model="queryParams" @submit.prevent>
                <el-form-item :label="t('learningCenter.course.title')">
                  <el-input v-model="queryParams.name" :placeholder="t('common.inputText')" clearable style="width: 240px" @keydown.enter="handleSearch" />
                </el-form-item>
                <el-form-item :label="t('category.topic.subjectName')" prop="topic">
                  <SearchCascader v-model="topicId" :has-no-subject="false" style="width: 260px" />
                </el-form-item>
                <el-form-item :label="t('category.topic.keyWords')">
                  <el-input v-model="queryParams.keywords" :placeholder="t('common.inputText')" clearable style="width: 240px" @keydown.enter="handleSearch" />
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="handleSearch">
                    <Icon class="mr-5px" icon="ep:search" />
                    {{ t('action.search') }}
                  </el-button>
                  <el-button type="default" @click="handleReset">
                    <Icon class="mr-5px" icon="ep:refresh" />
                    {{ t('action.reset') }}
                  </el-button>
                </el-form-item>
              </el-form>
            </ContentWrap>
            <ContentWrap>
              <el-table
                v-loading="courseLoading"
                :data="courseItem"
                @selection-change="handleSelectionChange"
                :show-overflow-tooltip="true"
                :row-class-name="hightLightRow"
                @row-click="handleRowClick">
                <el-table-column align="center" width="100px" fixed="left">
                  <template #default="scope">
                    <el-checkbox v-model="scope.row.isChecked" style="pointer-events: none" />
                  </template>
                </el-table-column>
                <el-table-column prop="id" width="95" :label="t('learningCenter.journey.uniqueId')" fixed="left" />
                <el-table-column prop="name" :label="t('learningCenter.journey.title')" :min-width="278" fixed="left">
                  <template #default="{ row }">
                    <div>
                      <div class="flex flex-row items-center justify-center">
                        <el-image :src="formatImgUrl(row.cover)" class="w-[96px] h-[50px]" />
                        <div class="pl-5 w-[160px]">
                          <el-tooltip
                            :content="row.name" popper-class="tooltip-mywidth"
                            effect="dark" placement="top"
                          >
                            <div class="break-all line-clamp-3">
                              {{ row.name }}
                            </div>
                          </el-tooltip>
                        </div>
                      </div>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column prop="topic" :label="t('category.topic.subjectName')" min-width="185" />
                <el-table-column prop="keywords" width="225" :label="t('category.topic.keyWords')" />
              </el-table>
              <pagination
                v-show="total > 0"
                v-model:page="queryParams.pageNo"
                v-model:limit="queryParams.pageSize"
                :total="total"
                @pagination="getCourseList"
              />
            </ContentWrap>
          </el-scrollbar>
        </div>
      </el-tab-pane>
    </el-tabs>
    </div>
    <template #footer>
      <el-button type="primary" @click="determineCourse">
        {{ t('global.confirm') }}
      </el-button>
      <el-button @click="showCourse = false">
        {{ t('global.cancel') }}
      </el-button>
    </template>
  </Dialog>

</template>

<style scoped lang="scss">
:deep .custom-tagdiv{
  border: 1px solid #c8c9cc;
  color: #909399;
  line-height: 1.9;
  border-radius: 4px;
  padding: 5px 5px;
  cursor: pointer;
  font-size: 12px !important;
  justify-content: center;
  align-items: center;
  vertical-align: middle;
  height: 24px;
  padding: 0 9px;
}
:deep .custom-tagdiv-active{
  border: 1px solid #007943;
  color: #007943;
  line-height: 1.9;
  border-radius: 4px;
  padding: 5px 5px;
  cursor: pointer;
  font-size: 12px !important;
  justify-content: center;
  align-items: center;
  vertical-align: middle;
  height: 24px;
  padding: 0 9px;
}
:deep .custom-tagdiv:hover{
  border: 1px solid #007943;
  color: #007943;
}

:deep(.el-input-number) {
  width: 50px;
}
:deep(.el-input-number.is-without-controls .el-input__wrapper) {
  padding-left: 11px;
  padding-right: 11px;
}
:deep(.custom-resource-scrollbar){
  .el-scrollbar__bar{
    @apply z-[9999];
  }
}
:deep(.custom-resource-tabs) {
  @apply h-full flex flex-col;
  .el-tab-pane{
    @apply flex flex-1 overflow-x-hidden;
  }
  .el-tabs__content{
    @apply flex flex-1;
  }
  .el-tabs__item {
    @apply p-0 #{!important};
    &>span{
      @apply mx-4;
    }
    &.is-active{
      @apply bg-[#E4F4EE];
    }
  }

}
:deep(.el-table) {
  .highlight-row {
    background-color: #ecf5ff; /* 示例颜色，可以根据需要修改 */
  }
}
</style>
