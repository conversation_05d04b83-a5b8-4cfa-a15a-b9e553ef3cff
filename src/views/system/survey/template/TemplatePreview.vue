<template>
  <div v-loading="loading" class="template-preview">
    <div v-if="templateData" class="preview-content">
      <!-- Template Header -->
      <div class="template-header">
        <h2 class="template-title">{{ templateData.name }}</h2>
        <div class="template-meta">
          <el-tag :type="getStatusType(templateData.status)">
            {{ getStatusText(templateData.status) }}
          </el-tag>
          <span class="template-code">{{ t('survey.templateCode') }}: {{ templateData.templateCode }}</span>
          <span class="template-category">{{ t('survey.category') }}: {{ templateData.categoryName }}</span>
        </div>
        <div v-if="templateData.description" class="template-description">
          {{ templateData.description }}
        </div>
      </div>

      <!-- Questions Preview -->
      <div class="questions-preview">
        <div class="questions-header">
          <h3>{{ t('survey.question') }} ({{ templateData.questions?.length || 0 }})</h3>
        </div>

        <div v-if="templateData.questions && templateData.questions.length > 0" class="questions-list">
          <div
            v-for="(question, index) in templateData.questions"
            :key="question.id"
            class="question-preview"
          >
            <div class="question-header">
              <span class="question-number">{{ index + 1 }}.</span>
              <span class="question-title">{{ question.title }}</span>
              <div class="question-tags">
                <el-tag size="small" :type="SurveyUtils.getQuestionTypeColor(question.questionType)">
                  {{ SurveyUtils.getQuestionTypeName(question.questionType) }}
                </el-tag>
                <el-tag v-if="question.required" size="small" type="danger">{{ t('survey.required') }}</el-tag>
              </div>
            </div>

            <div v-if="question.description" class="question-description">
              {{ question.description }}
            </div>

            <!-- Question Content Preview -->
            <div class="question-content">
              <!-- Single Choice -->
              <div v-if="question.questionType === 1" class="question-preview-content">
                <el-radio-group disabled>
                  <el-radio
                    v-for="(option, optionIndex) in getQuestionOptions(question)"
                    :key="optionIndex"
                    :value="option.value"
                    class="option-item"
                  >
                    {{ option.text }}
                    <span class="option-score">({{ option.score || 0 }}{{ t('survey.points') }})</span>
                  </el-radio>
                </el-radio-group>
              </div>

              <!-- Multiple Choice -->
              <div v-else-if="question.questionType === 2" class="question-preview-content">
                <el-checkbox-group disabled>
                  <el-checkbox
                    v-for="(option, optionIndex) in getQuestionOptions(question)"
                    :key="optionIndex"
                    :value="option.value"
                    class="option-item"
                  >
                    {{ option.text }}
                    <span class="option-score">({{ option.score || 0 }}{{ t('survey.points') }})</span>
                  </el-checkbox>
                </el-checkbox-group>
                <div v-if="getSelectionLimits(question)" class="selection-limits">
                  <small>{{ getSelectionLimits(question) }}</small>
                </div>
              </div>

              <!-- True/False -->
              <div v-else-if="question.questionType === 3" class="question-preview-content">
                <el-radio-group disabled>
                  <el-radio :value="true" class="option-item">
                    {{ getTrueFalseText(question, true) }}
                    <span class="option-score">({{ getTrueFalseScore(question, true) || 0 }}{{ t('survey.points') }})</span>
                  </el-radio>
                  <el-radio :value="false" class="option-item">
                    {{ getTrueFalseText(question, false) }}
                    <span class="option-score">({{ getTrueFalseScore(question, false) || 0 }}{{ t('survey.points') }})</span>
                  </el-radio>
                </el-radio-group>
              </div>

              <!-- Rating -->
              <div v-else-if="question.questionType === 4" class="question-preview-content">
                <el-rate
                  disabled
                  :value="0"
                  :max="getRatingMax(question)"
                  show-score
                  :score-template="`{value} / ${getRatingMax(question)}`"
                />
<!--                <div v-if="getRatingLabels(question)" class="rating-labels">-->
<!--                  <span class="min-label">{{ getRatingLabels(question).min }}</span>-->
<!--                  <span class="max-label">{{ getRatingLabels(question).max }}</span>-->
<!--                </div>-->
<!--                <div v-if="getRatingScoreInfo(question)" class="score-info">-->
<!--                  <small>{{ getRatingScoreInfo(question) }}</small>-->
<!--                </div>-->
              </div>

              <!-- File Upload -->
              <div v-else-if="question.questionType === 5" class="question-preview-content">
                <el-upload disabled list-type="text" :limit="getFileUploadLimit(question)">
                  <el-button disabled>
                    <Icon icon="ep:upload" class="mr-2" />
                    {{ t('survey.selectFile') }}
                  </el-button>
                  <template #tip>
                    <div class="el-upload__tip">
                      {{ getFileUploadTip(question) }}
                    </div>
                  </template>
                </el-upload>
              </div>

              <!-- Text Input -->
              <div v-else-if="question.questionType === 6" class="question-preview-content">
                <el-input
                  disabled
                  :placeholder="getTextPlaceholder(question)"
                  :maxlength="getTextMaxLength(question)"
                  show-word-limit
                />
                <div v-if="getTextLengthInfo(question)" class="length-info">
                  <small>{{ getTextLengthInfo(question) }}</small>
                </div>
              </div>

              <!-- Textarea -->
              <div v-else-if="question.questionType === 7" class="question-preview-content">
                <el-input
                  type="textarea"
                  disabled
                  :rows="getTextRows(question)"
                  :placeholder="getTextPlaceholder(question)"
                  :maxlength="getTextMaxLength(question)"
                  show-word-limit
                />
                <div v-if="getTextLengthInfo(question)" class="length-info">
                  <small>{{ getTextLengthInfo(question) }}</small>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div v-else class="empty-questions">
          <el-empty :description="t('survey.noData')" :image-size="80" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, withDefaults, defineProps } from 'vue'
import { useI18n } from '@/hooks/web/useI18n'
import { useRouter } from 'vue-router'
import { Icon } from '@/components/Icon'
import { TemplateApi, QuestionApi, SurveyUtils } from '@/api/system/survey'
import type { SurveyTemplate, SurveyQuestion } from '@/api/system/survey/types'

defineOptions({ name: 'TemplatePreview' })

const { t } = useI18n()
const router = useRouter()

const loading = ref(false)
const templateData = ref<SurveyTemplate | null>(null)

// Props
interface Props {
  templateId?: number
  template?: SurveyTemplate
}

const props = withDefaults(defineProps<Props>(), {
  templateId: undefined,
  template: undefined
})

/** Load template data */
const loadTemplate = async (templateId: number) => {
  loading.value = true

  try {
    // 加载模板基本信息
    const data = await TemplateApi.get(templateId)

    // 加载模板问题列表
    const questions = await QuestionApi.getByTemplate(templateId)

    // 将问题数据添加到模板对象中
    templateData.value = {
      ...data,
      questions: questions || []
    }
  } catch (error) {
    console.error('Failed to load template:', error)
  } finally {
    loading.value = false
  }
}

/** Set template data directly */
const setTemplateData = (data: SurveyTemplate) => {
  templateData.value = { ...data }
  loading.value = false
}

/** Get status type for tag */
const getStatusType = (status: number) => {
  return SurveyUtils.getCommonStatusColor(status)
}


/** Get status text */
const getStatusText = (status: number) => {
  const statusMap = {
    0: t('survey.enabled'),
    1: t('survey.disabled')
  }
  return statusMap[status] || t('common.unknown')
}



/** Get question options */
const getQuestionOptions = (question: SurveyQuestion) => {
  if (!question.config || !question.config.options) {
    // 返回默认选项
    return [
      { text: t('survey.option') + ' 1', value: 'option1', score: 1 },
      { text: t('survey.option') + ' 2', value: 'option2', score: 1 }
    ]
  }
  return question.config.options
}

/** Get selection limits for multiple choice */
const getSelectionLimits = (question: SurveyQuestion) => {
  if (question.config && (question.config.minSelections || question.config.maxSelections)) {
    const min = question.config.minSelections || 0
    const max = question.config.maxSelections || question.config.options?.length || 0
    return t('survey.selectionRange') + `: ${min} - ${max}`
  }
  return null
}

/** Get true/false text */
const getTrueFalseText = (question: SurveyQuestion, isTrue: boolean) => {
  if (question.config) {
    return isTrue ? (question.config.trueText || t('survey.correct')) : (question.config.falseText || t('survey.incorrect'))
  }
  return isTrue ? t('survey.correct') : t('survey.incorrect')
}

/** Get true/false score */
const getTrueFalseScore = (question: SurveyQuestion, isTrue: boolean) => {
  if (question.config) {
    return isTrue ? question.config.trueScore : question.config.falseScore
  }
  return isTrue ? 1 : 0
}

/** Get rating max value */
const getRatingMax = (question: SurveyQuestion) => {
  return question.config?.maxScore || 5
}

/** Get rating labels */
const getRatingLabels = (question: SurveyQuestion) => {
  if (question.config?.showLabels) {
    return {
      min: question.config.minLabel || t('survey.poor'),
      max: question.config.maxLabel || t('survey.excellent')
    }
  }
  return null
}

/** Get rating score info */
const getRatingScoreInfo = (question: SurveyQuestion) => {
  if (question.config?.scorePerPoint) {
    return t('survey.scorePerPoint') + `: ${question.config.scorePerPoint}`
  }
  return null
}

/** Get file upload limit */
const getFileUploadLimit = (question: SurveyQuestion) => {
  return question.config?.maxFiles || 1
}

/** Get file upload tip */
const getFileUploadTip = (question: SurveyQuestion) => {
  if (question.config) {
    const types = question.config.allowedTypes?.join(', ') || 'All types'
    const maxSize = question.config.maxFileSize ? formatFileSize(question.config.maxFileSize) : '10MB'
    const maxFiles = question.config.maxFiles || 1
    return `${t('survey.allowedTypes')}: ${types}, ${t('survey.maxFiles')}: ${maxFiles}, ${t('survey.maxSize')}: ${maxSize}`
  }
  return t('survey.selectFile')
}

/** Get text rows */
const getTextRows = (question: SurveyQuestion) => {
  return question.config?.rows || 3
}

/** Get text placeholder */
const getTextPlaceholder = (question: SurveyQuestion) => {
  return question.config?.placeholder || t('survey.pleaseEnter') + t('survey.answer')
}

/** Get text max length */
const getTextMaxLength = (question: SurveyQuestion) => {
  return question.config?.maxLength || 500
}

/** Get text length info */
const getTextLengthInfo = (question: SurveyQuestion) => {
  if (question.config && (question.config.minLength || question.config.maxLength)) {
    const min = question.config.minLength || 0
    const max = question.config.maxLength || t('survey.unlimited')
    return t('survey.lengthRequirement') + `: ${min} - ${max}`
  }
  return null
}

/** Format file size */
const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// Watch props changes
watch(
  () => props.templateId,
  (newId) => {
    if (newId) {
      loadTemplate(newId)
    }
  },
  { immediate: true }
)

watch(
  () => props.template,
  (newTemplate) => {
    if (newTemplate) {
      setTemplateData(newTemplate)
    }
  },
  { immediate: true }
)

// Expose methods for external use
defineExpose({
  loadTemplate,
  setTemplateData,
  // 保持向后兼容
  open: loadTemplate,
  openWithData: setTemplateData
})
</script>

<style lang="scss" scoped>
.template-preview {
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .preview-content {
    max-width: 800px;
    margin: 0 auto;
  }

  .template-header {
    margin-bottom: 32px;
    padding-bottom: 24px;
    border-bottom: 2px solid #f0f2f5;

    .template-title {
      font-size: 28px;
      font-weight: 600;
      color: #1f2937;
      margin: 0 0 16px 0;
      line-height: 1.3;
    }

    .template-meta {
      display: flex;
      align-items: center;
      gap: 20px;
      margin-bottom: 16px;
      flex-wrap: wrap;

      .template-code,
      .template-category {
        font-size: 14px;
        color: #6b7280;
        display: flex;
        align-items: center;
      }
    }

    .template-description {
      font-size: 16px;
      color: #4b5563;
      line-height: 1.6;
      background: #f8fafc;
      padding: 16px;
      border-radius: 6px;
      border-left: 4px solid #3b82f6;
    }
  }

  .questions-preview {
    .questions-header {
      margin-bottom: 24px;

      h3 {
        font-size: 20px;
        font-weight: 600;
        color: #1f2937;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 8px;

        &::before {
          content: '';
          width: 4px;
          height: 20px;
          background: linear-gradient(135deg, #3b82f6, #1d4ed8);
          border-radius: 2px;
        }
      }
    }

    .questions-list {
      display: flex;
      flex-direction: column;
      gap: 24px;
    }

    .question-preview {
      background: #fff;
      border: 1px solid #e5e7eb;
      border-radius: 12px;
      padding: 24px;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;

      &:hover {
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        transform: translateY(-2px);
        border-color: #3b82f6;
      }

      .question-header {
        display: flex;
        align-items: flex-start;
        gap: 12px;
        margin-bottom: 16px;

        .question-number {
          color: #374151;
          font-weight: 600;
          font-size: 14px;
          padding: 6px 12px;
          min-width: 32px;
          text-align: center;
          flex-shrink: 0;
          margin-top: 2px;
        }

        .question-title {
          font-size: 18px;
          font-weight: 600;
          color: #1f2937;
          line-height: 1.4;
          flex: 1;
        }

        .question-tags {
          display: flex;
          gap: 8px;
          flex-shrink: 0;
          align-items: center;
        }
      }

      .question-description {
        margin: 0 44px 20px 44px;
        font-size: 14px;
        color: #6b7280;
        line-height: 1.5;
        background: #f8fafc;
        padding: 12px 16px;
        border-radius: 6px;
        border-left: 3px solid #e5e7eb;
      }

      .question-content {
        margin-left: 44px;

        .question-preview-content {
          background: #fafbfc;
          border: 1px solid #e5e7eb;
          border-radius: 8px;
          padding: 20px;

          .option-item {
            //margin-bottom: 12px;
            font-size: 15px;

            &:last-child {
              margin-bottom: 0;
            }

            .option-score {
              color: #059669;
              font-weight: 500;
              margin-left: 8px;
            }
          }

          .selection-limits,
          .length-info,
          .score-info {
            margin-top: 12px;
            padding-top: 12px;
            border-top: 1px solid #e5e7eb;

            small {
              color: #6b7280;
              font-size: 13px;
            }
          }

          .rating-labels {
            display: flex;
            justify-content: space-between;
            margin-top: 12px;
            font-size: 13px;
            color: #6b7280;
          }

          .el-rate {
            margin-bottom: 8px;
          }

          .el-upload {
            .el-button {
              border-style: dashed;
              border-color: #d1d5db;
              background: #fff;

              &:hover {
                border-color: #3b82f6;
                color: #3b82f6;
              }
            }
          }

          .el-input,
          .el-textarea {
            .el-input__wrapper,
            .el-textarea__inner {
              background: #fff;
              border-color: #d1d5db;

              &:hover {
                border-color: #3b82f6;
              }
            }
          }
        }
      }
    }

    .empty-questions {
      text-align: center;
      padding: 60px 20px;
      background: #fafbfc;
      border-radius: 12px;
      border: 2px dashed #d1d5db;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .template-preview {
    padding: 16px;

    .template-header {
      .template-title {
        font-size: 24px;
      }

      .template-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
      }
    }

    .questions-preview {
      .question-preview {
        padding: 20px 16px;

        .question-header {
          flex-direction: column;
          align-items: flex-start;
          gap: 12px;

          .question-number {
            align-self: flex-start;
          }

          .question-tags {
            align-self: flex-start;
          }
        }

        .question-description,
        .question-content {
          margin-left: 0;
        }
      }
    }
  }
}
</style>
