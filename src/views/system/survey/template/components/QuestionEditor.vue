<template>
  <div class="question-editor" :class="{ 'editing': isEditing }">
    <div class="question-header" :class="{ 'drag-handle': !isEditing }">
      <div class="question-info">
        <div class="question-number">{{ index + 1 }}</div>
        <div class="question-meta">
          <el-tag size="small" :type="SurveyUtils.getQuestionTypeColor(question.questionType)">
            {{ SurveyUtils.getQuestionTypeName(question.questionType) }}
          </el-tag>
          <el-tag v-if="question.required" size="small" type="danger" class="ml-2">
            {{ t('survey.required') }}
          </el-tag>
        </div>
      </div>
      <div class="question-actions">
        <el-button link type="primary" size="small" @click="toggleEdit">
          <Icon :icon="isEditing ? 'ep:check' : 'ep:edit'" />
          {{ isEditing ? t('survey.save') : t('survey.edit') }}
        </el-button>
        <el-button link type="success" size="small" @click="handleDuplicate">
          <Icon icon="ep:copy-document" />
          {{ t('survey.copy') }}
        </el-button>
        <el-button link type="danger" size="small" @click="handleDelete">
          <Icon icon="ep:delete" />
          {{ t('survey.delete') }}
        </el-button>
      </div>
    </div>

    <div class="question-content">
      <!-- Edit Mode -->
      <div v-if="isEditing" class="edit-mode">
        <el-form :model="editData" label-width="140px" size="default" class="question-edit-form">
          <el-row :gutter="20">
            <el-col :span="16">
              <el-form-item :label="t('survey.questionTitle')">
                <el-input
                  v-model="editData.title"
                  :placeholder="t('survey.pleaseEnter') + t('survey.questionTitle')"
                  maxlength="200"
                  show-word-limit
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="16">
              <el-form-item :label="t('survey.description')">
                <el-input
                  v-model="editData.description"
                  type="textarea"
                  :placeholder="t('survey.pleaseEnter') + t('survey.description')"
                  :rows="2"
                  maxlength="500"
                  show-word-limit
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item :label="t('survey.required')" class="required-form-item">
                <el-switch
                  v-model="editData.required"
                  size="default"
                  :active-text="t('survey.yes')"
                  :inactive-text="t('survey.no')"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <!-- Question Type Specific Config -->
          <div class="config-section">
            <h4 class="config-title">{{ t('survey.questionConfig') }}</h4>
            <component
              :is="getConfigComponent(question.questionType)"
              :model-value="editData.config"
              @update:model-value="handleConfigUpdate"
              :question-type="question.questionType"
            />
          </div>
        </el-form>
      </div>

      <!-- Preview Mode -->
      <div v-else class="preview-mode">
        <h4 class="question-title">{{ question.title }}</h4>
        <p v-if="question.description" class="question-desc">{{ question.description }}</p>

        <!-- Question Preview -->
        <div class="question-preview">
          <component
            :is="getPreviewComponent(question.questionType)"
            :config="question.config"
            :question-type="question.questionType"
            disabled
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { SurveyQuestion } from '@/api/system/survey/types'
import { SurveyUtils } from '@/api/system/survey'
import SingleChoiceConfig from './configs/SingleChoiceConfig.vue'
import MultipleChoiceConfig from './configs/MultipleChoiceConfig.vue'
import TrueFalseConfig from './configs/TrueFalseConfig.vue'
import RatingConfig from './configs/RatingConfig.vue'
import FileUploadConfig from './configs/FileUploadConfig.vue'
import TextConfig from './configs/TextConfig.vue'
import SingleChoicePreview from './previews/SingleChoicePreview.vue'
import MultipleChoicePreview from './previews/MultipleChoicePreview.vue'
import TrueFalsePreview from './previews/TrueFalsePreview.vue'
import RatingPreview from './previews/RatingPreview.vue'
import FileUploadPreview from './previews/FileUploadPreview.vue'
import TextPreview from './previews/TextPreview.vue'

interface Props {
  question: SurveyQuestion
  index: number
}

const props = defineProps<Props>()
const emit = defineEmits(['update', 'delete', 'duplicate'])

const { t } = useI18n()

const isEditing = ref(false)
const editData = ref<SurveyQuestion>({ ...props.question })

// Watch for prop changes
watch(() => props.question, (newQuestion) => {
  if (!isEditing.value) {
    editData.value = { ...newQuestion }
  }
}, { deep: true })

/** Toggle edit mode */
const toggleEdit = () => {
  if (isEditing.value) {
    // Save changes
    emit('update', props.index, editData.value)
  } else {
    // Enter edit mode
    editData.value = { ...props.question }
  }
  isEditing.value = !isEditing.value
}

/** Handle duplicate */
const handleDuplicate = () => {
  emit('duplicate', props.index)
}

/** Handle delete */
const handleDelete = () => {
  emit('delete', props.index)
}

/** Handle config update */
const handleConfigUpdate = (newConfig: any) => {
  editData.value = {
    ...editData.value,
    config: newConfig
  }
}



/** Get config component */
const getConfigComponent = (type: number) => {
  const components = {
    1: SingleChoiceConfig,
    2: MultipleChoiceConfig,
    3: TrueFalseConfig,
    4: RatingConfig,
    5: FileUploadConfig,
    6: TextConfig
  }
  return components[type] || 'div'
}

/** Get preview component */
const getPreviewComponent = (type: number) => {
  const components = {
    1: SingleChoicePreview,
    2: MultipleChoicePreview,
    3: TrueFalsePreview,
    4: RatingPreview,
    5: FileUploadPreview,
    6: TextPreview
  }
  return components[type] || 'div'
}
</script>

<style scoped>
.question-editor {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background: white;
  transition: all 0.3s ease;
  overflow: hidden;
}

.question-editor:hover {
  border-color: #c0c4cc;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.question-editor.editing {
  border-color: #409eff;
  box-shadow: 0 4px 20px rgba(64, 158, 255, 0.15);
}



.question-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border-bottom: 1px solid #f0f2f5;
  background: #fafbfc;
  position: relative;
}

.question-header.drag-handle {
  cursor: move;
}

.question-header.drag-handle::before {
  content: '';
  position: absolute;
  left: 8px;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 16px;
  background: repeating-linear-gradient(
    to bottom,
    #c0c4cc 0px,
    #c0c4cc 2px,
    transparent 2px,
    transparent 4px
  );
  opacity: 0.6;
}

.question-editor.editing .question-header {
  cursor: default;
}

.question-editor.editing .question-header::before {
  display: none;
}

.question-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.question-number {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #409eff;
  color: white;
  border-radius: 50%;
  font-size: 12px;
  font-weight: 600;
}

.question-meta {
  display: flex;
  align-items: center;
}

.question-actions {
  display: flex;
  gap: 8px;
}

.question-content {
  padding: 16px;
}

.question-title {
  margin: 0 0 8px 0;
  font-size: 15px;
  font-weight: 500;
  color: #303133;
  line-height: 1.4;
}

.question-desc {
  margin: 0 0 12px 0;
  font-size: 13px;
  color: #606266;
  line-height: 1.5;
}

.question-preview {
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
  margin-top: 8px;
}

.edit-mode {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin: -8px -16px;
  min-height: 200px;
}

.question-editor.editing {
  margin-bottom: 24px;
}

.question-editor.editing .question-content {
  padding: 0;
}

.config-section {
  margin-top: 20px;
  padding: 16px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

.config-title {
  margin: 0 0 16px 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f2f5;
}

:deep(.el-form-item) {
  margin-bottom: 20px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
  font-size: 14px;
  line-height: 32px;
}

/* 防止问题编辑表单标签换行 */
.question-edit-form :deep(.el-form-item__label) {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

:deep(.el-form-item__content) {
  display: flex;
  align-items: center;
  min-height: 32px;
}

:deep(.el-input__wrapper) {
  border-radius: 6px;
}

:deep(.el-textarea__inner) {
  border-radius: 6px;
  font-size: 14px;
  line-height: 1.6;
}

/* 必答开关对齐 */
.required-form-item :deep(.el-form-item__content) {
  display: flex;
  align-items: center;
  height: 32px;
}

.required-form-item :deep(.el-switch) {
  margin: 0;
}
</style>
