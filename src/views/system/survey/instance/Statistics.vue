<template>
  <div class="statistics-page">
    <!-- Header -->
    <div class="editor-header">
      <div class="flex items-center">
        <el-button @click="goBack" circle class="mr-4">
          <Icon icon="ep:arrow-left" />
        </el-button>
        <div>
          <h1 class="text-xl font-bold">
            {{ t('survey.surveyStatistics') }}
          </h1>
        </div>
      </div>
      <div class="flex gap-3">
        <el-button @click="goBack">
          {{ t('common.back') }}
        </el-button>
      </div>
    </div>

    <!-- 统计内容 -->
    <div class="statistics-content">
      <SurveyStatistics
        v-if="instanceId"
        :instance-id="instanceId"
        ref="surveyStatisticsRef"
      />
      <div v-else class="no-data">
        <el-empty :description="t('common.noData')" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { Icon } from '@/components/Icon'
import SurveyStatistics from '@/components/Survey/SurveyStatistics.vue'
import { useI18n } from '@/hooks/web/useI18n'

const { t } = useI18n()
const route = useRoute()
const router = useRouter()

// 响应式数据
const instanceId = ref<number>()
const surveyStatisticsRef = ref()

// 获取实例ID
onMounted(() => {
  const id = route.query.instanceId || route.params.instanceId
  if (id) {
    instanceId.value = Number(id)
  }
})

// 返回上一页
const goBack = () => {
  router.back()
}
</script>

<style lang="scss" scoped>
.statistics-page {
  //height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f7fa;
  overflow-y: auto;
}

.editor-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  background: white;
  border-bottom: 1px solid #e4e7ed;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.statistics-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .no-data {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    background: white;
    margin: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  }
}

// 覆盖统计组件的样式，使其填满容器
:deep(.survey-statistics) {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}
</style>
