<template>
  <div class="app-main-height">
    <!-- Search Form -->
    <ContentWrap>
      <el-form
        class="-mb-15px search-form"
        :model="queryParams"
        ref="queryFormRef"
        :inline="true"
        label-width="120px"
      >
        <el-form-item :label="t('survey.instanceName')" prop="name">
          <el-input
            v-model="queryParams.name"
            :placeholder="t('survey.pleaseEnter') + t('survey.instanceName')"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>

        <el-form-item :label="t('survey.instanceStatus')" prop="status">
          <el-select
            v-model="queryParams.status"
            class="!w-240px"
            clearable
            :placeholder="t('survey.pleaseSelect') + t('survey.instanceStatus')"
          >
            <el-option :label="t('survey.unpublished')" :value="0" />
            <el-option :label="t('survey.ongoing')" :value="1" />
            <el-option :label="t('survey.ended')" :value="2" />
            <el-option :label="t('survey.draft')" :value="3" />
          </el-select>
        </el-form-item>

        <el-form-item :label="t('survey.createTime')" prop="createTime">
          <el-date-picker
            v-model="queryParams.createTime"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="daterange"
            :start-placeholder="t('global.startDate')"
            :end-placeholder="t('global.endDate')"
            :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
            class="!w-220px"
          />
        </el-form-item>
        <el-form-item>
          <el-button @click="handleQuery">
            <Icon icon="ep:search" class="mr-5px" /> {{ t('survey.search') }}
          </el-button>
          <el-button @click="resetQuery">
            <Icon icon="ep:refresh" class="mr-5px" /> {{ t('survey.reset') }}
          </el-button>
          <el-button
            type="primary"
            plain
            @click="openForm('create')"
          >
            <Icon icon="ep:plus" class="mr-5px" /> {{ t('survey.addInstance') }}
          </el-button>
        </el-form-item>
      </el-form>
    </ContentWrap>

    <!-- Instance List -->
    <ContentWrap>
      <el-table
        v-loading="loading"
        :data="instanceList"
        :stripe="true"
        :show-overflow-tooltip="true"
      >
        <el-table-column type="index" :label="t('common.index')" min-width="80" />
        <el-table-column :label="t('survey.instanceName')" prop="name" min-width="200" />
        <el-table-column :label="t('survey.template')" prop="templateName" min-width="150" />
        <el-table-column :label="t('survey.department')" prop="deptName" min-width="130">
          <template #default="scope">
            {{ scope.row.deptName || '-' }}
          </template>
        </el-table-column>
        <el-table-column :label="t('survey.instanceStatus')" align="center" prop="status" min-width="140">
          <template #default="scope">
            <el-tag :type="getInstanceStatusType(scope.row.status)" size="small">
              {{ getInstanceStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column :label="t('survey.startTime')" align="center" prop="startTime" width="160">
          <template #default="scope">
            {{ formatDateTime(scope.row.startTime) }}
          </template>
        </el-table-column>
        <el-table-column :label="t('survey.endTime')" align="center" prop="endTime" width="160">
          <template #default="scope">
            {{ formatDateTime(scope.row.endTime) }}
          </template>
        </el-table-column>
        <el-table-column :label="t('survey.anonymous')" align="center" prop="anonymous" min-width="110">
          <template #default="scope">
            <el-tag :type="scope.row.anonymous ? 'success' : 'info'" size="small">
              {{ scope.row.anonymous ? t('survey.yes') : t('survey.no') }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column :label="t('survey.responseCount')" align="center" min-width="140">
          <template #default="scope">
            <el-tag type="primary" size="small">{{ scope.row.responseCount || 0 }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column :label="t('survey.description')" prop="description" width="250" show-overflow-tooltip />
        <el-table-column
          :label="t('survey.createTime')"
          align="center"
          prop="createTime"
          :formatter="dateFormatter"
          width="180px"
        />
        <el-table-column :label="t('survey.actions')" align="center" min-width="300" fixed="right">
          <template #default="scope">
            <el-button
              link
              type="primary"
              @click="openForm('update', scope.row.id)"
            >
              {{ t('survey.edit') }}
            </el-button>
            <el-button
              link
              type="info"
              @click="openStatistics(scope.row.id)"
            >
              {{ t('survey.statistics') }}
            </el-button>
            <el-button
              v-if="scope.row.status === 0"
              link
              type="success"
              @click="handlePublish(scope.row.id)"
            >
              {{ t('survey.publishInstance') }}
            </el-button>
            <el-button
              v-if="scope.row.status === 1"
              link
              type="warning"
              @click="handleStop(scope.row.id)"
            >
              {{ t('survey.stopInstance') }}
            </el-button>
            <el-button
              link
              type="danger"
              @click="handleDelete(scope.row.id)"
            >
              {{ t('survey.delete') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- Pagination -->
      <Pagination
        :total="instanceTotal"
        v-model:page="queryParams.pageNo"
        v-model:limit="queryParams.pageSize"
        @pagination="getInstanceList"
      />
    </ContentWrap>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onActivated } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from '@/hooks/web/useI18n'
import { useMessage } from '@/hooks/web/useMessage'
import { ContentWrap } from '@/components/ContentWrap'
import { dateFormatter } from '@/utils/formatTime'
import { InstanceApi } from '@/api/system/survey/instance'
import type {
  SurveyInstance,
  InstanceQuery,
  InstanceStatusEnum
} from '@/api/system/survey/types'

defineOptions({ name: 'SurveyInstanceList' })

const { t } = useI18n()
const message = useMessage()
const router = useRouter()

// 响应式数据
const loading = ref(false)
const instanceList = ref<SurveyInstance[]>([])
const instanceTotal = ref(0)

// 查询参数
const queryParams = reactive<InstanceQuery>({
  pageNo: 1,
  pageSize: 10,
  name: undefined,
  status: undefined,
  createTime: undefined
})

// 表单引用
const queryFormRef = ref()

/** 获取实例列表 */
const getInstanceList = async () => {
  loading.value = true
  try {
    const data = await InstanceApi.getPage(queryParams)
    instanceList.value = data.list
    instanceTotal.value = data.total
  } finally {
    loading.value = false
  }
}



/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getInstanceList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 新增/编辑操作 */
const openForm = (type: string, id?: number) => {
  router.push({
    path: `/survey/instance`,
    query: {
      type: type,
      id: id
    }
  })
}

/** 打开统计页面 */
const openStatistics = (instanceId: number) => {
  router.push({
    path: `/survey/instance/statistics`,
    query: {
      instanceId: instanceId
    }
  })
}



/** 发布实例 */
const handlePublish = async (id: number) => {
  try {
    await message.confirm(t('survey.confirmPublish'))
    await InstanceApi.publish(id)
    message.success(t('survey.publishSuccess'))
    getInstanceList()
  } catch (error) {
    console.error('Failed to publish instance:', error)
  }
}

/** 停止实例 */
const handleStop = async (id: number) => {
  try {
    await message.confirm(t('survey.confirmStop'))
    await InstanceApi.end(id)
    message.success(t('survey.stopSuccess'))
    getInstanceList()
  } catch (error) {
    console.error('Failed to stop instance:', error)
  }
}



/** 删除操作 */
const handleDelete = async (id: number) => {
  try {
    await message.confirm(t('survey.confirmDelete'))
    await InstanceApi.delete(id)
    message.success(t('survey.deleteSuccess'))
    getInstanceList()
  } catch (error) {
    console.error('Failed to delete instance:', error)
  }
}



/** 获取实例状态类型 */
const getInstanceStatusType = (status: InstanceStatusEnum): string => {
  const types = {
    0: 'warning',  // 未发布
    1: 'success',  // 进行中
    2: 'info',     // 已结束
    3: 'danger'    // 草稿
  }
  return types[status] || 'info'
}

/** 获取实例状态文本 */
const getInstanceStatusText = (status: InstanceStatusEnum): string => {
  const texts = {
    0: t('survey.unpublished'),
    1: t('survey.ongoing'),
    2: t('survey.ended'),
    3: t('survey.draft')
  }
  return texts[status] || t('survey.unpublished')
}

/** 格式化日期时间 */
const formatDateTime = (timestamp: number): string => {
  if (!timestamp) return '-'
  const date = new Date(timestamp)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

// 初始化
onMounted(() => {
  getInstanceList()
})

// 页面激活时刷新数据（从其他页面返回时）
onActivated(() => {
  getInstanceList()
})
</script>

<style scoped>
.app-main-height {
  height: calc(100vh - 88px);
}

.search-form :deep(.el-form-item:last-child) {
  margin-left: auto;
  flex: 1;
}

.search-form :deep(.el-form-item:last-child .el-form-item__content) {
  justify-content: flex-end;
  display: flex;
  gap: 8px;
}

/* Prevent table header text wrapping */
:deep(.el-table .el-table__header-wrapper .el-table__header th) {
  white-space: nowrap;
}

:deep(.el-table .el-table__header-wrapper .el-table__header th .cell) {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
