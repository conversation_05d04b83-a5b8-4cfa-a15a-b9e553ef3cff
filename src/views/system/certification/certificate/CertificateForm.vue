<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" width="1200px">
    <div class="certificate-form-container">
      <!-- 左侧原有表单 -->
      <div class="form-section">
        <el-form
          ref="formRef"
          :model="formData"
          :rules="formRules"
          label-width="120px"
          v-loading="formLoading"
        >
          <el-form-item :label="t('certificate.name')" prop="name">
            <el-input v-model="formData.name" :placeholder="t('certificate.namePlaceholder')" />
          </el-form-item>
          <el-form-item :label="t('certificate.numberPrefix')" prop="numberPrefix">
            <el-input v-model="formData.numberPrefix" :placeholder="t('certificate.numberPrefixPlaceholder')" />
          </el-form-item>
          <el-form-item :label="t('certificate.template')" prop="tempId">
            <el-select v-model="formData.tempId" :placeholder="t('common.selectText')">
              <el-option
                v-for="item in certificateTemplatList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item :label="t('certificate.logo')" prop="logo">
            <ImageUpload
              v-model="formData.logo"
              class="mt-2.5"
              :limit="1"
              :is-show-tip="true"
              :file-size="500"
              :tip-text="t('category.topic.coverPH')"
              :upload-module="1"
            />
          </el-form-item>
          <el-form-item :label="t('certificate.officialSeal')" prop="officialSeal">
            <ImageUpload
              v-model="formData.officialSeal"
              class="mt-2.5"
              :limit="1"
              :is-show-tip="true"
              :file-size="500"
              :tip-text="t('category.topic.coverPH')"
              :upload-module="1"
            />
          </el-form-item>
          <el-form-item :label="t('certificate.status')" prop="status">
            <el-select v-model="formData.status" :placeholder="t('common.selectText')">
              <el-option
                v-for="dict in getIntDictOptions(DICT_TYPE.CERTIFICATE_STATUS)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-form>
      </div>

      <!-- 右侧预览区 -->
      <div class="preview-section">
        <div class="preview-header">
          <h4>{{ t('certificate.preview') }}</h4>
          <el-button
            type="primary"
            @click="previewCertificate"
            :loading="preview.loading"
            :disabled="!formData.tempId"
          >
            {{ t('certificate.preview') }}
          </el-button>
        </div>

        <div class="preview-content" v-loading="preview.loading">
          <!-- 预览图片 -->
          <div v-if="preview.imageBase64" class="preview-image">
            <img
              :src="`${preview.imageBase64}`"
              :alt="t('certificate.preview')"
              class="certificate-preview-img"
            />
          </div>

          <!-- 预览占位符 -->
          <div v-else-if="!preview.loading" class="preview-placeholder">
            <Icon icon="ep:picture" size="48" />
            <p>{{ t('certificate.previewPlaceholder') }}</p>
          </div>

          <!-- 预览错误 -->
          <div v-if="preview.error" class="preview-error">
            <el-alert
              :title="t('certificate.previewError')"
              :description="preview.error"
              type="error"
              show-icon
              :closable="false"
            />
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <el-button @click="dialogVisible = false">{{ t('common.cancel') }}</el-button>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">{{ t('common.save') }}</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { Icon } from '@/components/Icon'
import ImageUpload from '@/components/ImageUpload/index.vue'
import { CertificateApi, CertificateSaveVO } from '@/api/system/certification/certificate'
import { CertificateTemplateApi, PreviewReq } from '@/api/system/certification/certificatetemplate'
/** 证书 表单 */
defineOptions({ name: 'CertificateForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  tempId: undefined,
  name: undefined,
  numberPrefix: undefined,
  logo: undefined,
  officialSeal: undefined,
  status: undefined
})

// 预览数据
const preview = reactive({
  imageBase64: '',
  loading: false,
  error: null as string | null
})
const formRules = reactive({
  tempId: [{ required: true, message: t('certificate.templateRequired'), trigger: 'change' }],
  name: [{ required: true, message: t('certificate.nameRequired'), trigger: 'blur' }],
  numberPrefix: [{ required: true, message: t('certificate.numberPrefixRequired'), trigger: 'blur' }],
  logo: [{ required: true, message: t('certificate.logoRequired'), trigger: 'change'}],
  officialSeal: [{ required: true, message: t('certificate.officialSealRequired'), trigger: 'blur' }],
  status: [{ required: true, message: t('certificate.statusRequired'), trigger: 'change' }]
})
const formRef = ref() // 表单 Ref

// 证书模板信息
const certificateTemplatList = ref([])
const getCertificateTemplate = async () => {
  try {
    const data = await CertificateTemplateApi.getCertificateTemplatePage({pageNo: 1, pageSize: 10})
    certificateTemplatList.value = data.list
  } catch (error) {
    console.error('Failed to load certificate templates:', error)
    certificateTemplatList.value = []
    message.error(t('certificate.loadTemplatesFailed'))
    // 不抛出错误，让调用方继续执行
  }
}

// 预览功能 - 先保存再预览
const previewCertificate = async () => {
  if (!formData.value.tempId) {
    message.warning(t('certificate.selectCertificateFirst'))
    return
  }

  preview.loading = true
  preview.error = null

  try {
    // 1. 先校验表单
    await formRef.value.validate()

    // 2. 保存证书数据
    const data = formData.value as unknown as CertificateSaveVO
    let certificateId = formData.value.id

    if (formType.value === 'create') {
      // 新增模式：先创建证书
      const result = await CertificateApi.createCertificate(data)
      certificateId = result.id || result // 根据API返回结构调整
      formData.value.id = certificateId
      message.success(t('common.createSuccess'))
    } else {
      // 编辑模式：更新证书
      await CertificateApi.updateCertificate(data)
      message.success(t('common.updateSuccess'))
    }

    // 3. 调用预览接口
    const previewData: PreviewReq = {
      certificateId: certificateId,
      variables: {
        userNickname: 'Sample User',
        certificateName: formData.value.name || 'Sample Certificate',
        certificateNumber: formData.value.numberPrefix || 'CERT-001',
        issueDate: new Date().toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        })
      }
    }

    const response = await CertificateTemplateApi.previewCertificate(previewData)
    preview.imageBase64 = response.imageBase64

  } catch (error: any) {
    preview.error = error.message || t('certificate.previewFailed')
    console.error('Preview failed:', error)
  } finally {
    preview.loading = false
  }
}

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  // 先打开弹窗，避免异步操作失败导致弹窗不显示
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()

  // 加载证书模板列表
  try {
    await getCertificateTemplate()
  } catch (error) {
    console.error('Failed to load certificate templates:', error)
    // 即使加载模板失败，也要保持弹窗打开
  }

  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await CertificateApi.getCertificate(id)
    } catch (error) {
      console.error('Failed to load certificate data:', error)
      message.error(t('certificate.loadDataFailed'))
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as CertificateSaveVO
    if (formType.value === 'create') {
      // 新增模式：如果已经有ID说明预览时已经创建过，只需要更新
      if (formData.value.id) {
        await CertificateApi.updateCertificate(data)
        message.success(t('common.updateSuccess'))
      } else {
        await CertificateApi.createCertificate(data)
        message.success(t('common.createSuccess'))
      }
    } else {
      await CertificateApi.updateCertificate(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    tempId: undefined,
    name: undefined,
    numberPrefix: undefined,
    logo: undefined,
    officialSeal: undefined,
    status: undefined
  }

  // 重置预览状态
  preview.imageBase64 = ''
  preview.loading = false
  preview.error = null

  formRef.value?.resetFields()
}
</script>

<style scoped lang="scss">
.certificate-form-container {
  display: flex;
  gap: 24px;
  height: 600px;
}

.form-section {
  flex: 1;
  overflow-y: auto;
  padding-right: 12px;
}

.preview-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;

  h4 {
    margin: 0;
    color: #303133;
    font-size: 16px;
    font-weight: 600;
  }
}

.preview-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  background: #fafafa;
  position: relative;
}

.preview-image {
  max-width: 100%;
  max-height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;

  .certificate-preview-img {
    max-width: 100%;
    max-height: 100%;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

.preview-placeholder {
  text-align: center;
  color: #909399;

  p {
    margin: 12px 0 0 0;
    font-size: 14px;
  }
}

.preview-error {
  width: 100%;
}
</style>
