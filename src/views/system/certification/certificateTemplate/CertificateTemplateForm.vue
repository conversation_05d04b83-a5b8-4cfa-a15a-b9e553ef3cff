<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" :width="dialogWidth" append-to-body>
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="140px"
      v-loading="formLoading"
    >
      <el-form-item :label="t('certificate.templateName')" prop="name">
        <el-input v-model="formData.name" :placeholder="t('certificate.templateNameRequired')" />
      </el-form-item>
      <el-form-item :label="t('certificate.templateImage')" prop="image">
        <TemplateUpload
          v-model="formData.image"
          class="mt-2.5"
          :limit="1"
          :is-show-tip="true"
          :file-size="500"
          :tip-text="t('category.topic.coverPH')"
          :upload-module="1"
          @confirmName="handleTitleCoordinate"
          @confirmNumber="handleNumberCoordinate"
          @confirmLogo="handleLogoCoordinate"
          @confirmSeal="handleSealCoordinate"
          @confirmUser="handleUserCoordinate"
          @confirmTime="handleTimeCoordinate"
          @imageLoaded="handleImageLoaded"
          :coordinate-details="formData.coordinateDetails"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">Save</el-button>
      <el-button @click="dialogVisible = false">Cancel</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { CertificateTemplateApi, CertificateTemplateSaveVO, CoordinateDetails } from '@/api/system/certification/certificatetemplate'
import TemplateUpload from '@/components/TemplateUpload/index.vue'

/** 证书模板 表单 */
defineOptions({ name: 'CertificateTemplateForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const dialogWidth = ref('800px') // 对话框宽度，动态调整
const formData = ref({
  id: undefined,
  name: undefined,
  image: undefined,
  coordinateDetails: {
    nameDetails: {
      x: 20,
      y: 60,
      width: 200,
      height: 40,
    },
    numberDetails: {
      x: 20,
      y: 100,
      width: 180,
      height: 30,
    },
    timeDetails: {
      x: 20,
      y: 140,
      width: 160,
      height: 30,
    },
    userDetails: {
      x: 20,
      y: 180,
      width: 150,
      height: 35,
    },
    logoDetails: {
      x: 20,
      y: 220,
      width: 100,
      height: 100,
    },
    officialSealDetails: {
      x: 20,
      y: 340,
      width: 100,
      height: 100,
    }
  }
})



// 文本元素坐标和尺寸处理函数
const handleTitleCoordinate = (x: number, y: number, width?: number, height?: number) => {
  formData.value.coordinateDetails.nameDetails.x = x
  formData.value.coordinateDetails.nameDetails.y = y
  if (width !== undefined) formData.value.coordinateDetails.nameDetails.width = width
  if (height !== undefined) formData.value.coordinateDetails.nameDetails.height = height
}
const handleNumberCoordinate = (x: number, y: number, width?: number, height?: number) => {
  formData.value.coordinateDetails.numberDetails.x = x
  formData.value.coordinateDetails.numberDetails.y = y
  if (width !== undefined) formData.value.coordinateDetails.numberDetails.width = width
  if (height !== undefined) formData.value.coordinateDetails.numberDetails.height = height
}
const handleUserCoordinate = (x: number, y: number, width?: number, height?: number) => {
  formData.value.coordinateDetails.userDetails.x = x
  formData.value.coordinateDetails.userDetails.y = y
  if (width !== undefined) formData.value.coordinateDetails.userDetails.width = width
  if (height !== undefined) formData.value.coordinateDetails.userDetails.height = height
}
const handleTimeCoordinate = (x: number, y: number, width?: number, height?: number) => {
  formData.value.coordinateDetails.timeDetails.x = x
  formData.value.coordinateDetails.timeDetails.y = y
  if (width !== undefined) formData.value.coordinateDetails.timeDetails.width = width
  if (height !== undefined) formData.value.coordinateDetails.timeDetails.height = height
}

// 图片元素坐标和尺寸处理函数
const handleLogoCoordinate = (x: number, y: number, width?: number, height?: number) => {
  formData.value.coordinateDetails.logoDetails.x = x
  formData.value.coordinateDetails.logoDetails.y = y
  if (width !== undefined) formData.value.coordinateDetails.logoDetails.width = width
  if (height !== undefined) formData.value.coordinateDetails.logoDetails.height = height
}
const handleSealCoordinate = (x: number, y: number, width?: number, height?: number) => {
  formData.value.coordinateDetails.officialSealDetails.x = x
  formData.value.coordinateDetails.officialSealDetails.y = y
  if (width !== undefined) formData.value.coordinateDetails.officialSealDetails.width = width
  if (height !== undefined) formData.value.coordinateDetails.officialSealDetails.height = height
}

// 处理图片加载完成，动态调整对话框宽度
const handleImageLoaded = (imageInfo: { displayWidth: number, displayHeight: number }) => {
  // 计算合适的对话框宽度：图片宽度 + 表单宽度 + 边距
  const formWidth = 300 // 表单大概宽度
  const padding = 80 // 对话框内边距
  const minWidth = 800 // 最小宽度

  const calculatedWidth = Math.max(minWidth, imageInfo.displayWidth + formWidth + padding)
  dialogWidth.value = `${calculatedWidth}px`

  console.log(`对话框宽度已调整为: ${dialogWidth.value}`)
}
const formRules = reactive({
  name: [{ required: true, message: t('certificate.templateNameRequired'), trigger: 'blur' }],
  image: [{ required: true, message: t('certificate.templateImageRequired'), trigger: 'blur' }],
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await CertificateTemplateApi.getCertificateTemplate(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as CertificateTemplateSaveVO
    if (formType.value === 'create') {
      await CertificateTemplateApi.createCertificateTemplate(data)
      message.success(t('common.createSuccess'))
    } else {
      await CertificateTemplateApi.updateCertificateTemplate(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    name: undefined,
    image: undefined,
    coordinateDetails: {
      nameDetails: {
        x: 20,
        y: 60,
        width: 200,
        height: 40,
      },
      numberDetails: {
        x: 20,
        y: 100,
        width: 180,
        height: 30,
      },
      timeDetails: {
        x: 20,
        y: 140,
        width: 160,
        height: 30,
      },
      userDetails: {
        x: 20,
        y: 180,
        width: 150,
        height: 35,
      },
      logoDetails: {
        x: 20,
        y: 220,
        width: 100,
        height: 100,
      },
      officialSealDetails: {
        x: 20,
        y: 340,
        width: 100,
        height: 100,
      }
    }
  }

  formRef.value?.resetFields()
}
</script>
