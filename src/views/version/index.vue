<script lang="ts" setup>
import { ref, onMounted } from 'vue'

defineOptions({ name: 'Version' })

const { t } = useI18n()
const versionContent = ref('-')
const latestVersion = ref('-')
const releaseDate = ref('-')

// 获取VERSION.md内容
const loadVersionContent = async () => {
  try {
    // 测试环境
    const response = await fetch('/VERSION.md')
    // UAT
    // const response = await fetch('/admin/VERSION.md')
    const text = await response.text()

    // 提取最新版本信息
    const versionMatch = text.match(/##\s+(v[\d.]+)\s+\(([^)]+)\)/)
    if (versionMatch) {
      latestVersion.value = versionMatch[1]
      releaseDate.value = versionMatch[2]
    }

    versionContent.value = parseMarkdown(text)
  } catch (error) {
    console.error('加载版本信息失败:', error)
    versionContent.value = '<p>版本信息加载失败</p>'
  }
}

// 简单的markdown解析器
const parseMarkdown = (md: string): string => {
  return md
    .replace(/^# (.*$)/gm, '<h1 class="text-2xl font-bold mb-4 text-primary">$1</h1>')
    .replace(/^## (.*$)/gm, '<h2 class="text-xl font-semibold mb-3 mt-6 text-gray-800">$1</h2>')
    .replace(/^### (.*$)/gm, '<h3 class="text-lg font-medium mb-2 mt-4 text-gray-700">$1</h3>')
    .replace(
      /^\- ✨ (.*$)/gm,
      '<li class="flex items-center mb-1"><span class="text-green-500 mr-2">✨</span>$1</li>'
    )
    .replace(
      /^\- 🔧 (.*$)/gm,
      '<li class="flex items-center mb-1"><span class="text-blue-500 mr-2">🔧</span>$1</li>'
    )
    .replace(
      /^\- ⚡ (.*$)/gm,
      '<li class="flex items-center mb-1"><span class="text-yellow-500 mr-2">⚡</span>$1</li>'
    )
    .replace(
      /^\- 🐛 (.*$)/gm,
      '<li class="flex items-center mb-1"><span class="text-red-500 mr-2">🐛</span>$1</li>'
    )
    .replace(/^\- (.*$)/gm, '<li class="mb-1">• $1</li>')
    .replace(/^---$/gm, '<hr class="my-6 border-gray-300">')
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
    .replace(/`(.*?)`/g, '<code class="bg-gray-100 px-1 rounded">$1</code>')
    .replace(/\n\n/g, '</p><p class="mb-3">')
    .replace(/^(?!<[h|l|p|d])/gm, '<p class="mb-3">')
    .replace(/<li/g, '</p><ul class="list-none mb-3"><li')
    .replace(/<\/li>(?!\s*<li)/g, '</li></ul><p class="mb-3">')
}

onMounted(() => {
  loadVersionContent()
})
</script>

<template>
  <ContentWrap>
    <div class="flex flex-col space-y-4 p-3">
      <div class="flex !items-center justify-start gap-2">
        <Icon icon="ep:info-filled" :size="20" />
        <span class="text-xl font-bold">{{ t('router.versionInfo') }}</span>
      </div>

      <div class="flex gap-4">
        <span>System Name:</span>
        <span class="font-medium">Online Learning Platform</span>
      </div>

      <div class="flex gap-4">
        <span>Latest Version:</span>
        <span class="font-medium">{{ latestVersion }}</span>
      </div>

      <div class="flex gap-4">
        <span>Release Date:</span>
        <span class="font-medium">{{ releaseDate }}</span>
      </div>
    </div>
  </ContentWrap>

  <ContentWrap>
    <div class="p-4">
      <div v-html="versionContent" class="prose prose-sm max-w-none"></div>
    </div>
  </ContentWrap>
</template>