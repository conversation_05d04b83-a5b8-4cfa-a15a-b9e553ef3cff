<template>
  <div class="input-form">
    <!--input表单-->
    <el-form ref="formLogin" :model="loginData.loginForm" :rules="LoginRules" class="login-form">
      <!--Username-->
      <el-form-item prop="username">
        <el-text>{{ t('login.username') }}</el-text>
        <el-input
          v-model="loginData.loginForm.username"
          :placeholder="t('login.usernamePlaceholder')"
        />
      </el-form-item>

      <!--Password-->
      <el-form-item prop="password">
        <el-text>{{ t('login.password') }}</el-text>
        <el-input
          v-model="loginData.loginForm.password"
          :placeholder="t('login.passwordPlaceholder')"
          show-password
          type="password"
        />
      </el-form-item>
    </el-form>

    <!--记住密码-->
    <el-checkbox v-model="loginData.loginForm.rememberMe" @change="handleRememberMeChange">
      <el-text>{{ t('login.remember') }}</el-text>
    </el-checkbox>

    <!--登录&注册按钮-->
    <div class="login-btn-group">
      <el-button class="login-btn" @click="getCode()" :loading="loginLoading">{{
        t('login.login')
      }}</el-button>
      <!-- <el-button class="signup-btn">{{ t('login.register') }}</el-button> -->
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ElLoading } from 'element-plus'
import LoginFormTitle from './LoginFormTitle.vue'
import type { RouteLocationNormalizedLoaded } from 'vue-router'

import { useIcon } from '@/hooks/web/useIcon'

import * as authUtil from '@/utils/auth'
import { usePermissionStore } from '@/store/modules/permission'
import * as LoginApi from '@/api/login'
import { LoginStateEnum, useFormValid, useLoginState } from './useLogin'
import { decrypt, encrypt } from '@/utils/jsencrypt'
import { useUserStore } from '@/store/modules/user'
import { loginTypeEnum } from '@/api/login'
import { useCache, CACHE_KEY } from '@/hooks/web/useCache'

defineOptions({ name: 'LoginForm' })

const { t } = useI18n()
const message = useMessage()
const { wsCache } = useCache()
const iconHouse = useIcon({ icon: 'ep:house' })
const iconAvatar = useIcon({ icon: 'ep:avatar' })
const iconLock = useIcon({ icon: 'ep:lock' })
const formLogin = ref()
const { validForm } = useFormValid(formLogin)
const { setLoginState, getLoginState } = useLoginState()
const { currentRoute, push } = useRouter()
const permissionStore = usePermissionStore()
const redirect = ref<string>('')
const loginLoading = ref(false)
const verify = ref()
const captchaType = ref('blockPuzzle') // blockPuzzle 滑块 clickWord 点击文字

const getShow = computed(() => unref(getLoginState) === LoginStateEnum.LOGIN)
const userStore = useUserStore()

const LoginRules = {
  tenantName: [required],
  username: [required],
  password: [required]
}
const loginData = reactive({
  isShowPassword: false,
  captchaEnable: import.meta.env.VITE_APP_CAPTCHA_ENABLE,
  tenantEnable: import.meta.env.VITE_APP_TENANT_ENABLE,
  loginForm: {
    tenantName: import.meta.env.DEV ? import.meta.env.VITE_APP_DEFAULT_LOGIN_TENANT || '' : '', // 为开发模式时，自动填写如下信息；否则，为空
    username: import.meta.env.DEV ? import.meta.env.VITE_APP_DEFAULT_LOGIN_USERNAME || '' : '',
    password: import.meta.env.DEV ? import.meta.env.VITE_APP_DEFAULT_LOGIN_PASSWORD || '' : '',
    captchaVerification: '',
    rememberMe: true // 默认记录我。如果不需要，可手动修改
  }
})

const socialList = [
  { icon: 'ant-design:wechat-filled', type: 30 },
  { icon: 'ant-design:dingtalk-circle-filled', type: 20 },
  { icon: 'ant-design:github-filled', type: 0 },
  { icon: 'ant-design:alipay-circle-filled', type: 0 }
]

// 获取验证码
const getCode = async () => {
  // 情况一，未开启：则直接登录
  if (loginData.captchaEnable === 'false') {
    await handleLogin({})
  } else {
    // 情况二，已开启：则展示验证码；只有完成验证码的情况，才进行登录
    // 弹出验证码
    verify.value.show()
  }
}
// 获取租户 ID
const getTenantId = async () => {
  if (loginData.tenantEnable === 'true') {
    const res = await LoginApi.getTenantIdByName(loginData.loginForm.tenantName)
    authUtil.setTenantId(res)
  }
}
// 记住我
const getLoginFormCache = () => {
  // 在开发环境下，优先使用环境变量，不读取缓存
  if (import.meta.env.DEV) {
    return
  }

  const loginForm = authUtil.getLoginForm()
  if (loginForm) {
    loginData.loginForm = {
      ...loginData.loginForm,
      username: loginForm.username ? loginForm.username : loginData.loginForm.username,
      password: loginForm.password ? loginForm.password : loginData.loginForm.password,
      rememberMe: loginForm.rememberMe,
      tenantName: loginForm.tenantName ? loginForm.tenantName : loginData.loginForm.tenantName
    }
  }
}

// 记住密码变化处理
const handleRememberMeChange = () => {
  if (loginData.loginForm.rememberMe) {
    // 选中记住密码时，保存当前表单数据（密码会在 authUtil.setLoginForm 中加密）
    if (loginData.loginForm.username && loginData.loginForm.password) {
      authUtil.setLoginForm(loginData.loginForm)
    }
  } else {
    // 取消记住密码时，清除缓存
    authUtil.removeLoginForm()
  }
}
// 根据域名，获得租户信息
const getTenantByWebsite = async () => {
  const website = location.host
  const res = await LoginApi.getTenantByWebsite(website)
  if (res) {
    loginData.loginForm.tenantName = res.name
    authUtil.setTenantId(res.id)
  }
}
const loading = ref() // ElLoading.service 返回的实例
// 登录
const handleLogin = async (params) => {
  loginLoading.value = true
  // 默认自定义登录
  wsCache.set('loginType', loginTypeEnum.PASSWORD)
  try {
    await getTenantId()
    const data = await validForm()
    if (!data) {
      return
    }
    // 进行数据深拷贝, 防止登录密码在输入框展示被加密的字符串
    const loginDataLoginForm = JSON.parse(JSON.stringify(loginData.loginForm))
    loginDataLoginForm.password = encrypt(loginDataLoginForm.password) as string
    loginDataLoginForm.captchaVerification = params.captchaVerification
    const res = await LoginApi.login(loginDataLoginForm)
    if (!res) {
      return
    }
    loading.value = ElLoading.service({
      lock: true,
      text: '正在加载系统中...',
      background: 'rgba(0, 0, 0, 0.7)'
    })
    if (loginData.loginForm.rememberMe) {
      // 保存原始表单数据（密码是明文），authUtil.setLoginForm 会自动加密密码
      authUtil.setLoginForm(loginData.loginForm)
    } else {
      authUtil.removeLoginForm()
    }
    authUtil.setToken(res)
    if (!redirect.value) {
      redirect.value = '/'
    }
    // 判断是否为SSO登录
    if (redirect.value.indexOf('sso') !== -1) {
      window.location.href = window.location.href.replace('/login?redirect=', '')
    } else {
      push({ path: redirect.value || permissionStore.addRouters[0].path })
    }
  } finally {
    loginLoading.value = false
    loading.value.close()
  }
}

// 社交登录
const doSocialLogin = async (type: number) => {
  if (type === 0) {
    message.error('此方式未配置')
  } else {
    loginLoading.value = true
    if (loginData.tenantEnable === 'true') {
      // 尝试先通过 tenantName 获取租户
      await getTenantId()
      // 如果获取不到，则需要弹出提示，进行处理
      if (!authUtil.getTenantId()) {
        try {
          const data = await message.prompt('请输入租户名称', t('common.reminder'))
          if (data?.action !== 'confirm') throw 'cancel'
          const res = await LoginApi.getTenantIdByName(data.value)
          authUtil.setTenantId(res)
        } catch (error) {
          if (error === 'cancel') return
        } finally {
          loginLoading.value = false
        }
      }
    }
    // 计算 redirectUri
    // tricky: type、redirect需要先encode一次，否则钉钉回调会丢失。
    // 配合 Login/SocialLogin.vue#getUrlValue() 使用
    const redirectUri =
      location.origin +
      '/social-login?' +
      encodeURIComponent(`type=${type}&redirect=${redirect.value || '/'}`)

    // 进行跳转
    const res = await LoginApi.socialAuthRedirect(type, encodeURIComponent(redirectUri))
    window.location.href = res
  }
}
watch(
  () => currentRoute.value,
  (route: RouteLocationNormalizedLoaded) => {
    redirect.value = route?.query?.redirect as string
  },
  {
    immediate: true
  }
)
onMounted(() => {
  getLoginFormCache()
  getTenantByWebsite()
})
</script>

<style lang="scss" scoped>
/* 登录表单样式 */
.input-form {
  display: flex;
  flex-direction: column;
  gap: 12px;

  max-width: 348px;

  // 修改input小标题字体颜色
  :deep(.el-text) {
    margin-left: 1px;
    color: rgba(36, 52, 63, 1); // 从视觉上与带有圆角的表单及按钮保持对齐
  }

  // 修改input框样式
  :deep(.el-input__wrapper) {
    height: 40px;
    border-radius: 8px;

    &.is-focus {
      box-shadow: none;
      border: 1px solid var(--cs-color-primary) !important;
    }
  }

  // 定义表单项底部间距
  :deep(.el-form-item) {
    position: relative;
    margin-bottom: 4px; // 设置统一间距

    &:last-child {
      margin-bottom: 0; // 最后一个表单项无间距
    }

    // 修改错误提示位置至右侧
    .el-form-item__error {
      position: absolute;
      left: 0;
      right: 1px;
      text-align: right;
    }
  }

  /* 记住密码复选框 */
  :deep(.el-checkbox) {
    // 从视觉上与带有圆角的表单及按钮保持对齐
    // 更靠近密码输入框
    margin: -8px 0 0 1px;

    // 增大复选框圆角
    .el-checkbox__inner {
      border-radius: 4px;
      border: 1px solid var(--cs-color-primary);

      will-change: transform, background-color; // 性能优化：提前通知浏览器变化属性
      transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1); // 使用贝塞尔曲线添加过渡动画增强交互效果
    }

    .el-checkbox__input.is-checked .el-checkbox__inner {
      background-color: var(--cs-color-primary);
    }

    .el-text {
      justify-content: center;
      align-items: center;

      color: rgba(36, 52, 63, 0.4);

      will-change: transform, background-color; // 性能优化：提前通知浏览器变化属性
      transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1); // 使用贝塞尔曲线添加过渡动画增强交互效果

      &:hover {
        color: rgba(36, 52, 63, 0.8);
      }

      &:active {
        color: rgba(75, 96, 18, 1);
      }
    }
  }

  /* 登录注册按钮 */
  .login-btn-group {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    gap: 12px;

    width: 100%;

    // 统一按钮盒模型
    .el-button {
      box-sizing: border-box !important;
      margin: 0 !important; // 清除默认外边距
      padding: 0 16px !important; // 控制左右内边距
    }

    //登录按钮
    .login-btn {
      height: 40px;
      width: 100%;
      border: none;
      border-radius: 8px;
      color: white;
      background-color: rgba(75, 96, 18, 1);

      will-change: transform, background-color; // 性能优化：提前通知浏览器变化属性
      transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1); // 使用贝塞尔曲线添加过渡动画增强交互效果

      // 悬停状态
      &:hover {
        background-color: rgba(75, 96, 18, 0.8); // 颜色变浅20%
      }

      // 点击状态
      &:active {
        transform: scale(0.98); // 缩小2%
        background-color: rgba(75, 96, 18, 0.8);
      }
    }

    //注册按钮
    .signup-btn {
      height: 40px;
      width: 100%;
      border-color: rgba(75, 96, 18, 1);
      border-radius: 8px;
      color: rgba(75, 96, 18, 1);
      background-color: white;

      will-change: transform, background-color; // 性能优化：提前通知浏览器变化属性
      transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1); // 使用贝塞尔曲线添加过渡动画增强交互效果

      // 悬停状态
      &:hover {
        border-color: rgba(75, 96, 18, 0.5);
        background-color: rgba(255, 255, 255, 0.5);
      }

      // 点击状态
      &:active {
        transform: scale(0.98); // 缩小2%
        border-color: rgba(75, 96, 18, 0.5);
        background-color: rgba(255, 255, 255, 0.5);
      }
    }
  }
}
</style>
