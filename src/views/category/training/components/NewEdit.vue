<script setup lang="ts" name="newEdit">
import type { ComponentInternalInstance } from 'vue'
import type { FormInstance, FormProps } from 'element-plus'
import { addTopic, listTopic, TopicRespVO, updateTopic } from '@/api/category/training'
import { defaultProps, handleTree } from '@/utils/tree'
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
const props = defineProps<{ titleName: string, modelValue: boolean, formData: object }>()
const emit = defineEmits(['update:isVisible', 'update:modelValue', 'parentEmit'])
const { t } = useI18n()
const message = useMessage() // 消息弹窗
const labelPosition = ref<FormProps['labelPosition']>('top')
const formRef = ref<FormInstance>()
const formLoading = ref(false)
const subjectList = ref()
const data = reactive<{
  rules: any
}>({
  rules: {
    name: [{ required: true, message: t('category.topic.subjectNameRule'), trigger: 'change' }],
    ddt: [{ required: true, message: t('sys.user.statusRule'), trigger: 'change' }],
    sort: [{ required: false, message: t('category.topic.sortRule'), trigger: 'blur' }],
  },
})

const { rules } = toRefs(data)
const form = ref({
  name: '',
  sort: 1,
  parentId: '',
  description: '',
  level: 0,
  type: 3, // 1 hse 2 ddt 3 others
  status: undefined
})
const isVisible = ref(false)
const inputValue = ref('')
const inputVisible = ref()
const InputRef = ref()
/** 表单重置 */
const reset = () => {
  formRef.value?.resetFields()
}
const handleOpen = () => {
  getList()
  form.value = {
    sort: 1,
    level: 0
  }
  isVisible.value = true
}
const handleEdit = (row: TopicRespVO) => {
  getList()
  form.value = { ...row, parentId: +row.parentId === 0 ? '' : +row.parentId, sort: row.sort ? row.sort : 1, level: row.level ? row.level : 1 }
  isVisible.value = true
}
const handleClose = () => {
  isVisible.value = false
}

const getList = async () => {
  const data = await listTopic({})
  subjectList.value = handleTree(data)
}
/** 提交按钮 */
const handleConfirm = async () => {
  const valid = await formRef.value.validate()
  if (!valid) return
  try {
    formLoading.value = true
    form.value.parentId = form.value.parentId && form.value.parentId !== '' ? form.value.parentId : 0
    if (form.value.id !== undefined) {
      await updateTopic({ ...form.value })
      message.success(t('common.updateSuccess'))
      emit('parentEmit')
    } else {
      await addTopic({ ...form.value })
      message.success(t('common.createSuccess'))
      emit('parentEmit')
    }
    isVisible.value = false
  } finally {
    formLoading.value = false
  }
}
const showInput = () => {
  inputVisible.value = true
  nextTick(() => {
    InputRef.value!.input!.focus()
  })
}

const handleInputConfirm = () => {
  if (inputValue.value) {
    form.value.keywords.push(inputValue.value)
  }
  inputVisible.value = false
  inputValue.value = ''
}

const handleCloseTag = (tag: string) => {
  form.value.keywords.splice(form.value.keywords.indexOf(tag), 1)
}

defineExpose({ handleOpen, handleEdit })
</script>

<template>
  <Dialog v-model="isVisible" :title="props.titleName" @close="handleClose">
    <el-form ref="formRef" :model="form" label-width="auto" class="demo-dynamic" :label-position="labelPosition" :rules="rules">
      <el-form-item v-if="form.id && !form.parentId && props.titleName === t('category.topic.addTopic')" prop="parentId" :label="t('category.topic.parentSubject0')">
        <el-tree-select
          v-model="form.parentId"
          :data="subjectList"
          :props="defaultProps"
          clearable
          check-strictly
          default-expand-all
          value-key="deptId"
          class="!w-240px"
          filterable
        />
      </el-form-item>
      <el-form-item v-else-if="+form.parentId && props.titleName === t('category.topic.editTopic')" prop="parentId" :label="t('category.topic.parentSubject')">
        <el-tree-select
          v-model="form.parentId"
          :data="subjectList"
          :props="defaultProps"
          filterable
          clearable
          check-strictly
          default-expand-all
          value-key="deptId"
          class="!w-240px"
          :disabled="props.titleName === t('category.topic.editTopic')"
        />
      </el-form-item>
      <el-form-item prop="name" :label="t('category.topic.subjectName')">
        <el-input v-model="form.name" maxlength="50" show-word-limit />
      </el-form-item>
      <el-form-item prop="sort" :label="t('category.topic.sort')">
        <el-input-number v-model="form.sort" :min="1" :max="99" controls-position="right" />
      </el-form-item>
      <el-form-item label="Status" prop="type">
        <el-radio-group v-model="form.type">
          <el-radio
            v-for="dict in getIntDictOptions(DICT_TYPE.TRAINING_CATEGORY_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value + 1"
          />
        </el-radio-group>
      </el-form-item>
      <el-form-item prop="description" :label="t('category.topic.introduction')">
        <el-input v-model="form.description" type="textarea" show-word-limit maxlength="5000" :rows="4" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button type="primary" :loading="formLoading" @click="handleConfirm">
        {{ t('global.confirm') }}
      </el-button>
      <el-button @click="handleClose">
        {{ t('global.cancel') }}
      </el-button>
    </template>
  </Dialog>
</template>
