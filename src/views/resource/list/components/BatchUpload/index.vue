<script setup lang="ts" name="AddResource">
import type { ComponentInternalInstance } from 'vue'
import {
  ElLink,
  ElNotification,
  type FormInstance,
  type FormRules,
  type NotificationHandle
} from 'element-plus'
import { Close } from '@element-plus/icons-vue'
import DeptTreeSelect from '../DeptTreeSelect/index.vue'
import DurationInput from '../DurationInput/index.vue'
import { formatFileType } from './script'
import CustomDialog from '@/components/CustomDialog/index.vue'
import LargeFileUpload from '@/components/LargeFileUpload/index.vue'
import type { CustomUploadUserFile } from '@/components/LargeFileUpload/index.vue'
import { deptTreeSelect } from '@/api/system/user'
import { MediaType } from '@/enums/resource'
import { formatBytes, formatSecond } from '@/utils/ruoyi'
import { addResource, MediaTypeEnum, type ResourceSaveVO } from '@/api/resource/list'
import { customColor } from '@/components/LargeFileUpload/script'
import { DICT_TYPE, getIntDictOptions, getStrDictOptions } from '@/utils/dict'

interface RequestType {
  duration?: number | string | undefined
  fileId: number
  mediaType?: MediaType
  lang: string
  size: number
  title: string
}
interface FormAPI {
  index: number
  name: string
  lang: string[] | undefined
  deptIds: string[] | undefined
  duration: number | undefined
  size: number
  mediaType: MediaType | undefined
  hash: string | undefined
  response: CustomUploadUserFile['response']
  isExist?: boolean
  deleting?: boolean
}
const emit = defineEmits(['handleSearch'])
const { t } = useI18n()
const message = useMessage() // 消息弹窗
const router = useRouter()
const { name } = useRoute()
const route = useRoute()
const props = defineProps<{
  mediaType: number
}>()
const tool = ref<{
  lang: undefined | string[]
  deptIds: undefined | string[]
  duration: undefined | number
}>({
  lang: undefined,
  deptIds: undefined,
  duration: undefined
})
// const form = ref<FormAPI[]>()
const form = reactive<{ resource: FormAPI[] | undefined }>({
  resource: undefined
})
const rules = ref<FormRules<FormAPI>>({
  lang: [{ required: true, message: t('learningCenter.course.languageRule'), trigger: 'change' }],
  deptIds: [{ required: true, message: t('resource.dataPermissionsRule'), trigger: 'change' }],
  name: [{ required: true, message: t('sys.user.userNicknameRule'), trigger: 'blur' }],
  duration: [
    {
      required: true,
      validator: (rule, value, callback) => {
        if (value == null) {
          callback(new Error(t('learningCenter.course.durationRule')))
          return
        }
        callback()
      }
    }
  ]
})
const formRef = ref<FormInstance>()
const toolRef = ref<FormInstance>()
const largeRef = ref<InstanceType<typeof LargeFileUpload>>()
const uploadList = ref<CustomUploadUserFile[]>()
const treeOptions = ref([])
const uploadLimit = 999999999
const isDialogClosedBySubmit = ref(false)
// 文件上传状态，上传完成的数量（包括成功和失败）
const fileUploadStatus = computed(() => {
  const count = uploadList.value?.length || 0
  // 改进：使用viewStatus来判断上传是否完成，而不是percentage
  const complete =
    uploadList.value?.filter((item) => item.viewStatus === 'success' || item.viewStatus === 'fail')
      .length || 0

  // 新增：正在上传中的文件数量（包括uploading和merging状态）
  const uploading =
    uploadList.value?.filter(
      (item) => item.viewStatus === 'uploading' || item.viewStatus === 'merging'
    ).length || 0

  return {
    count,
    complete,
    uploading,
    // 兼容性：保留原有的percentage判断作为备用
    completeByPercentage: uploadList.value?.filter((item) => item.percentage === 100).length || 0
  }
})
/**
 * 存在未处理的资源
 */
const isHasResource = computed(() => {
  return form.resource && form.resource.length > 0
})
/** 文件上传完毕但是有错误的数量 */
const fileUploadResult = computed(() => {
  if (!uploadList.value || uploadList.value.length === 0) {
    return undefined
  }

  // 获取文件上传成功的数量
  const successNum = uploadList.value.filter((item) => item.viewStatus === 'success').length
  const errorNum = uploadList.value.filter((item) => item.viewStatus === 'fail').length
  const uploadingNum = uploadList.value.filter(
    (item) =>
      item.viewStatus === 'uploading' ||
      item.viewStatus === 'merging' ||
      item.viewStatus === 'ready'
  ).length

  // 改进：只有当所有文件都有最终状态时才返回结果
  if (successNum + errorNum === uploadList.value.length && uploadingNum === 0) {
    return {
      successNum,
      errorNum,
      totalNum: uploadList.value.length
    }
  }
  return undefined
})
const loading = ref(false)
const show = ref(false)
const isMini = ref(false)
let openNotification: NotificationHandle
/**
 * 创建页面通知
 * @param type 创建的通知类型，1文件上传完毕，2阻止离开页面
 */
const createNotication = (type: 1 | 2 | 3) => {
  const clickHereNode = h(
    ElLink,
    { type: 'primary', underline: false, onClick: handleToResource },
    () => 'Click here to return to the resource management page'
  )
  if (type === 1) {
    // 文件上传完毕提示框
    openNotification = ElNotification({
      title: 'Resource',
      type: 'success',
      duration: 0,
      message: h('p', [t('resource.fileUploadCompleted'), clickHereNode])
    })
  } else if (type === 2) {
    nextTick(() => {
      // 阻止关闭页面提示框
      openNotification = ElNotification({
        title: 'Resource',
        type: 'info',
        duration: 0,
        message: h('p', [t('resource.thereAreStillUnprocessedFiles'), clickHereNode])
      })
    })
  } else if (type === 3) {
    openNotification = ElNotification({
      title: 'Resource',
      type: 'error',
      duration: 0,
      message: h('p', [t('resource.allFilesError'), clickHereNode])
    })
  }
}
const getDeptTreeList = async () => {
  treeOptions.value = await deptTreeSelect({})
}

/**
 * 当前form是否已经存在对应的值
 * @param file 文件信息
 */
function isHasValue(
  file: CustomUploadUserFile,
  key: keyof FormAPI,
  newUploadList: CustomUploadUserFile[]
) {
  const index = file.index as number
  const lastIndex = file.lastIndex as number
  // 如果最后一项没有lastIndex,证明此次是添加操作，就需要停止数据还原，因为index与现在的form表单是对应的关系
  if (!newUploadList[newUploadList.length - 1].lastIndex) {
    // 读取现在表单中保存的值
    const formValue = form.resource![index]?.[key]
    // 对于lang特殊设置
    if (key === 'lang' && !formValue) {
      return '1'
    }
    return form.resource![index]?.[key]
  }
  if (form.resource![lastIndex || index]?.[key]) {
    return form.resource![lastIndex || index][key]
  } else {
    return key === 'lang' ? 0 : ''
  }
}
const formatDuration = (num: string) => {
  let [min, second] = num.split('.').map(Number)
  // if (second % 1 !== 0) {
  //   // 是小数，先取到分钟
  //   min = Math.trunc(second % 60)
  // }
  // 将秒数转换为分钟
  if (second >= 60) {
    min = min + Math.floor(second / 60)
    second = second % 60 || 0
  }
  return {
    min,
    second: second || 0
  }
}

/**
 * 批量设置form表单的值
 */
const handleBatchSetForm = () => {
  form.resource?.forEach((element) => {
    element.lang =
      (tool.value.lang && tool.value.lang?.length > 0 ? tool.value.lang : undefined) || element.lang
    element.deptIds = tool.value.deptIds || element.deptIds

    if (element.mediaType && ![MediaType.Audio, MediaType.Video].includes(element.mediaType)) {
      // 有类型，并且类型不是视频与音频，需要手动设置一个时长
      if (tool.value.duration) {
        // const { min, second } = formatDuration(tool.value.duration)
        element.duration = tool.value.duration
      }
      // element.duration = tool.value.duration ? min * 60 + second : element.duration
    }
  })
}
function checkFile(file: FormAPI): boolean {
  // 检查是否存在资源信息，如果存在直接返回，证明已经存在于资源库
  const resourceInfo = file.response?.resourceInfo
  if (resourceInfo) {
    file.isExist = true
    return true
  }

  // 判断这一次的文件列表中是否存在相同文件，如果存在也标记为文件重复上传
  // 文件hash
  const fileHash = file?.hash
  const fileIndex = file.index as number
  if (fileHash) {
    // 查找当前文件数组中相同hash值的文件,但是需要先排除掉自己
    const findFile = form.resource
      ?.filter((item) => item.index !== fileIndex)
      ?.find((item) => item?.hash === fileHash)
    if (!findFile) {
      file.isExist = false
      return false
    }

    // 如果当前文件的index小于找到的文件的index，证明当前文件排名靠前，需要保留当前文件，如果大于当前文件，则证明为重复文件
    if (fileIndex < findFile.index) {
      // 第一次出现的文件，保留
      file.isExist = false
      return false
    } else {
      file.isExist = true
      return true
    }
  }
  file.isExist = false
  return false
}
/** 提交信息 */
const handleSubmit = () => {
  // 判断是否无需要提交的资源
  // if (fileUploadStatus.value.count === 0) {
  //   message.warning(t('warning.noResourcesByNow'))
  //   return
  // }

  // 改进：检查是否还有文件正在上传中
  // if (fileUploadStatus.value.uploading > 0) {
  //   message.warning(t('warning.someFilesStillUploading'))
  //   return
  // }

  // 检测是否全部文件已经上传完成（成功或失败）
  // if (fileUploadStatus.value.count !== fileUploadStatus.value.complete) {
  //   message.warning(t('warning.someFilesNotUploaded'))
  //   return
  // }

  // 检测是否有上传失败的文件
  // if (fileUploadResult.value && fileUploadResult.value.errorNum > 0) {
  //   message.warning(t('warning.resourcesUploadedIncorrectly'))
  //   return
  // }

  formRef.value?.validate(async (valid) => {
    if (valid) {
      // 改进：增加更严格的数据验证
      // const validResources = form.resource?.filter(item => {
      //   // 过滤掉已存在的资源和上传失败的资源
      //   if (item.isExist) return false
      //
      //   // 检查对应的上传文件状态
      //   const uploadFile = uploadList.value?.find(upload => upload.index === item.index)
      //   if (!uploadFile || uploadFile.viewStatus !== 'success') {
      //     console.warn(`资源 ${item.name} 上传状态异常:`, uploadFile?.viewStatus)
      //     return false
      //   }
      //
      //   // 检查必要的响应数据
      //   if (!item.response?.id || !item.response?.url) {
      //     console.warn(`资源 ${item.name} 缺少必要的响应数据:`, {
      //       response: item.response,
      //       uploadFile: uploadFile,
      //       item: item
      //     })
      //     return false
      //   }
      //
      //   return true
      // })

      // 组织接口参数 - 转换为ResourceSaveVO格式
      const params = form.resource?.map((item) => ({
        title: item.name,
        // 如果是视频类型，默认就是秒，所以不需要转换
        duration: [MediaType.Audio, MediaType.Video].includes(item.mediaType || MediaType.Video)
          ? item.duration
          : item.duration
            ? +item.duration
            : 0,
        mediaType: item.mediaType,
        fileId: item.response!.id,
        lang: Array.isArray(item.lang) ? item.lang.join(',') : `${item.lang}`,
        size: item.size,
        deptIds: Array.isArray(item.deptIds) ? item.deptIds.map((id) => Number(id)) : [],
        address: item.response?.url,
        format: item.response?.format,
        // 添加其他必要字段
        enable: '1', // 默认启用
        type: 'L', // 默认类型, 本地上传
        scope: 1, // 默认范围
        delete: false, // 默认不删除
        referenceCount: 0 // 默认引用计数
      })) as ResourceSaveVO[]
      console.log('输出文件最终')
      console.log(params)

      try {
        loading.value = true
        console.log('提交资源参数:', params)

        // 检查是否有需要保存的资源
        if (!params || params.length === 0) {
          message.warning(t('warning.noResourcesByNow'))
          return
        }

        // 调用资源保存API，传递资源数组
        await addResource(params)
        isDialogClosedBySubmit.value = true
        message.success(`${t('global.addSuccess')} (${params.length})`)
        show.value = false
        emit('handleSearch')
      } catch (error) {
        console.error('提交资源失败:', error)
        message.error(t('error.submitFailed'))
      } finally {
        loading.value = false
      }
    }
  })
}
/**
 * 删除项目
 * @param file 文件详情
 * @param index 当前项的索引
 */
const handleDeleteItem = async (file: FormAPI, index: number) => {
  try {
    // 检查是否正在上传
    const uploadItem = uploadList.value?.find((item) => item.index === file.index)
    const isUploading =
      uploadItem && (uploadItem.viewStatus === 'uploading' || uploadItem.viewStatus === 'merging')

    file.deleting = true

    // 如果正在上传，先取消上传
    if (isUploading && uploadItem?.instance) {
      uploadItem.instance.isCancel = true
    }

    // 使用原有的删除逻辑，它会处理上传组件的移除
    largeRef.value?.handleRemoveFile(file.index)
  } catch (error) {
    // 删除失败
    file.deleting = false
  }
}

/**
 * 原有的删除函数，保持兼容性
 * @param file 文件详情
 */
const handleDelete = (file: FormAPI) => {
  largeRef.value?.handleRemoveFile(file.index)
}
const handleCancel = async () => {
  if (isDialogClosedBySubmit.value) {
    isDialogClosedBySubmit.value = false
    return
  }

  if (isHasResource.value) {
    await message.confirm(t('confirm.uploading'))
    show.value = false
    isMini.value = false
  } else {
    show.value = false
    // 这里因为直接设置false会出现闪屏，
    setTimeout(() => {
      isMini.value = false
    }, 300)
  }
}

const handleToResource = () => {
  router.push('/resource/resource-list')
  isMini.value = false
  openNotification.close()
}
const resetForm = () => {
  formRef.value?.resetFields()
  toolRef.value?.resetFields()
  form.resource = undefined
}
/** 设置表单duration的值 */
function setDuration(file: CustomUploadUserFile, newValue: any[]): number {
  if (file.duration) return file.duration
  // 没有duration
  if (!form.resource) return 0
  if (newValue.length !== form.resource.length) {
    return isHasValue(file, 'duration', newValue) as number
  }
  return form.resource[file.index as number]?.duration as number
}
getDeptTreeList()
watch(
  uploadList,
  (newValue) => {
    // 只在数量变化或者第一次没有值的时候初始化，避免一直刷新表单数据
    // if (newValue?.length !== form.value?.length || !form.value) {
    nextTick(() => {
      console.log(
        'uploadList 变化:',
        newValue?.map((file) => ({
          name: file.name,
          index: file.index,
          viewStatus: file.viewStatus,
          percentage: file.percentage,
          response: file.response
        }))
      )

      form.resource = newValue?.map((file) => ({
        name: file.name,
        index: file.index!,
        lang: !form.resource
          ? ['1']
          : newValue.length !== form.resource.length
            ? (isHasValue(file, 'lang', newValue) as string[])
            : form.resource[file.index as number]?.lang || ['1'],
        deptIds: !form.resource
          ? ['']
          : newValue.length !== form.resource.length
            ? (isHasValue(file, 'deptIds', newValue) as string[])
            : form.resource[file.index as number]?.deptIds,
        duration: setDuration(file, newValue),
        size: file.size || 0,
        // 优先使用文件已有的 mediaType，如果没有则通过 formatFileType 判断
        mediaType: file.mediaType || formatFileType(file),
        response: file.response,
        hash: file?.hash
      }))

      console.log(
        'form.resource 更新:',
        form.resource?.map((item) => ({
          name: item.name,
          index: item.index,
          mediaType: item.mediaType,
          response: item.response
        }))
      )

      console.log(
        'uploadList 文件详情:',
        newValue?.map((file) => ({
          name: file.name,
          index: file.index,
          type: file.type,
          mediaType: file.mediaType,
          raw: file.raw ? { type: file.raw.type, name: file.raw.name } : null
        }))
      )
    })
    // }
  },
  {
    deep: true
  }
)
watch(show, (newValue) => {
  if (!newValue) {
    nextTick(() => {
      resetForm()
    })
  }
})
watch(fileUploadResult, (newValue) => {
  // && name !== route.name
  if (newValue && name !== route.name) {
    // 文件全部上传完毕,并且当前所处的页面也不是资源库页面，需要弹框让用户返回页面
    // 需要判断当前上传完毕后是否存在错误
    createNotication(newValue.errorNum > 0 ? 3 : 1)
  }
})

// 监听上传状态，自动删除失败的文件
watch(
  uploadList,
  (newValue, oldValue) => {
    if (newValue && oldValue) {
      newValue.forEach((item, index) => {
        if (item.viewStatus === 'fail') {
          // 上传失败，自动删除该item
          setTimeout(() => {
            if (form.resource && form.resource[index]) {
              handleDeleteItem(form.resource[index], index)
            }
          }, 1000) // 延迟1秒删除，让用户看到失败状态
        }
      })
    }
  },
  { deep: true }
)
const getMetiaType = (mediaType: number) => {
  switch (mediaType) {
    case MediaTypeEnum.VIDEO:
      return 'Video'
    case MediaTypeEnum.AUDIO:
      return 'Audio'
    case MediaTypeEnum.FILE:
      return 'File'
    case MediaTypeEnum.SCORM:
      return 'Scorm'
    case MediaTypeEnum.AICC:
      return 'AICC'
  }
}
defineExpose({
  form,
  show,
  createNotication
})
</script>

<template>
  <Dialog
    v-model="show"
    v-model:is-mini="isMini"
    width="70%"
    :destroy-on-close="true"
    @close="handleCancel"
    :title="`${t('resource.uploadIn')} ${getMetiaType(mediaType)} ${t('resource.batch')}`"
    class="!p-6"
  >
    <div v-loading="loading">
      <LargeFileUpload
        ref="largeRef"
        v-model:file-list="uploadList"
        :limit="uploadLimit"
        :multiple="true"
        :show-list="false"
      >
        <template #tip>
          <div class="text-[#abacae] flex items-center gap-1.5 mt-1">
            <Icon icon="ep:info-filled" />
            <span class="text-sm">{{ t('resource.supportResource') }}</span>
          </div>
        </template>
      </LargeFileUpload>

      <!-- 批量设置工具 :disabled="!form || form.length === 0" -->
      <el-form ref="toolRef" :model="tool" inline>
        <el-row :gutter="16" class="mt-4">
          <!-- 批量设置语言 -->
          <el-col :xs="24" :sm="12" :md="8" :lg="6">
            <el-form-item :label="t('learningCenter.course.language')" prop="lang" class="!w-full">
              <el-select v-model="tool.lang" clearable multiple class="!w-full">
                <el-option
                  v-for="item in getStrDictOptions(DICT_TYPE.RESOURCE_LIST_LANG)"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <!-- 数据权限 -->
          <el-col :xs="24" :sm="12" :md="8" :lg="6">
            <el-form-item :label="t('action.dataPermission')" prop="deptIds" class="!w-full">
              <DeptTreeSelect
                v-model="tool.deptIds"
                :data="treeOptions"
                :is-selected-dept="true"
                class="!w-full"
              />
            </el-form-item>
          </el-col>
          <!-- 时长批量设置 -->
          <el-col :xs="24" :sm="12" :md="8" :lg="5">
            <el-form-item
              :label="t('learningCenter.course.duration')"
              prop="duration"
              class="!w-full"
            >
              <DurationInput v-model="tool.duration" class="!w-full" />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="24" :lg="7">
            <el-form-item class="!w-full">
              <el-button type="primary" plain @click="handleBatchSetForm" class="!w-full">
                {{ t('action.settingInBatch') }}
              </el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <!-- 文件列表区域 -->
      <el-form ref="formRef" :model="form" inline-message class="flex flex-col gap-4">
        <el-card
          v-for="(item, index) in form.resource"
          :key="item.index"
          shadow="hover"
          class="group relative"
        >
          <div class="flex justify-between items-center gap-5">
            <!-- 如果文件不存在可以设置名称 -->
            <el-form-item
              v-if="!checkFile(item)"
              :prop="`resource[${index}].name`"
              :rules="rules.name"
              class="!w-full !mb-0"
            >
              <el-input v-model="item.name" clearable class="!w-full" />
            </el-form-item>
            <span v-else>{{ item.name }}</span>
            <!-- Hover显示的删除按钮 -->
            <el-button
              type="danger"
              :icon="Close"
              text
              circle
              @click="handleDeleteItem(item, index)"
              :loading="item.deleting"
              class="absolute top-1/2 right-2 -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 z-10"
            />
          </div>

          <!-- 正常情况下的表单 !checkFile(item) -->
          <el-row v-if="!checkFile(item)" :gutter="16" class="mt-4">
            <!-- 语言 -->
            <el-col :xs="24" :sm="12" :md="8" :lg="6">
              <el-form-item
                :label="t('learningCenter.course.language')"
                :prop="`resource[${index}].lang`"
                :rules="rules.lang"
                class="!w-full"
              >
                <el-select v-model="item.lang" clearable class="!w-full" multiple>
                  <el-option
                    v-for="dict in getStrDictOptions(DICT_TYPE.RESOURCE_LIST_LANG)"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8" :lg="6">
              <el-form-item
                :label="t('action.dataPermission')"
                :prop="`resource[${index}].deptIds`"
                :rules="rules.deptIds"
                class="!w-full"
              >
                <DeptTreeSelect
                  v-model="item.deptIds"
                  :data="treeOptions"
                  class="!w-full"
                  :is-selected-dept="true"
                  :max-tag="1"
                />
              </el-form-item>
            </el-col>
            <!-- 时长 -->
            <el-col :xs="24" :sm="12" :md="8" :lg="5">
              <el-form-item
                :prop="`resource[${index}].duration`"
                :rules="rules.duration"
                class="!w-full"
              >
                <div
                  class="flex items-center gap-1 text-[#606266] before:content-['*'] before:text-[--el-color-danger] !w-full"
                >
                  <el-icon size="14">
                    <Clock />
                  </el-icon>
                  <label class="whitespace-nowrap">
                    {{ t('learningCenter.course.duration') }}
                  </label>
                  <!-- <span>{{ formatSecond(item.duration || 0) }}</span> -->
                  <span
                    v-if="[MediaType.Audio, MediaType.Video].includes(item.mediaType!)"
                    class="whitespace-nowrap"
                    >{{ formatSecond(+(item.duration || 0)) }}</span
                  >
                  <DurationInput v-else v-model="item.duration" class="flex-1 min-w-0" />
                </div>
              </el-form-item>
            </el-col>
            <!-- 体积 -->
            <el-col :xs="12" :sm="6" :md="4" :lg="3">
              <el-form-item class="!w-full">
                <div class="flex items-center gap-1 text-[#606266] !w-full">
                  <svg-icon icon-class="cube" class="text-sm flex-shrink-0" />
                  <span class="whitespace-nowrap">
                    {{ t('common.size') }}
                  </span>
                  <span class="whitespace-nowrap">{{ formatBytes(item.size) }}</span>
                </div>
              </el-form-item>
            </el-col>
            <!-- 格式 -->
            <el-col :xs="12" :sm="6" :md="4" :lg="4">
              <el-form-item class="!w-full">
                <div class="flex items-center gap-1 text-[#606266] !w-full">
                  <svg-icon icon-class="format" class="text-sm flex-shrink-0" />
                  <span class="whitespace-nowrap">
                    {{ t('common.format') }}
                  </span>
                  <dict-tag
                    :type="DICT_TYPE.RESOURCE_LIST_TYPE"
                    :value="item.mediaType"
                    class="flex-shrink-0"
                  />
                </div>
              </el-form-item>
            </el-col>
            <!-- 进度条，只在上传中且未完成时显示 -->
            <el-col
              v-if="
                uploadList![item.index] &&
                (uploadList![item.index]?.percentage || 0) > 0 &&
                (uploadList![item.index]?.percentage || 0) < 100 &&
                uploadList![item.index]?.viewStatus !== 'success'
              "
              :span="24"
            >
              <el-progress
                :percentage="uploadList![item.index]?.percentage"
                :color="customColor"
                class="flex-1"
              />
            </el-col>
          </el-row>
          <!-- 如果已经存在当前文件，只展示文件信息即可 -->
          <div v-else class="mt-5 text-[#E2A03B]">
            {{ t('common.noExist') }}
          </div>
        </el-card>
      </el-form>
      <el-empty v-if="!form.resource || form.resource?.length === 0" />
    </div>
    <template #footer>
      <el-button @click="handleCancel">
        {{ t('action.cancel') }}
      </el-button>

      <el-button type="primary" @click="handleSubmit">
        {{ t('action.submit') }}
      </el-button>
    </template>
  </Dialog>
</template>
