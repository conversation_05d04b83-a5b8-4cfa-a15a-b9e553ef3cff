<script setup lang='ts'>
import { formatSecond } from '@/utils/ruoyi'
import { useI18n } from '@/hooks/web/useI18n'

const { t } = useI18n()
const model = defineModel<number | undefined>()
const inputValue = ref<Date | null>(null)

// 设置默认时间为1:00:00
const defaultTime = (() => {
  const today = new Date()
  today.setHours(1, 0, 0, 0) // 设置为01:00:00
  return today
})()

const secondsToDate = (seconds: number): Date => {
  // 使用当天的日期，避免时区问题
  const today = new Date()
  today.setHours(0, 0, 0, 0) // 设置为当天的00:00:00

  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = seconds % 60

  today.setHours(hours, minutes, secs, 0)
  return today
}

const timeToSeconds = (date: Date): number => {
  const h = date.getHours()
  const m = date.getMinutes()
  const s = date.getSeconds()
  return h * 3600 + m * 60 + s
}

const handleChange = (value: Date | null) => {
  if (value) {
    model.value = timeToSeconds(value)
  } else {
    model.value = undefined
  }
}

watch(model, (newValue) => {
  if (newValue !== undefined && newValue !== null) {
    inputValue.value = secondsToDate(newValue)
  } else {
    inputValue.value = null
  }
}, {
  immediate: true,
})
</script>

<template>
  <el-time-picker
    v-model="inputValue"
    class="!w-full"
    @change="handleChange"
    clearable
    :placeholder="t('common.selectText')"
    :default-value="defaultTime"
  />
</template>

<style scoped lang='scss'>
:deep(.el-input-group__append){
  @apply px-3;
}
</style>
