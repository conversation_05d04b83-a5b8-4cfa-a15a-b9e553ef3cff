<template>
  <el-drawer :title="dialogTitle" v-model="dialogVisible" size="700">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="170px"
      v-loading="formLoading"
    >
      <el-form-item :label="t('academy.course.courseTitle')" prop="title">
        <el-input
          v-model="formData.title"
          :placeholder="t('academy.course.courseTitlePlaceholder')"
        />
      </el-form-item>
      <el-form-item :label="t('academy.course.shortTitle')" prop="shortTitle">
        <el-input
          v-model="formData.shortTitle"
          :placeholder="t('academy.course.shortTitlePlaceholder')"
        />
      </el-form-item>
      <el-form-item :label="t('academy.class.trainingDays')" prop="trainingDays">
        <div class="flex items-center">
          <el-input-number v-model="formData.trainingDays" :min="0" :max="999" :controls="false" />
          <span class="ms-3">{{ t('academy.internal.days') }}</span>
        </div>
      </el-form-item>
      <!--      展示去除，先隐藏-->
      <!--      <el-form-item label="Course Code" prop="code">-->
      <!--        <el-input v-model="formData.code" placeholder="Please input course code" disabled />-->
      <!--      </el-form-item>-->
      <el-form-item :label="t('academy.course.category')" prop="categoryId">
        <div class="flex">
          <el-tree-select
            v-model="formData.categoryId"
            :data="categoryList"
            :props="defaultProps"
            filterable
            clearable
            check-strictly
            default-expand-all
            :placeholder="t('academy.course.pleaseSelect')"
            class="!w-240px"
            value-key="categoryId"
          />
          <el-link type="primary" :underline="false" @click="openCategory">
            <Icon icon="ep:plus" class="me-3 ms-3" />{{ t('academy.course.addCategory') }}
          </el-link>
        </div>
      </el-form-item>
      <el-form-item :label="t('academy.course.cover')">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-radio-group v-model="isCover" @change="changeCover">
              <el-radio :label="1">{{ t('academy.course.default') }}</el-radio>
              <el-radio :label="2">{{ t('academy.course.customize') }}</el-radio>
            </el-radio-group>
          </el-col>
          <el-col :span="12">
            <ImageUpload
              v-if="isCover === 2"
              v-model="formData.cover"
              class="mt-2.5"
              :limit="1"
              :is-show-tip="true"
              :file-size="500"
              :tip-text="t('category.topic.coverPH')"
              :upload-module="1"
            />
            <el-image
              v-if="isCover === 1"
              :src="defaultImage"
              alt=""
              class="w-[150px] h-[150px] mt-2.5"
            />
          </el-col>
        </el-row>
      </el-form-item>
      <!--      <el-form-item label="考试id(当模版用)" prop="examId">-->
      <!--        <el-input v-model="formData.examId" placeholder="请输入考试id(当模版用)" />-->
      <!--      </el-form-item>-->
      <el-form-item :label="t('academy.course.language')" prop="languageList">
        <el-checkbox-group v-model="formData.languageList">
          <el-checkbox
            v-for="dict in getIntDictOptions(DICT_TYPE.TRAINING_COURSE_LANGUAGE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-checkbox-group>
      </el-form-item>
      <el-form-item :label="t('academy.course.trainerType')" prop="trainerType">
        <el-select v-model="formData.trainerType" :placeholder="t('academy.course.pleaseSelect')">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.TRAINER_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item prop="keywords" :label="t('category.topic.keyWords')">
        <div class="flex">
          <div class="mt-2">
            <div class="flex flex-wrap gap-2 w-full">
              <el-tag
                v-for="tag in formData.keywords"
                :key="tag"
                closable
                :disable-transitions="false"
                @close="handleCloseTag(tag)"
              >
                {{ tag }}
              </el-tag>
              <el-input
                v-if="inputKeyVisible"
                ref="InputKeyRef"
                v-model="inputKeyValue"
                class="!w-60"
                size="small"
                maxlength="300"
                show-word-limit
                @keyup.enter="handleKeyInputConfirm"
                @blur="handleKeyInputConfirm"
              />
              <el-button
                v-else-if="!inputKeyVisible && formData.keywords && formData.keywords.length < 5"
                class="button-new-tag"
                size="small"
                @click="showKeyInput"
              >
                {{ t('action.addKeyWord') }}
              </el-button>
            </div>
            <div v-if="repeatKeyword" class="text-[#f56c6c] text-xs my-2">
              {{ t('common.keyWords') }}
            </div>
            <div class="text-[#ABACAE] text-xs my-2">
              {{ t('common.keyWordsLength') }}
            </div>
          </div>
          <!--          目前需求先让注释当前Ai生成关键字功能-->
          <!--          <div>-->
          <!--            <el-button-->
          <!--              link-->
          <!--              type="primary"-->
          <!--              @click="changeAiGenerate"-->
          <!--            >-->
          <!--              AI Generate-->
          <!--            </el-button>-->
          <!--          </div>-->
        </div>
      </el-form-item>
      <el-form-item prop="level" :label="t('academy.course.level')">
        <el-select
          v-model="formData.level"
          clearable
          filterable
          :placeholder="t('academy.course.pleaseSelect')"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.COURSE_LEVEL)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="t('academy.course.ifmsApproval')" prop="ifmsApprovalKey">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-switch
              v-model="ifmsApproval"
              size="large"
              :active-value="1"
              :inactive-value="0"
              @change="changeIfmsApproval"
            />
          </el-col>
          <el-col :span="24" v-if="ifmsApproval === 1">
            <el-radio-group v-model="formData.ifmsApprovalKey" @change="changeIfmsApprovalKey">
              <el-radio
                v-for="(item, index) in ifmsApprovalList"
                :key="index"
                :label="item.name"
                :value="item.key"
              />
            </el-radio-group>
          </el-col>
          <el-col :span="24" v-show="ifmsApprovalKeyId">
            <div class="h-[200px] overflow-y-auto">
              <Approval :model-id="ifmsApprovalKeyId" />
            </div>
          </el-col>
        </el-row>
      </el-form-item>
      <el-form-item :label="t('academy.course.contractorApproval')" prop="contractorApprovalKey">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-switch
              v-model="contractorApproval"
              size="large"
              :active-value="1"
              :inactive-value="0"
              @change="changeContractorApproval"
            />
          </el-col>
          <el-col :span="24" v-show="contractorApproval === 1">
            <el-radio-group
              v-model="formData.contractorApprovalKey"
              @change="changeContractorApprovalKey"
            >
              <el-radio
                v-for="(item, index) in contractorApprovalList"
                :key="index"
                :label="item.name"
                :value="item.key"
              />
            </el-radio-group>
          </el-col>
          <el-col :span="24" v-show="contractorApprovalKeyId">
            <div class="h-[200px] overflow-y-auto">
              <Approval :model-id="contractorApprovalKeyId" />
            </div>
          </el-col>
        </el-row>
      </el-form-item>
      <el-form-item :label="t('academy.course.validity')" prop="validity">
        <div class="flex">
          <el-input-number v-model="formData.validity" :min="0" :max="999" :controls="false" />
          <span class="ms-3">{{ t('academy.course.monthsUnit') }}</span>
          <span class="ms-3 text-[#BBBBBB]">{{ t('academy.course.noRestrictions') }}</span>
        </div>
      </el-form-item>
      <el-form-item :label="t('academy.course.certificate')" prop="certificateId">
        <el-input
          v-model="checkCertificateInfo.name"
          :placeholder="t('academy.course.pleaseSelectCertificate')"
          disabled
        >
          <template #suffix>
            <Icon icon="ep:more" class="mr-5px cursor-pointer" @click="openCertificate" />
          </template>
        </el-input>
      </el-form-item>
      <el-form-item :label="t('academy.course.exam')">
        <el-input
          v-model="checkExamInfo.name"
          :placeholder="t('academy.course.pleaseSelectExam')"
          disabled
        >
          <template #suffix>
            <Icon icon="ep:more" class="mr-5px cursor-pointer" @click="openExam" />
          </template>
        </el-input>
      </el-form-item>
      <el-form-item :label="t('academy.course.prerequisite')">
        <div class="flex flex-col">
          <div>
            {{ t('academy.course.course') }}
          </div>
          <el-input :placeholder="t('academy.course.pleaseSelectCourse')" disabled>
            <template #suffix>
              <Icon icon="ep:more" class="mr-5px cursor-pointer" @click="openCourse" />
            </template>
          </el-input>
          <div class="break-words">
            <el-tag
              v-for="tag in checkPrerequisteCourseInfo"
              :key="tag"
              closable
              class="me-2 mb-2"
              :disable-transitions="false"
              @close="handleClose(tag.id)"
            >
              {{ tag.title }}
            </el-tag>
          </div>
          <div>
            {{ t('academy.course.attachment') }}
          </div>
          <div class="break-words">
            <el-tag
              v-for="tag in formData.prerequisteAttachment"
              :key="tag"
              closable
              class="ms-2 mb-2"
              :disable-transitions="false"
              @close="handleAttmentClose(tag)"
            >
              {{ tag }}
            </el-tag>
            <el-input
              v-if="inputVisible"
              ref="InputRef"
              v-model="inputValue"
              class="w-10"
              size="small"
              @keyup.enter="handleInputConfirm"
              @blur="handleInputConfirm"
            />
            <el-button v-else class="button-new-tag mb-2 ms-2" size="small" @click="showInput">
              + {{ t('academy.course.new') }}
            </el-button>
          </div>
        </div>
      </el-form-item>
      <el-form-item :label="t('academy.course.restriction')" :prop="restrictionProp">
        <div class="flex">
          <span class="me-1">{{ t('academy.course.absent') }}</span>
          <el-input-number v-model="formData.absentTime" :min="0" :max="999" :controls="false" />
          <span class="ms-1"
            >{{ t('academy.course.absentTime') }}{{ t('academy.course.unableToSubscribe') }}</span
          >
          <el-input-number v-model="formData.freezeTime" :min="0" :max="999" :controls="false" />
          <span class="ms-1">{{ t('academy.course.months') }}</span>
        </div>
      </el-form-item>
      <el-form-item :label="t('academy.course.feedback')">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-switch
              v-model="formData.feedbackStatus"
              size="large"
              :active-value="1"
              :inactive-value="0"
            />
          </el-col>
          <el-col :span="24" v-show="formData.feedbackStatus === 1">
            <el-button
              type="primary"
              plain
              size="small"
              v-for="dict in getIntDictOptions(DICT_TYPE.TRAINING_COURSE_TEMPLATE)"
              :key="dict.value"
              @click="imagePreview(dict.value)"
            >
              {{ dict.label }}
            </el-button>
          </el-col>
        </el-row>
      </el-form-item>
      <el-form-item :label="t('academy.course.bookingTimeSetting')" prop="bookingTime">
        <el-switch
          v-model="formData.bookingTime"
          size="large"
          :active-value="1"
          :inactive-value="0"
        />
        <span class="ms-3 text-[#BBBBBB]">{{ t('academy.course.bookingTimeDesc') }}</span>
      </el-form-item>
      <el-form-item :label="t('academy.course.description')">
        <el-input v-model="formData.remarks" type="textarea" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="dialogVisible = false">{{ t('academy.course.cancel') }}</el-button>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">{{
        t('academy.course.confirm')
      }}</el-button>
    </template>
  </el-drawer>

  <!--  选择人员-->
  <Dialog v-model="show" :width="1050" :title="t('academy.course.selectStudents')">
    <ScopeSelect
      ref="scopeRef"
      v-model="scopeList"
      v-loading="loading"
      :employee-table-props="{
        height: 420
      }"
      @confirm="handleConfirm"
      @delete="handleDelete"
    />
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="show = false">{{ t('academy.course.cancel') }}</el-button>
        <el-button type="primary" @click="confirmStudent">{{
          t('academy.course.confirm')
        }}</el-button>
      </div>
    </template>
  </Dialog>

  <!--  选择考试-->
  <ExamSelect ref="examRef" @confirm="examConfirm" />
  <!--  选择证书-->
  <CertificateSelect ref="certificateRef" @confirm="certificateConfirm" />
  <!--  新增分类-->
  <Category ref="categoryRef" @success="categoryConfirm" />

  <PrerequisiteCourseSelect ref="prerequisiteCourseRef" @confirm="prerequisiteCourseConfirm" />

  <!--  预览反馈模板-->
  <Dialog v-model="showTemplate" :width="500" :title="t('academy.course.template')">
    <el-image :src="templateSrc" />
  </Dialog>
</template>
<script setup lang="ts">
import ImageUpload from '@/components/ImageUpload/index.vue'
import { CourseApi, CourseRespVO, CourseSaveVO } from '@/api/academy/course'
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { deptTreeSelect } from '@/api/system/user'
import { NodeType } from '@/components/ScopeSelect/enums/NodeType'
import { ScopeConfirm, ScopeData } from '@/components/ScopeSelect/typings'
import ScopeSelect from '@/components/ScopeSelect/index.vue'
import ExamSelect from './components/ExamSelect.vue'
import PrerequisiteCourseSelect from './components/PrerequisiteCourseSelect.vue'
import CertificateSelect from './components/CertificateSelect.vue'
import Category from './components/Category.vue'
import { ExamRespVO, getExam } from '@/api/topicMgt/exam'
import enTemplate from '@/assets/images/academyCourse/enTemplate.png'
import arTemplate from '@/assets/images/academyCourse/arTemplate.png'
import zhTemplate from '@/assets/images/academyCourse/zhTemplate.png'
import defaultImage from '@/assets/images/academyCourse/courseDefaultImage.png'
import { listTopic } from '@/api/category/training'
import { defaultProps, handleTree } from '@/utils/tree'
import Approval from './components/Approval.vue'
import { CourseScopeVO } from '@/api/topicMgt/elearning'
import * as ModelApi from '@/api/bpm/model'
import { CertificateApi, CertificateRespVO } from '@/api/system/certification/certificate'
/** 学院课程信息 表单 */
defineOptions({ name: 'CourseForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  title: undefined,
  shortTitle: undefined,
  trainingDays: undefined,
  // code: undefined,
  categoryId: undefined,
  examId: undefined,
  certificateId: undefined,
  cover: undefined,
  languageList: [],
  trainerType: undefined,
  ifmsApprovalKey: '',
  contractorApprovalKey: '',
  validity: undefined,
  feedbackStatus: 0,
  feedbackLanguage: 1,
  prerequisteCourse: '',
  prerequisteAttachment: [],
  notification: 0, // 默认给0 不发通知
  remarks: undefined,
  absentTime: undefined,
  freezeTime: undefined,
  bookingTime: 1,
  level: 0,
  keywords: []
})
const formRules = reactive({
  title: [{ required: true, message: t('academy.course.courseNameRequired'), trigger: 'blur' }],
  shortTitle: [
    { required: true, message: t('academy.course.shortTitleRequired'), trigger: 'blur' }
  ],
  trainingDays: [
    { required: true, message: t('academy.course.trainingDaysRequired'), trigger: 'blur' }
  ],
  code: [{ required: true, message: t('academy.course.courseCodeRequired'), trigger: 'blur' }],
  categoryId: [
    { required: true, message: t('academy.course.categoryRequired'), trigger: 'change' }
  ],
  cover: [{ required: true, message: t('academy.course.coverRequired'), trigger: 'change' }],
  languageList: [
    { required: true, message: t('academy.course.languageRequired'), trigger: 'change' }
  ],
  trainerType: [
    { required: true, message: t('academy.course.trainerTypeRequired'), trigger: 'change' }
  ],
  ifmsApprovalKey: [
    { required: true, message: t('academy.course.approvalSettingRequired'), trigger: 'change' }
  ],
  contractorApprovalKey: [
    { required: true, message: t('academy.course.approvalSettingRequired'), trigger: 'change' }
  ],
  validity: [{ required: true, message: t('academy.course.validityRequired'), trigger: 'blur' }],
  certificateId: [
    { required: true, message: t('academy.course.certificateRequired'), trigger: 'change' }
  ],
  bookingTime: [
    { required: true, message: t('academy.course.bookingTimeRequired'), trigger: 'change' }
  ],
  restriction: [
    { required: true, message: t('academy.course.restrictionRequired'), trigger: 'blur' }
  ]
})
const formRef = ref() // 表单 Ref
const scopeRef = ref()
const examRef = ref()
const categoryRef = ref()
const certificateRef = ref()
const prerequisiteCourseRef = ref()
const show = ref(false)
const showTemplate = ref(false)
const templateSrc = ref()
const companyList = ref([]) // 部门信息
const scopeList = ref<ScopeData[]>([])
const checkScopeList = ref([]) // 选择后的scope数据
const isCover = ref(1)
const categoryList = ref([]) // 课程分类信息
const ifmsApproval = ref()
const contractorApproval = ref()
const modelList = ref([]) // 流程模型信息
const ifmsApprovalList = ref([])
const contractorApprovalList = ref([])
const ifmsApprovalKeyId = ref('')
const contractorApprovalKeyId = ref()
const inputValue = ref('')
const attachmentTags = ref([])
const inputVisible = ref(false)
const InputRef = ref()
// keywords定义
const repeatKeyword = ref(false)
const inputKeyValue = ref('')
const inputKeyVisible = ref()
const InputKeyRef = ref()

const showInput = () => {
  inputVisible.value = true
  nextTick(() => {
    InputRef.value!.input!.focus()
  })
}

// 附件
const handleInputConfirm = () => {
  if (inputValue.value) {
    formData.value.prerequisteAttachment.push(inputValue.value)
  }
  inputVisible.value = false
  inputValue.value = ''
}
const handleAttmentClose = (tag: string) => {
  formData.value.prerequisteAttachment.splice(formData.value.prerequisteAttachment.indexOf(tag), 1)
}

// 关于keywords操作
const handleCloseTag = (tag: string) => {
  formData.value.keywords.splice(formData.value.keywords.indexOf(tag), 1)
  if (formData.value.keywords.includes(inputKeyValue.value)) {
    repeatKeyword.value = true
  } else {
    repeatKeyword.value = false
  }
}
const showKeyInput = () => {
  inputKeyVisible.value = true
  nextTick(() => {
    InputKeyRef.value!.input!.focus()
  })
}
const handleKeyInputConfirm = () => {
  if (formData.value.keywords.includes(inputKeyValue.value)) {
    repeatKeyword.value = true
    return
  }
  repeatKeyword.value = false
  if (inputKeyValue.value) {
    formData.value.keywords.push(inputKeyValue.value)
  }
  inputKeyVisible.value = false
  inputKeyValue.value = ''
}

// Todo 待补充ai接口生成关键词
const changeAiGenerate = () => {
  // formData.value.keywords.push(123)
}

/**  已选择的考试信息 */
const checkExamInfo = ref({
  name: '',
  id: ''
})
/**  已选择的证书信息 */
const checkCertificateInfo = ref({
  name: '',
  id: ''
})
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  courseIds: []
})
/**  已选择的prerequiste course信息 */
const checkPrerequisteCourseInfo = ref([])
const changeIfmsApproval = (val: number | string) => {
  if (val === 0) {
    formData.value.ifmsApprovalKey = ''
    ifmsApprovalKeyId.value = undefined
  }
}
const changeIfmsApprovalKey = (val: number | string) => {
  ifmsApprovalList.value.forEach((item: any) => {
    if (item.key === val) {
      ifmsApprovalKeyId.value = item.key
    }
  })
}

const changeContractorApproval = (val: number | string) => {
  if (val === 0) {
    formData.value.contractorApprovalKey = ''
    contractorApprovalKeyId.value = undefined
  }
}

const changeContractorApprovalKey = (val: number | string) => {
  contractorApprovalList.value.forEach((item: any) => {
    if (item.key === val) {
      contractorApprovalKeyId.value = item.key
    }
  })
}
const restrictionProp = computed(() => {
  // return (!formData.value.absentTime || !formData.value.freezeTime) ? 'restriction' : ''
})

// 分类列表
const getCategory = async () => {
  const data = await listTopic()
  categoryList.value = handleTree(data)
}

/** 切换清空自定义上传封面 */
const changeCover = (val: number | string) => {
  if (val === 1) {
    formData.value.cover = ''
  }
}

/**  打开分类弹出框 */
const openCategory = () => {
  categoryRef.value.open()
}

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  getCategory()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await CourseApi.getCourse(id)
      // 特殊处理keywords参数
      const keywords = formData.value?.keywords
        ? (formData.value?.keywords as string).split(',')
        : undefined
      formData.value.keywords = keywords
      const prerequisteAttachment = formData.value?.prerequisteAttachment
        ? (formData.value?.prerequisteAttachment as string).split(',')
        : undefined
      formData.value.prerequisteAttachment = prerequisteAttachment
      if (!formData.value.keywords) {
        formData.value.keywords = []
      }
      if (!formData.value.prerequisteAttachment) {
        formData.value.prerequisteAttachment = []
      }
      // 为了下面展示相对应的审批信息
      ifmsApproval.value = formData.value?.ifmsApprovalKey ? 1 : 0
      contractorApproval.value = formData.value?.contractorApprovalKey ? 1 : 0
      // 流程数据回显 根据id去匹配标识
      changeIfmsApprovalKey(formData.value?.ifmsApprovalKey)
      changeContractorApprovalKey(formData.value?.contractorApprovalKey)
      if (formData.value?.certificateId) {
        await getCertificateName(formData.value?.certificateId)
      }
      if (formData.value?.examId) {
        await getExamName(formData.value?.examId)
      }
      isCover.value = formData.value.cover ? 2 : 1
      // 只有当上传了附件课程才允许调用课程信息列表接口取得课程信息,如果不判断则直接会把课程信息列表接口第一页的数据展示出来
      if (formData.value?.prerequisteCourse) {
        queryParams.courseIds = formData.value?.prerequisteCourse
        getCourseName()
      }
    } finally {
      formLoading.value = false
    }
  } else {
    // 新增时获取课程编号 暂时注释,界面code内容已经隐藏了
    // getCourseCode()
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

// 获取附件课程名称
const getCourseName = async () => {
  const data = await CourseApi.getCoursePage(queryParams)
  checkPrerequisteCourseInfo.value = data.list
}
/** 获取课程编号  暂时不需要*/
const getCourseCode = async () => {
  // const data = await CourseApi.getCourseCode()
  // formData.value.code = data
}
/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    // 特殊处理keywords参数
    const keywords = formData.value.keywords
      ? (formData.value.keywords as string[]).join(',')
      : undefined
    const prerequisteAttachment = formData.value.prerequisteAttachment
      ? (formData.value.prerequisteAttachment as string[]).join(',')
      : undefined
    // keywords有重复的，提交时，去掉重复值
    if (repeatKeyword.value) {
      inputKeyVisible.value = false
      repeatKeyword.value = false
      inputKeyValue.value = ''
    }
    const data: CourseSaveVO = {
      ...formData.value,
      keywords,
      prerequisteAttachment
    }
    if (formType.value === 'create') {
      await CourseApi.createCourse(data)
      message.success(t('common.createSuccess'))
    } else {
      await CourseApi.updateCourse(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    title: '',
    shortTitle: undefined,
    trainingDays: undefined,
    code: undefined,
    categoryId: undefined,
    examId: undefined,
    cover: undefined,
    language: [],
    trainerType: undefined,
    ifmsApprovalKey: '',
    contractorApprovalKey: '',
    validity: undefined,
    feedbackStatus: 0,
    feedbackLanguage: 1,
    prerequisteCourse: '',
    prerequisteAttachment: [],
    absent: undefined,
    freezeTime: undefined,
    certificateId: undefined,
    notification: 0,
    remarks: undefined,
    keywords: [],
    level: 0,
    bookingTime: 1
  }
  // 重置一遍数据
  ifmsApproval.value = undefined
  contractorApproval.value = undefined
  ifmsApprovalKeyId.value = ''
  contractorApprovalKeyId.value = ''
  checkCertificateInfo.value.name = ''
  checkExamInfo.value.name = ''
  checkPrerequisteCourseInfo.value = []
  attachmentTags.value = []
  // 重置数据
  inputKeyVisible.value = false
  repeatKeyword.value = false
  inputKeyValue.value = ''
  formRef.value?.resetFields()
}

// 选择考试
const openExam = () => {
  examRef.value.open(formData.value.examId)
}
// 获取选择的考试信息
const examConfirm = (data: ExamRespVO) => {
  checkExamInfo.value = data
  formData.value.examId = data.id
}

// 获取选择的证书信息
const certificateConfirm = (data: CertificateRespVO) => {
  checkCertificateInfo.value = data
  formData.value.certificateId = data.id
}

const categoryConfirm = () => {
  // 清空当前分类
  formData.categoryId = undefined
  // 重新获取分类数据
  getCategory()
}

// 选择证书
const openCertificate = () => {
  certificateRef.value.open(formData.value.certificateId)
}

// 通过证书id获取证书名称 (用于输入框内展示)
const getCertificateName = async (id: number) => {
  const data = await CertificateApi.getCertificate(id)
  checkCertificateInfo.value.name = data.name
}
// 通过考试id获取考试名称 (用于输入框内展示)
const getExamName = async (id: number) => {
  const data = await getExam(id)
  checkExamInfo.value.name = data.name
}

const openCourse = () => {
  const ids = checkPrerequisteCourseInfo.value?.map((item) => item.id)
  prerequisiteCourseRef.value.open(ids)
}

// 确定选择的附件课程
const prerequisiteCourseConfirm = (data: CourseRespVO) => {
  checkPrerequisteCourseInfo.value = data
  formData.value.prerequisteCourse = data.map((item) => item.id).join(',')
}

// 删除课程
const handleClose = (id: number) => {
  checkPrerequisteCourseInfo.value = checkPrerequisteCourseInfo.value.filter(
    (item) => item.id !== id
  )
  formData.value.prerequisteCourse = checkPrerequisteCourseInfo.value
    .map((item) => item.id)
    .join(',')
}

// 预览模板
const imagePreview = (type: number) => {
  // 预览哪一个最终确定是哪个模板
  formData.value.feedbackLanguage = type
  switch (type) {
    case 1:
      templateSrc.value = enTemplate
      break
    case 2:
      templateSrc.value = arTemplate
      break
    case 3:
      templateSrc.value = zhTemplate
      break
    default:
      break
  }
  showTemplate.value = true
}

const getModel = async () => {
  modelList.value = await ModelApi.getModelList({})
  if (modelList.value) {
    ifmsApprovalList.value = modelList.value.filter(
      (item) => item.categoryName && item.categoryName.toLowerCase().includes('ifms')
    )
    contractorApprovalList.value = modelList.value.filter(
      (item) => item.categoryName && item.categoryName.toLowerCase().includes('contractor')
    )
  }
}
onMounted(() => {
  // 流程信息
  getModel()
})
</script>
<style scoped lang="scss">
:deep(.el-input-number) {
  width: 50px;
}
:deep(.el-input-number.is-without-controls .el-input__wrapper) {
  padding-left: 11px;
  padding-right: 11px;
}
</style>
