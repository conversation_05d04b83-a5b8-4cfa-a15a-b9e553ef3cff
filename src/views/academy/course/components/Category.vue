<template>
  <Dialog v-model="showCategory" :title="t('academy.course.newCategory')">
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="formData"
      ref="formRef"
      :rules="formRules"
      :inline="true"
      label-width="120x"
    >
      <el-form-item :label="t('academy.course.categoryName')" prop="name">
        <el-input
          v-model="formData.name"
          :placeholder="t('academy.course.categoryNamePlaceholder')"
          clearable
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item :label="t('academy.course.categoryParent')" prop="parentId">
        <el-tree-select
          v-model="formData.parentId"
          :data="categoryList"
          :props="defaultProps"
          filterable
          clearable
          check-strictly
          default-expand-all
          value-key="id"
          :placeholder="t('academy.course.selectParentCategory')"
          class="!w-240px"
          @change="handleCategory"
        />
      </el-form-item>
      <!-- <el-form-item :label="t('academy.course.status')" prop="type">
        <el-radio-group v-model="formData.type">
          <el-radio :key="otherCategory.value" :label="otherCategory.label" :value="otherCategory.value" />
          <el-radio
            v-for="dict in getIntDictOptions(DICT_TYPE.TRAINING_CATEGORY_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
      </el-radio-group>
    </el-form-item> -->
  </el-form>
  <template #footer>
      <div class="dialog-footer">
        <el-button @click="showCategory = false">{{ t('academy.course.cancel') }}</el-button>
        <el-button type="primary" :loading="formLoading" @click="confirmCategory">{{ t('academy.course.confirm') }}</el-button>
      </div>
    </template>

  </Dialog>

</template>

<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import {addTopic, listTopic, TopicDdtEnum} from "@/api/category/training"
import { handleTree,defaultProps } from "@/utils/tree"
import { descriptionProps } from 'element-plus'
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const formLoading = ref(false) // 列表的加载中
// const otherCategory = ref({
//   dictType: 'other_category',
//   value: 2,
//   label: 'Other Category',
//   labelCn: '其他类别',
//   labelEn: 'Other Category',
//   cssClass: 'other-category',
//   colorType: '',
// })
const formData = ref({
  name: '' as string,
  parentId: undefined as number | undefined,
  level: 1,
  sort: 0,
  type: 3,
  description: '' as string | undefined
})
const formRules = reactive({
  name: [{ required: true, message: t('category.topic.subjectNameRule'), trigger: 'blur' }],
  parentId: [{ required: true, message: t('academy.course.parentCategoryRequired'), trigger: 'change' }],
  type: [{ required: true, message: t('sys.user.statusRule'), trigger: 'change' }],
})
const formRef = ref()
const showCategory = ref(false)
const categoryList = ref([]) // 分类信息
const reCategoryList = ref([])
/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
/** 查询分类 */
const getCategory = async () => {
  const data = await listTopic()
  categoryList.value = handleTree(data)
  reCategoryList.value = data
}

const getRootNode = async (item) => {
  if (item.parentId) {
    const parent = reCategoryList.value.find((i: any) => i.id === item.parentId)
    if (parent) {
      return getRootNode(parent)
    }
  }
  return item
}

// 通过选择父分类id获取详情信息
const handleCategory = (id: number) => {
  // 通过id获取当前详情信息
  const item = reCategoryList.value.find((item: any) => item.id === id)
  formData.value.level = item.level + 1
  getRootNode(item).then((res) => {
    console.log('res', res)
    if (res.name === 'DDT') {
      formData.value.type = 1;
    } else if (res.name === 'HSE') {
      formData.value.type = 2;
    } else {
      formData.value.type = 3;
    }
    
  })
}

/** 重置按钮操作 */
const resetQuery = () => {
  formData.value.name = ''
  formData.value.parentId = undefined
  formData.value.level = 1
  formData.value.sort = 0
  formData.value.type = 3
  formData.value.description = ''
}

/** 打开弹窗 */
const open = async () => {
  showCategory.value = true
  resetQuery()
  // 获取分类信息
  await getCategory()
}
const confirmCategory = async () => {
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    if (!formData.value.name) {
      throw new Error(t('category.topic.subjectNameRule'))
    }
    await addTopic(formData.value)
    message.success(t('common.createSuccess'))
    // 发送操作成功的事件
    emit('success')
    showCategory.value = false
  } finally {
    formLoading.value = false
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗
</script>
