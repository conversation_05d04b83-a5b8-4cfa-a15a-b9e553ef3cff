<template>
  <Dialog v-model="showCourse" align-center :width="1050" :title="t('academy.course.selectPrerequisitesCourse')">
    <ContentWrap>
      <!-- 搜索工作栏 -->
      <el-form
        class="-mb-15px"
        :model="queryParams"
        ref="queryFormRef"
        :inline="true"
        label-width="68px"
      >
        <el-form-item :label="t('academy.course.category')" prop="categoryId">
          <el-tree-select
            v-model="queryParams.categoryId"
            :data="categoryList"
            :props="defaultProps"
            filterable
            clearable
            check-strictly
            default-expand-all
            value-key="id"
            :placeholder="t('academy.course.pleaseSelect')"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item :label="t('academy.course.language')" prop="language">
          <el-select
            v-model="queryParams.language"
            :placeholder="t('academy.course.pleaseSelect')"
            clearable
            class="!w-240px"
          >
            <el-option
              v-for="dict in getIntDictOptions(DICT_TYPE.TRAINING_COURSE_LANGUAGE)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="t('academy.course.type')" prop="trainerType">
          <el-select
            v-model="queryParams.trainerType"
            :placeholder="t('academy.course.pleaseSelect')"
            clearable
            class="!w-240px"
          >
            <el-option
              v-for="dict in getIntDictOptions(DICT_TYPE.TRAINER_TYPE)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="t('academy.course.courseTitle')" prop="title">
          <el-input
            v-model="queryParams.title"
            :placeholder="t('academy.course.courseTitlePlaceholder')"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item>
          <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" />{{ t('academy.course.search') }}</el-button>
          <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" />{{ t('academy.course.reset') }}</el-button>
        </el-form-item>
      </el-form>
    </ContentWrap>

    <!-- 列表 -->
    <ContentWrap>
      <el-table ref="RefSingleTable" v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true" row-key="id" @selection-change="handleSelectionChange">
        <el-table-column type="selection" :reserve-selection="true" width="55" align="center" />
        <el-table-column :label="t('academy.course.courseTitle')" align="center" prop="title" min-width="180px" />
        <el-table-column :label="t('academy.course.courseCode')" align="center" prop="code" min-width="180px">
          <template #default="scope">
            MJN-{{ getCode(scope.row.categoryFullPath) }}-{{ scope.row.code }}
          </template>
        </el-table-column>
        <el-table-column :label="t('academy.course.category')" align="center" prop="categoryFullPath" min-width="180px" />
        <el-table-column :label="t('academy.course.language')" align="center" prop="language" min-width="180px">
          <template #default="scope">
            <el-tag v-for="item in scope.row.languageStr" :key="item" type="primary" class="me-1">
              {{ item }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column :label="t('academy.course.trainerType')" align="center" prop="trainerType" min-width="180px">
          <template #default="scope">
            <dict-tag :type="DICT_TYPE.TRAINER_TYPE" :value="scope.row.trainerType" min-width="180px" />
          </template>
        </el-table-column>
        <el-table-column :label="t('academy.course.approvals')" align="center" min-width="180px">
          <template #default="scope">
            {{ scope.row.isApprovals ? t('academy.course.yes') : t('academy.course.no') }}
          </template>
        </el-table-column>
        <el-table-column :label="t('academy.course.validity')" align="center" prop="validity" min-width="180px">
          <template #default="scope">
            {{ scope.row.validity }} {{ t('academy.course.months') }}
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <Pagination
        :total="total"
        v-model:page="queryParams.pageNo"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </ContentWrap>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="showCourse = false">{{ t('academy.course.cancel') }}</el-button>
        <el-button type="primary" @click="confirmCourse" :loading="formLoading">{{ t('academy.course.confirm') }}</el-button>
      </div>
    </template>

  </Dialog>

</template>

<script setup lang="ts">
import { CourseApi, CourseRespVO } from '@/api/academy/course'
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { listTopic } from "@/api/category/training"
import { handleTree, defaultProps } from "@/utils/tree"
import { getUsers} from "@/api/system/user"
import { listCompany } from "@/api/system/company"
/** Prerequisite课程信息 列表 */
defineOptions({ name: 'PrerequisiteCourse' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(false) // 列表的加载中
const list = ref<CourseRespVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  title: undefined,
  categoryId: undefined,
  language: undefined,
  trainerType: undefined
})
const multipleSelection = ref([])
const queryFormRef = ref() // 搜索的表单
const RefSingleTable = ref()
const formLoading = ref(false)
/** 添加/修改操作 */
const formRef = ref()
const exportLoading = ref(false) // 导出的加载中
const showCourse = ref(false)
const courseInfo = ref({}) // 存储课程详情信息
const categoryList = ref([]) // 课程分类信息
/**  已选择的课程ID */
const checkCourse = ref([])
const selectedCoueseIds = ref([])
const companyList = ref([]) // 部门信息
const userList = ref([]) // 用户信息
/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const res = await CourseApi.getCoursePage(queryParams)
    list.value = res.list
    list.value = res.list.map((course: CourseRespVO) => {
      return {
        ...course,
        isApprovals: course.ifmsApprovalKey || course.contractorApprovalKey ? 1 : 0,
      }
    })
    if (selectedCoueseIds.value) {
      setTableSelected()
    }
    total.value = res.total
  } finally {
    loading.value = false
  }
}

// 分类列表
const getCategory = async () => {
  const data = await listTopic()
  categoryList.value = handleTree(data)
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  multipleSelection.value = []
  queryParams.pageSize = 10
  queryParams.title = undefined
  queryParams.categoryId = undefined
  queryParams.language = undefined
  queryParams.trainerType = undefined
  handleQuery()
}


// 截取code值(根据分类名称第一个 - 进行截取拼接)
const getCode = (val: string) => {
  const parts = val.split('-')
  return parts.length > 0 ? parts[0] : val
}
const emits = defineEmits(['confirm'])
/** 选择条数  */
const handleSelectionChange = (selection: CourseRespVO[]) => {
  multipleSelection.value = selection
}

const confirmCourse = () => {
  emits('confirm', multipleSelection.value)
  showCourse.value = false
}
// 设置表格数据回显
const setTableSelected = () => {
  setTimeout(() => {
    list.value.forEach((item, index) => {
      selectedCoueseIds.value.forEach(sel => {
        if (sel === item.id) {
          sel = item
          RefSingleTable.value.toggleRowSelection(item, true)
        }
      })
    })
  }, 0)
}

/** 打开弹窗 */
const open = (courseIds?: number) => {
  selectedCoueseIds.value = courseIds
  showCourse.value = true
  resetQuery()
  getCategory()
}

defineExpose({ open }) // 提供 open 方法，用于打开弹窗
</script>