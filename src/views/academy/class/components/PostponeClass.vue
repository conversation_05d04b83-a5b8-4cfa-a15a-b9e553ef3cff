<template>
  <Dialog v-model="showClass" align-center :width="1050" :title="t('academy.class.postponeClass')">
    <ContentWrap>
      <div class="time-range-selector">
        <el-radio-group
          v-model="selectedTimeRange"
          @change="handleTimeRangeChange"
          class="time-radio-group"
        >
          <el-radio
            v-for="(timeRange, index) in availableTimeRanges"
            :key="index"
            :label="index"
            class="time-radio-item"
          >
            <span class="time-range-text">{{ formatTimeRange(timeRange) }}</span>
          </el-radio>
        </el-radio-group>

        <div v-if="availableTimeRanges.length === 0" class="no-time-available">
          <el-empty description="No time slots available" />
        </div>
      </div>
    </ContentWrap>
    <template #footer>
      <el-button @click="showClass = false">{{ t('academy.class.cancel') }}</el-button>
      <el-button @click="submitForm" :disabled="selectedTimeRange === null" type="primary">
        {{ t('dialog.confirm') }}
      </el-button>
    </template>
  </Dialog>

  <Dialog v-model="showPostponeClass" :title="t('academy.class.confirmPostponeClass')" width="500">
    <div>{{ t('academy.class.confirmPostponeAndMerge', { name: checkClassInfo.name }) }}</div>
    <div class="flex justify-between items-center">
      <span class="text-black">{{ t('academy.class.messageNotification') }}</span>
      <el-switch
        v-model="formData.notification"
        size="large"
        :active-value="1"
        :inactive-value="0"
      />
    </div>
    <div>{{ t('academy.class.notificationDescription') }}</div>
    <template #footer>
      <el-button @click="showPostponeClass = false">{{ t('academy.class.cancel') }}</el-button>
      <el-button @click="submitPostpone" type="primary" :loading="formLoading">{{
        t('dialog.confirm')
      }}</el-button>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import download from '@/utils/download'
import { CourseApi } from '@/api/academy/course'
import { ClassInfoApi, ClassInfoRespVO, ClassStatusEnum, ClassTypeEnum } from '@/api/academy/class'
import ClassForm from './ClassForm.vue'
import PostponeClass from './components/postponeClass.vue'
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import {
  getMonthStartAndEnd,
  getNextMonthStartAndEnd,
  getQuarterStartAndEnd,
  getWeekStartAndEnd,
  getYearStartAndEnd
} from '@/utils/formatDate'

/** postpone合并class信息 列表 */
defineOptions({ name: 'PostponeClass' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(false) // 列表的加载中
const formLoading = ref(false) //
const list = ref<ClassInfoRespVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  courseId: undefined,
  name: undefined,
  type: undefined,
  languages: undefined,
  status: ClassStatusEnum.DRAFT,
  startDate: []
})
const formData = ref({
  classId: '',
  targetClassId: '',
  notification: 1
})
const queryFormRef = ref() // 搜索的表单
const timeType = ref() // 默认当前月
const checkClassInfo = ref({}) // 已经选择的class信息
const showClass = ref(false)
const showPostponeClass = ref(false)
const classId = ref() // 通过首页传递过来的class Id
const templateSelection = ref<string[]>([])
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const queryAvailTimeRange = {
  classId: 0,
  startDate: '',
  intervalHours: 0
}

// 时间段选择相关数据
const selectedTimeRange = ref<number | null>(null) // 选中的时间段索引
const availableTimeRanges = ref<Array<{ startTime: string; endTime: string }>>([]) // 可用的时间段列表
/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const res = await ClassInfoApi.getClassInfoPage(queryParams)
    // 根据点击Postpone按钮将外层的课堂id带过来,如果是一样的课堂按id去除
    list.value = res.list?.filter((clazz: ClassInfoRespVO) => clazz.id !== classId.value)
    total.value = list.value?.length
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  timeType.value = 2
  // 课程id和语言id不允许全部清空
  queryParams.pageNo = 1
  queryParams.pageSize = 10
  queryParams.name = ''
  queryParams.type = undefined
  queryParams.status = 0
  handleQuery()
}

/** 单选class */
const handleCurrentChange = (currentRow: ClassInfoRespVO, oldCurrentRow) => {
  if (currentRow) {
    checkClassInfo.value = currentRow
    templateSelection.value = [currentRow.id]
  } else {
    templateSelection.value = []
  }
}

const addHoursToDate = (date, hours) => {
  const result = new Date(date)
  result.setHours(result.getHours() + hours)
  return result.toISOString().split('T')[0]
}

const calculateHoursRoundedUp = (startTime, endTime) => {
  // 将时间字符串转换为秒数
  function timeToSeconds(timeStr) {
    const [hours, minutes, seconds] = timeStr.split(':').map(Number)
    return hours * 3600 + minutes * 60 + seconds
  }
  // 计算时间差（秒）
  const startSeconds = timeToSeconds(startTime)
  const endSeconds = timeToSeconds(endTime)
  const diffSeconds = endSeconds - startSeconds

  // 转换为小时并向上取整
  const exactHours = diffSeconds / 3600
  const roundedHours = Math.ceil(exactHours)
  return roundedHours
}

/** 打开弹窗 */
const open = async (item: ClassInfoRespVO) => {
  queryAvailTimeRange.classId = item.id
  queryAvailTimeRange.startDate = item.startDate as unknown as string
  queryAvailTimeRange.intervalHours = calculateHoursRoundedUp(item.startTime, item.endTime)
  const availableTimeRange = await ClassInfoApi.calculateAvailableTimeRange(queryAvailTimeRange)
  availableTimeRanges.value = availableTimeRange
  selectedTimeRange.value = null // 重置选择

  templateSelection.value = []
  showClass.value = true
  classId.value = item.id
  checkClassInfo.value = {}
  // 根据外层选择的课堂的开始时间筛选出推迟24小时后的课堂
  queryParams.startDate = [`${addHoursToDate(item.startDate, 24)}`, '2099-01-01']
  // 该页面中的Course和要延期的Class的Course  需求
  queryParams.courseId = item.courseId
  queryParams.languages = item.language
  resetQuery()
}

// 格式化时间段显示
const formatTimeRange = (timeRange: { startTime: string; endTime: string }) => {
  return `${timeRange.startTime} - ${timeRange.endTime}`
}

// 处理时间段选择变化
const handleTimeRangeChange = (value: number) => {
  const selectedRange = availableTimeRanges.value[value]
}

const submitForm = () => {
  if (selectedTimeRange.value !== null) {
    formData.value.classId = classId.value
    showPostponeClass.value = true
    // 默认开启发送消息
    formData.value.notification = 1
  }
}
// 最终合并
const submitPostpone = async () => {
  try {
    formLoading.value = true
    // 检查是否选择了时间段
    if (selectedTimeRange.value === null) {
      message.error('Please select a time period')
      return
    }
    // 获取选中的时间段
    const selectedRange = availableTimeRanges.value[selectedTimeRange.value]

    const mergeData = {
      classId: Number(classId.value),
      startDate: queryAvailTimeRange.startDate,
      startTime: selectedRange.startTime,
      endTime: selectedRange.endTime,
      notification: formData.value.notification
    }
    await ClassInfoApi.mergeClassTime(mergeData)
    message.success(t('academy.class.mergeSuccessful'))
    showClass.value = false
    showPostponeClass.value = false
    emit('success')
  } catch (error: any) {
    message.error('Failed to modify course time')
  } finally {
    formLoading.value = false
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗
</script>

<style scoped>
.time-range-selector {
  padding: 16px;
}

.time-radio-group {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.time-radio-item {
  margin-right: 0;
  margin-bottom: 0;
  padding: 12px 16px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  transition: all 0.3s;
  flex: 0 0 auto;
  min-width: 180px;
}

.time-radio-item:hover {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.time-radio-item.is-checked {
  border-color: #409eff;
  background-color: #ecf5ff;
}

.time-range-text {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

.no-time-available {
  text-align: center;
  padding: 40px 0;
}
</style>
