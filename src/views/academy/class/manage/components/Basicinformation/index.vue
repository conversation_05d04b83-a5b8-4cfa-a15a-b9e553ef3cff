<template>
  <ContentWrap>
    <div class="rounded-x py-5 px-7">
      <div class="flex justify-between items-center">
        <span class="text-[#222222] text-xl"> {{ t('learningCenter.course.basicInfo') }} </span>
        <div>
          <!--          加入直播 当类型未直播或者混合类型的时候进行展示，当课堂的状态为进行中的时候允许点击-->
          <el-button
            type="primary"
            v-hasPermi="['academy:class-info:join']"
            @click="handleJoin"
            v-show="ClassTypeEnum.VIRTUAL_CLASS || ClassTypeEnum.HYBRID_CLASS"
            :disabled="classInfo.status !== ClassStatusEnum.ONGOING"
          >
            <Icon icon="ep:link" class="mr-5px" />
            {{ t('academy.class.join') }}
          </el-button>
          <el-button
            type="primary"
            @click="handleBack"
            :disabled="
              classInfo.status === ClassStatusEnum.ONGOING ||
              classInfo.status === ClassStatusEnum.ENDED ||
              classInfo.status === ClassStatusEnum.POSTPONED ||
              classInfo.status === ClassStatusEnum.CANCELLED
            "
          >
            <Icon icon="ep:edit" class="mr-5px" />
            {{ t('academy.classroom.edit') }}
          </el-button>
        </div>
      </div>
      <el-descriptions :column="2" border class="mt-5">
        <el-descriptions-item :label="t('academy.class.courseTitle')">
          {{ classInfo.courseName }}
        </el-descriptions-item>
        <el-descriptions-item :label="t('academy.course.category')">{{
          classInfo?.courseDO?.categoryFullPath
        }}</el-descriptions-item>
        <el-descriptions-item :label="t('academy.class.classCode')">{{
          classInfo.code
        }}</el-descriptions-item>
        <!--        <el-descriptions-item label="Class Title">{{-->
        <!--            classInfo.name-->
        <!--          }}</el-descriptions-item>-->
        <el-descriptions-item :label="t('academy.class.classType')">
          <dict-tag :type="DICT_TYPE.CLASS_TYPE" :value="classInfo.type" />
        </el-descriptions-item>
        <el-descriptions-item :label="t('academy.class.trainer')">{{
          classInfo.trainerName
        }}</el-descriptions-item>
        <el-descriptions-item :label="t('academy.class.duration')">{{
          classInfo.startTime && classInfo.endTime
            ? `${classInfo.startTime} - ${classInfo.endTime}`
            : ''
        }}</el-descriptions-item>
        <el-descriptions-item :label="t('academy.class.trainingDays')">{{
          classInfo.trainingDays
        }}</el-descriptions-item>
        <el-descriptions-item :label="t('academy.class.language')">
          <dict-tag :type="DICT_TYPE.TRAINING_COURSE_LANGUAGE" :value="classInfo.language" />
        </el-descriptions-item>
        <el-descriptions-item :label="t('academy.class.translator')">
          <dict-tag :type="DICT_TYPE.TRAINING_COURSE_LANGUAGE" :value="classInfo.translator" />
        </el-descriptions-item>
        <el-descriptions-item :label="t('academy.class.classroom')">{{
          classInfo.classRoomName
        }}</el-descriptions-item>
        <el-descriptions-item :label="t('academy.class.maximumAttendanceNum')">{{
          classInfo.maxNum
        }}</el-descriptions-item>
        <el-descriptions-item :label="t('academy.class.minimumAttendanceNum')">{{
          classInfo.minNum
        }}</el-descriptions-item>
        <!--        <el-descriptions-item label="Live Link">{{-->
        <!--            classInfo.liveLink-->
        <!--          }}</el-descriptions-item>-->
      </el-descriptions>
    </div>
  </ContentWrap>
</template>
<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { calculateTrainingDays } from '@/utils/formatDate'
import {
  ClassInfoApi,
  ClassStatusEnum,
  ClassPublishStatusEnum,
  ClassTypeEnum
} from '@/api/academy/class'
import { useTagsViewStore } from '@/store/modules/tagsView'
import { formatTime } from '@/utils/formatDate'

/** 课堂信息 表单 */
defineOptions({ name: 'BasicInformation' })

const { t } = useI18n() // 国际化
const route = useRoute()
const message = useMessage() // 消息弹窗
const { delView } = useTagsViewStore() // 视图操作
const { router, push, currentRoute } = useRouter() // 路由
const props = defineProps<{
  classId: string | number
  basicInformation: any
}>()
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const classInfo = ref([])
const formRef = ref() // 表单 Ref

// 回退编辑抽屉界面
const handleBack = () => {
  delView(unref(currentRoute))
  push({
    path: '/academy/class',
    query: { id: props.classId }
  })
}

// 加入直播
const handleJoin = async () => {
  try {
    const data = await ClassInfoApi.joinLive({ classId: route.query.id })
    window.open(data, '_blank')
  } catch (e) {}
}

const getInfo = async (id: number) => {
  classInfo.value = await ClassInfoApi.getClassInfo(id)
  console.log(classInfo.value.status === ClassStatusEnum.ONGOING)
}

onMounted(() => {
  if (route.query.id) {
    getInfo(route.query.id as unknown as number)
  }
})
defineExpose({ getInfo }) // 提供 open 方法，用于打开弹窗
</script>
<style scoped lang="scss"></style>
