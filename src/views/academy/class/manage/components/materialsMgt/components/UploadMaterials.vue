<template>
  <el-drawer :title="t('common.uploadMaterials')" v-model="dialogVisible" size="900">
    <CourseResource
      ref="resourceRef"
      v-model="resourceList"
      :is-from-repository="false"
      @confirm="selectFile"
      class="w-full"
      :page-name-type="[...Video, ...PDF]"
      :ooc="true"
      :limit="1"
      :edit="false"
    />
    <template #footer>
      <el-button @click="dialogVisible = false">{{ t('common.cancel') }}</el-button>
      <el-button @click="submitForm" type="primary" :loading="formLoading">{{
        t('common.save')
      }}</el-button>
    </template>
  </el-drawer>
</template>
<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import CourseResource from '@/components/CourseResource/index.vue'
import { CourseOrigin, MediaType } from '@/enums/resource'
import { PDF, PPT, Video } from '@/components/LargeFileUpload/script/FileAllowTypes'
import { ClassInfoApi } from '@/api/academy/class'
import { formatChapterType } from '@/views/learning-center/course/course-form/components/CourseCatalogue/CourseDialog/utils'
/** 上传课件 */
defineOptions({ name: 'UploadMaterials' })
interface SelectedResource {
  origin: CourseOrigin //
  format: string | undefined
  mediaType: MediaType
  lang: string[]
  duration: number
  fileId: number | undefined
  size: number | undefined
  url: string | undefined
  name: string | undefined
  resourceId: number | undefined
}
const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const route = useRoute()
const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formRef = ref() // 表单 Ref
const resourceRef = ref()
const formData = ref({
  classId: route.query.id,
  resourceId: undefined
})
const resourceList = ref<SelectedResource[]>([])
/** 打开弹窗 */
const open = async () => {
  resetForm()
  dialogVisible.value = true
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

const selectFile = (file: SelectedResource) => {
  formData.value.resourceId = file[0].fileId
  console.log('传入uploadMaterial的resourceId', formData.value.resourceId)
}

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  formLoading.value = true
  try {
    console.log('resource list', resourceList.value)
    const params = resourceList.value.map((item) => ({
      title: item.name,
      // 如果是视频类型，默认就是秒，所以不需要转换
      duration: [MediaType.Audio, MediaType.Video].includes(item.mediaType || MediaType.Video)
        ? item.duration
        : item.duration
          ? +item.duration
          : 0,
      mediaType: item.mediaType,
      fileId: item.fileId,
      address: item.url,
      lang: Array.isArray(item.lang) ? item.lang.join(',') : `${item.lang}`,
      size: item.size,
      // 添加其他必要字段
      enable: '1', // 默认启用
      type: 'L', // 默认类型, 本地上传
      scope: 1, // 默认范围
      delete: false, // 默认不删除
      referenceCount: 0 // 默认引用计数
    })) as ResourceSaveVO[]
    console.log('开始保存资源到服务器')
    const addResourceToServerResponse = await ClassInfoApi.addResource(params)
    console.log('获取资源列表resourceData', addResourceToServerResponse)
    const dataForCreateMaterial = {
      classId: Number(formData.value.classId),
      resourceId: Number(addResourceToServerResponse[0])
    }
    console.log('resourceId', dataForCreateMaterial.resourceId)
    await ClassInfoApi.createMaterial(dataForCreateMaterial)
    message.success(t('common.successfulOperation'))
    emit('success')
    dialogVisible.value = false
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    classId: route.query.id,
    resourceId: undefined
  }
  resourceList.value = []
}
</script>
