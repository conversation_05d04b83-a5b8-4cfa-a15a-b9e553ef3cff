<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item>
        <el-button type="success" plain @click="handleExport">
          <Icon icon="ep:download" class="mr-5px" />
          {{ t('global.export') }}
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="50" fixed="left" />
      <el-table-column :label="t('sys.user.company')" align="center" prop="companyName" min-width="180px" />
      <el-table-column :label="t('academy.class.studentNumber')" align="center" prop="studentNumber" min-width="180px" />
      <el-table-column :label="t('academy.class.completionRate')" align="center" prop="completionRate" min-width="180px">
        <template #default="scope">
          {{ scope.row.completionRate }} %
        </template>
      </el-table-column>
      <el-table-column :label="t('academy.class.notPassed')" align="center" prop="notPassed" min-width="180px" />
      <el-table-column :label="t('table.action')" align="center" fixed="right" min-width="120px">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="handleDetail(scope.row)"
          >
            {{ t('dialog.detail') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </ContentWrap>

  <CompanyDetail
    ref="companyDetailRef"
  />

</template>

<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { ClassInfoApi, PostPoneCompanyRespVO } from '@/api/academy/class'
import CompanyDetail from './CompanyDetail.vue'
import download from '@/utils/download'
import { listCompany } from "@/api/system/company"
import { DeptRespVO } from "@/api/system/dept"
import { getUsers } from "@/api/system/user"

defineOptions({ name: 'ClassRosterCompany' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const route = useRoute()
const total = ref(0)
const queryFormRef = ref() // 搜索的表单
const companyDetailRef = ref()
const checkCompany = ref([])
const loading = ref(false)
const exportLoading = ref(false)
const list = ref([])

const getList = async () => {
  loading.value = true
  try {
    list.value = await ClassInfoApi.postponeCompany({ classId: route.query.id as number })
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  getList()
}


/** 选择条数  */
const handleSelectionChange = (selection: any[]) => {
  checkCompany.value = []
  selection.forEach(item => {
    checkCompany.value.push(item.id)
  })
}


// 查看详情
const handleDetail = (item: PostPoneCompanyRespVO) => {
  companyDetailRef.value.open(item)
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    exportLoading.value = true
    const data = await ClassInfoApi.exportPostponeCompany({ classId: route.query.id as number })
    download.excel(data, 'Company.xlsx')
  } finally {
    exportLoading.value = false
  }
}


defineExpose({ getList })
onMounted(() => {
  getList()
})
</script>