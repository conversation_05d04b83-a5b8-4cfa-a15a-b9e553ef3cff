<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { listCompany } from '@/api/system/company'
import { DeptRespVO, listDept } from '@/api/system/dept'
import { handlePhaseTree } from '@/utils/tree'
import { getUsers, UserRespVO } from '@/api/system/user'
import { ClassInfoApi } from '@/api/academy/class'
import TestReminder from './TestReminder.vue'
import { dateFormatter } from '@/utils/formatTime'
import { useUserStore } from '@/store/modules/user'
import { getTopic, TopicDdtEnum } from '@/api/category/training'
import { reactive, ref } from 'vue'

defineOptions({ name: 'ClassRosterStudent' })

const message = useMessage()
const { t } = useI18n()
const route = useRoute()
const userStore = useUserStore()
const total = ref(0)
const status = ref<boolean | undefined>()
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  attendanceStatus: undefined,
  status: undefined,
  companyId: undefined,
  deptId: undefined,
  userIds: [],
  classIds: route.query.id,
  nickname: undefined
})
const queryFormRef = ref()
const testReminderRef = ref()
const companyList = ref([])
const departOptions = ref([])
const queryUserParams = ref({
  pageNo: 1,
  pageSize: 99999,
  nickname: undefined
})
const userList = ref<UserRespVO[]>([])
const loading = ref(false)
const checkStudent = ref<number[]>([])
const actionType = ref<number | undefined>()
const actionList = computed(() => [
  { label: t('common.batchReject'), value: 1 },
  { label: t('common.batchPass'), value: 2 },
  { label: t('common.batchFail'), value: 3 },
  { label: t('common.batchPostpone'), value: 4 }
])

const queryData = ref({
  ids: [],
  status: 0,
  classId: route.query.id
})

const list = ref<any[]>([])

/** eyeTest 编辑控制：使用 reactive 缓存，显示下拉选项 P/G/F */
const editingEye = ref<number | null>(null)
const eyeCache = reactive<Record<number, string>>({})
const EYE_OPTIONS = [
  { label: 'P', value: 'P' }, // Pass
  { label: 'G', value: 'G' }, // Glasses pass
  { label: 'F', value: 'F' } // Fail
]

function handleCellDblclick(row: any, column: any) {
  if (!status.value) return
  if (column && column.property === 'eyeTest') {
    enterEyeEdit(row)
  }
}
function enterEyeEdit(row: any) {
  editingEye.value = row.id
  eyeCache[row.id] = row.eyeTest ?? ''
}
function cancelEyeEdit() {
  if (editingEye.value != null) {
    delete eyeCache[editingEye.value]
    editingEye.value = null
  }
}

async function confirmEyeEdit(row: any) {
  const id = row.id
  const newVal = (eyeCache[id] ?? '').trim()
  if (!newVal || newVal === (row.eyeTest ?? '')) {
    cancelEyeEdit()
    return
  }

  try {
    const payload = { ids: [id], eyeTest: newVal }
    // prefer API wrapper if exists, fallback to fetch
    if (typeof (ClassInfoApi as any).updateEyeTest === 'function') {
      await (ClassInfoApi as any).updateEyeTest(payload)
    }
    row.eyeTest = newVal
    message.success(t('common.successfulOperation'))
  } catch (e) {
    message.error?.(t('sys.api.errMsg500'))
  } finally {
    cancelEyeEdit()
  }
}

const getList = async () => {
  loading.value = true
  try {
    const res = await ClassInfoApi.getRosterStudentList(queryParams)
    list.value = (res.list || []).map((r: any) => ({ ...r }))
    total.value = res.total
    actionType.value = undefined
  } finally {
    loading.value = false
  }
}

/** 搜索 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置 */
const resetQuery = () => {
  actionType.value = undefined
  queryFormRef.value.resetFields()
  queryParams.userIds = []
  getDepartmentTree(userStore.user.companyId)
  handleQuery()
}

// 选择选项时的处理（但不关闭下拉框）
const handleStatusChange = async (value, row) => {
  ElMessageBox.confirm(`Are you sure you want to change the state of ${row.nickname} to present?`)
    .then(async () => {
      row.attendanceStatus = value
      await saveAttendanceStatus(row)
      message.success('Check in successful')
    })
    .catch(() => {
      row.attendanceStatus = row.attendanceStatus === 1 ? 0 : 1
    })
}

const classCheck = ref({
  userId: 0,
  classId: 0
})

// 保存考勤状态
const saveAttendanceStatus = async (row) => {
  classCheck.value.userId = row.userId
  classCheck.value.classId = row.classId
  await ClassInfoApi.classAdminCheckIn(classCheck.value)
}

/** 选择条数 */
const handleSelectionChange = (selection: any[]) => {
  checkStudent.value = selection.map((s) => s.id)
}
const handleStatus = async (id: number | undefined, type: number, statusVal: number) => {
  queryData.value.status = statusVal
  try {
    queryData.value.ids = type === 1 ? [id] : checkStudent.value
    await ClassInfoApi.rejectStudent(queryData.value)
    message.success(t('common.successfulOperation'))
    getList()
  } catch {}
}

const changeBatchAction = (val: number) => {
  if ([1, 2, 3, 4].includes(val)) {
    handleStatus(undefined, 2, val)
  }
}

const changeReminder = () => {
  testReminderRef.value.open()
}

const getCompanyTree = async () => {
  companyList.value = await listCompany()
}
const handleCompanyClick = (node: DeptRespVO) => {
  departOptions.value = []
  getDepartmentTree(node.id)
}
const getDepartmentTree = async (companyId: number) => {
  const data = await listDept({ companyId })
  if (data) departOptions.value = handlePhaseTree(data, 'id')
}
const getUserList = async () => {
  queryParams.userIds = []
  const res = await getUsers(queryUserParams.value)
  userList.value = res.list
}

const getInfo = async (id: number) => {
  const data = await ClassInfoApi.getClassInfo(id)
  const categoryId = data?.courseDO?.categoryId
  const topicInfo = await getTopic(categoryId)
  status.value = topicInfo.ddt === TopicDdtEnum.YES ? true : false
}

defineExpose({ getList })
onMounted(() => {
  getInfo(route.query.id as unknown as number)
  getList()
  getUserList()
  getCompanyTree()
  getDepartmentTree(userStore.user.companyId)
})
</script>

<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="140px"
    >
      <el-form-item :label="t('sys.user.company')" prop="companyId">
        <el-tree-select
          v-model="queryParams.companyId"
          :data="companyList"
          :props="{ value: 'id', label: 'name', children: 'children' }"
          value-key="id"
          :placeholder="t('sys.user.companyPH')"
          check-strictly
          clearable
          filterable
          class="!w-240px"
          @node-click="handleCompanyClick"
        />
      </el-form-item>
      <el-form-item :label="t('sys.user.department')" prop="deptId">
        <el-tree-select
          v-model="queryParams.deptId"
          :data="departOptions"
          :props="{ value: 'id', label: 'name', children: 'children' }"
          value-key="id"
          :placeholder="t('sys.user.departmentPH')"
          check-strictly
          clearable
          filterable
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item :label="t('academy.class.attendanceStatus')" prop="attendanceStatus">
        <el-select
          v-model="queryParams.attendanceStatus"
          :placeholder="t('academy.classroom.pleaseSelect')"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ATTENDANCE_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="t('academy.class.status')" prop="status">
        <el-select
          v-model="queryParams.status"
          :placeholder="t('academy.classroom.pleaseSelect')"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.CLASS_ROSTER_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="t('academy.classroom.name')" prop="nickname">
        <el-select
          v-model="queryParams.userIds"
          :placeholder="t('academy.classroom.pleaseSelect')"
          clearable
          filterable
          class="!w-240px"
        >
          <el-option
            v-for="user in userList"
            :key="user.id"
            :label="user.nickname"
            :value="user.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button @click="handleQuery">
          <Icon icon="ep:search" class="mr-5px" /> {{ t('academy.class.search') }}
        </el-button>
        <el-button @click="resetQuery">
          <Icon icon="ep:refresh" class="mr-5px" /> {{ t('academy.class.reset') }}
        </el-button>
        <el-select
          v-model="actionType"
          :placeholder="t('common.batchAction')"
          clearable
          class="!w-200px ms-3"
          @change="changeBatchAction"
        >
          <el-option
            v-for="dict in actionList"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
            :disabled="checkStudent.length === 0"
          />
        </el-select>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <ContentWrap>
    <el-table
      v-loading="loading"
      :data="list"
      :stripe="true"
      :show-overflow-tooltip="true"
      @selection-change="handleSelectionChange"
      @cell-dblclick="handleCellDblclick"
    >
      <el-table-column type="selection" width="50" fixed="left" />
      <el-table-column
        v-if="status"
        :label="t('common.ddtPermitNo')"
        align="center"
        prop="ddtPermitNo"
        min-width="180px"
      />
      <el-table-column
        v-if="status"
        :label="t('academy.class.refresher')"
        align="center"
        prop="issuingDate"
        min-width="180px"
        :formatter="dateFormatter"
      />
      <el-table-column
        :label="t('academy.classroom.name')"
        align="center"
        prop="nickname"
        min-width="180px"
      />
      <el-table-column
        :label="t('academy.class.badgeNo')"
        align="center"
        prop="badgeNo"
        min-width="180px"
      />
      <el-table-column
        v-if="status"
        :label="t('common.dateOfBirth')"
        align="center"
        prop="dateOfBirth"
        min-width="180px"
      />
      <el-table-column
        :label="t('sys.user.company')"
        align="center"
        prop="companyName"
        min-width="180px"
      />
      <el-table-column
        :label="t('common.projectAsset')"
        align="center"
        prop="roomNumber"
        min-width="180px"
      />
      <el-table-column
        :label="t('common.workType')"
        align="center"
        prop="workType"
        min-width="180px"
      >
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.SYSTEM_USER_WORK_TYPE" :value="scope.row.workType" />
        </template>
      </el-table-column>
      <el-table-column
        :label="t('common.workTerm')"
        align="center"
        prop="workTerms"
        min-width="180px"
      >
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.SYSTEM_USER_WORK_TERMS" :value="scope.row.workTerms" />
        </template>
      </el-table-column>
      <el-table-column
        v-if="status"
        :label="t('common.drivingLicenceNumber')"
        align="center"
        prop="drivingLicenceNumber"
        min-width="180px"
      />
      <el-table-column
        v-if="status"
        :label="t('common.issuingDate')"
        align="center"
        prop="issuingDate"
        min-width="180px"
        :formatter="dateFormatter"
      />
      <el-table-column
        v-if="status"
        :label="t('common.expiryDate')"
        align="center"
        prop="expiryDate"
        min-width="180px"
        :formatter="dateFormatter"
      />
      <el-table-column
        v-if="status"
        :label="t('common.vehicle')"
        align="center"
        prop="vehicle"
        min-width="180px"
      />
      <el-table-column
        :label="t('academy.class.position')"
        align="center"
        prop="positionName"
        min-width="180px"
      />

      <!-- Eye Test 列（DDT 课程可编辑，双击或 cell-dblclick 进入） -->
      <el-table-column
        v-if="status"
        :label="t('common.eyeTest')"
        align="center"
        prop="eyeTest"
        min-width="180px"
      >
        <template #default="{ row }">
          <div v-if="editingEye === row.id" class="flex items-center gap-2">
            <el-select v-model="eyeCache[row.id]" size="small" clearable class="w-36">
              <el-option
                v-for="opt in EYE_OPTIONS"
                :key="opt.value"
                :label="opt.label"
                :value="opt.value"
              />
            </el-select>
            <el-button
              size="small"
              type="primary"
              circle
              :disabled="(eyeCache[row.id] ?? '') === (row.eyeTest ?? '') || !eyeCache[row.id]"
              @click="confirmEyeEdit(row)"
              :title="t('global.confirm')"
            >
              <Icon icon="ep:check" />
            </el-button>
            <el-button size="small" circle @click="cancelEyeEdit" :title="t('global.cancel')">
              <Icon icon="ep:close" />
            </el-button>
          </div>
          <div v-else class="cursor-pointer select-none">
            <span>{{ row.eyeTest ?? '' }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column
        :label="t('academy.class.attendanceStatus')"
        align="center"
        prop="attendanceStatus"
        min-width="190px"
      >
        <template #default="scope">
          <el-switch
            :active-value="1"
            :inactive-value="0"
            active-text="present"
            inactive-text="absent"
            v-model="scope.row.attendanceStatus"
            @change="(value) => handleStatusChange(value, scope.row)"
            :disabled="scope.row.attendanceStatus == 1 ? true : false"
          />
          <!-- <dict-tag
            @click="handleEdit(scope.row)"
            :type="DICT_TYPE.ATTENDANCE_STATUS"
            :value="scope.row.attendanceStatus"
          /> -->
        </template>
      </el-table-column>
      <el-table-column
        :label="t('common.testResult')"
        align="center"
        prop="status"
        min-width="180px"
      >
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.CLASS_ROSTER_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>

      <el-table-column
        :label="t('table.action')"
        align="center"
        fixed="right"
        min-width="280px"
        v-if="!status"
      >
        <template #default="scope">
          <el-button link type="primary" @click="handleStatus(scope.row.id, 1, 1)">{{
            t('common.reject')
          }}</el-button>
          <el-button link type="primary" @click="handleStatus(scope.row.id, 1, 2)">{{
            t('common.pass')
          }}</el-button>
          <el-button link type="primary" @click="handleStatus(scope.row.id, 1, 3)">{{
            t('common.fail')
          }}</el-button>
          <el-button link type="primary" @click="handleStatus(scope.row.id, 1, 4)">{{
            t('common.postpone')
          }}</el-button>
        </template>
      </el-table-column>
    </el-table>

    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <TestReminder ref="testReminderRef" />
</template>

<style scoped lang="scss">
:deep(.custom-page) {
  @apply relative p-[unset] #{!important};
  .el-pagination {
    @apply relative #{!important};
  }
}
:deep .custom-elselect .el-select__wrapper {
  border: none;
  margin: 0;
  padding: 0;
}
:deep .custom-elselect .is-disabled {
  cursor: text;
  background-color: #fff;
}
:deep .custom-elselect .is-disabled:hover {
  cursor: text;
  background-color: #f5f7fa;
  box-shadow: 0 0 0 1px #f5f7fa inset;
}
:deep .custom-elselect .el-select__wrapper .el-select__suffix .el-select__icon {
  cursor: text;
  visibility: hidden;
}
:deep .custom-elselect .el-select__placeholder {
  text-align: center;
}
:deep .el-select {
  --el-select-disabled-border: #fff;
  --el-select-disabled-color: #606266;
}
</style>
