<template>
  <el-drawer size="50%" :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      v-loading="formLoading"
    >
      <el-form-item :label="t('academy.course.courseTitle')" prop="courseId">
        <el-select
          v-model="formData.courseId"
          :placeholder="t('academy.course.pleaseSelect')"
          @change="changeCourse"
          :disabled="trainingCourseId"
        >
          <el-option
            v-for="item in courseList"
            :key="item.id"
            :label="item.title"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="t('academy.course.category')">
        <el-tree-select
          v-model="formData.categoryId"
          :data="categoryList"
          :props="defaultProps"
          filterable
          clearable
          check-strictly
          default-expand-all
          value-key="deptId"
          disabled
        />
      </el-form-item>
      <el-form-item :label="t('academy.class.classCode')">
        <el-input v-model="formData.code" disabled />
      </el-form-item>
      <el-form-item :label="t('academy.class.classTitle')">
        <el-input v-model="formData.name" :placeholder="t('academy.course.pleaseInput')" disabled />
      </el-form-item>
      <el-form-item :label="t('academy.class.classType')" prop="type">
        <el-radio-group v-model="formData.type" @change="changeType">
          <el-radio
            v-for="dict in getIntDictOptions(DICT_TYPE.CLASS_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-radio-group>
      </el-form-item>
      <el-form-item :label="t('academy.class.trainer')" prop="trainerId">
        <el-select v-model="formData.trainerId" :placeholder="t('academy.course.pleaseSelect')">
          <el-option
            v-for="item in trainerList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <div class="startDuration">
        <el-form-item :label="t('academy.class.startDate')" prop="startDate">
          <el-date-picker
            class="!mr-2"
            v-model="formData.startDate"
            type="date"
            :placeholder="t('academy.class.pickADate')"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            @change="changeDate"
            :disabled-date="disabledStartDate"
          />
        </el-form-item>
        <el-form-item prop="duration" class="no-label-spacing" style="margin-left: 0px" label="">
          <el-time-picker
            v-model="formData.duration"
            is-range
            :range-separator="t('academy.class.to')"
            :start-placeholder="t('academy.class.startTime')"
            :end-placeholder="t('academy.class.endTime')"
            @change="changeDuration"
            @clear="clearDuration"
            value-format="HH:mm:ss"
            :disabled-hours="disabledHours"
            :disabled-minutes="disabledMinutes"
            :disabled-seconds="disabledSeconds"
          />
        </el-form-item>
        <el-button type="primary" @click="addClassTimeList()" class="!ml-8">+</el-button>
      </div>
      <el-table :data="classTimeTable" style="width: 85%" class="tableTimeStyle">
        <el-table-column prop="startDate" label="Date" class="timeTableColumnStyle" />
        <el-table-column prop="duration" label="duration" />
        <el-table-column label="Operations">
          <template #default="scope">
            <el-button size="small" type="danger" @click="handleDelete(scope.$index, scope.row)">
              Delete
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- <el-form-item :label="t('academy.class.trainingDays')" prop="trainingDays">
        <el-input-number v-model="formData.trainingDays" :min="0" :controls="false" disabled />
        {{ t('academy.class.days') }}
      </el-form-item> -->
      <el-form-item :label="t('academy.class.language')" prop="language">
        <el-radio-group v-model="formData.language">
          <el-radio
            v-for="dict in languageList"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-radio-group>
      </el-form-item>
      <el-form-item
        :label="t('academy.class.translator')"
        v-show="
          formData.type === ClassTypeEnum.OFFLINE_CLASS ||
          formData.type === ClassTypeEnum.HYBRID_CLASS
        "
      >
        <el-row :gutter="20">
          <el-col :span="24">
            <el-switch
              v-model="translatorType"
              size="large"
              :active-value="1"
              :inactive-value="0"
              @change="changeTranslator"
            />
          </el-col>
          <el-col :span="24" v-show="translatorType === 1">
            <div class="flex align-center justify-center">
              <div class="flex align-center">
                <div class="me-[10px] mt-[1px]">{{ t('academy.class.translator') }}:</div>
                <el-radio-group v-model="formData.translator">
                  <el-radio
                    v-for="dict in getIntDictOptions(DICT_TYPE.TRAINING_COURSE_LANGUAGE)"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-radio-group>
              </div>
            </div>
          </el-col>
        </el-row>
      </el-form-item>
      <el-form-item
        :label="t('academy.class.classroom')"
        :prop="classRoomIdProp"
        v-show="
          formData.type === ClassTypeEnum.OFFLINE_CLASS ||
          formData.type === ClassTypeEnum.HYBRID_CLASS
        "
      >
        <el-input
          v-model="checkClassroomInfo.name"
          :placeholder="t('academy.class.pleaseSelectClassroom')"
          disabled
        >
          <template #suffix>
            <Icon icon="ep:more" class="mr-5px cursor-pointer" @click="openClassroom" />
          </template>
        </el-input>
      </el-form-item>
      <el-form-item
        :label="t('academy.class.maximum')"
        :prop="maxNumProp"
        v-show="
          formData.type === ClassTypeEnum.OFFLINE_CLASS ||
          formData.type === ClassTypeEnum.HYBRID_CLASS
        "
      >
        <template #label>
          <Tooltip
            :message="t('academy.class.maximumAttendanceNum')"
            :title="t('academy.class.maximum')"
          />
        </template>
        <el-input-number v-model="formData.maxNum" :min="1" />
      </el-form-item>
      <el-form-item
        :label="t('academy.class.minimum')"
        :prop="minNumProp"
        v-show="
          formData.type === ClassTypeEnum.OFFLINE_CLASS ||
          formData.type === ClassTypeEnum.HYBRID_CLASS
        "
      >
        <template #label>
          <Tooltip
            :message="t('academy.class.minimumAttendanceNum')"
            :title="t('academy.class.minimum')"
          />
        </template>
        <el-input-number v-model="formData.minNum" :min="1" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="dialogVisible = false">{{ t('academy.class.cancel') }}</el-button>
      <el-button
        v-if="formData.status !== ClassPublishStatusEnum.PUBLISHED"
        @click="loopSubmitForm(1)"
        type="primary"
        :disabled="formLoading"
        >{{ t('academy.class.save') }}</el-button
      >
      <el-button @click="loopSubmitForm(2)" type="primary" :disabled="formLoading">{{
        t('academy.class.publish')
      }}</el-button>
    </template>
  </el-drawer>

  <!--选择教室-->
  <ClassroomSelect ref="classroomRef" @confirm="classroomConfirm" />
</template>
<script setup lang="ts">
import {
  ClassInfoApi,
  ClassInfoRespVO,
  ClassPublishStatusEnum,
  ClassTypeEnum
} from '@/api/academy/class'
import { CourseApi } from '@/api/academy/course'
import { TrainerApi } from '@/api/academy/trainer'
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import ClassroomSelect from './components/ClassroomSelect.vue'
import { calculateTrainingDays, getCurrentDateFormatted } from '@/utils/formatDate'
import { listTopic } from '@/api/category/training'
import { handleTree, defaultProps } from '@/utils/tree'
import { ClassRoomRespVO } from '@/api/academy/classroom'
import { formatTime } from '@/utils/formatDate'

/** 课堂信息 表单 */
defineOptions({ name: 'ClassInfoForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const router = useRouter()
const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  categoryId: undefined,
  courseId: undefined,
  name: undefined,
  code: undefined,
  type: ClassTypeEnum.OFFLINE_CLASS,
  trainerId: undefined,
  classRoomId: undefined,
  duration: [],
  startTime: undefined,
  endTime: undefined,
  trainingDays: undefined,
  translator: 0,
  language: undefined,
  maxNum: undefined,
  minNum: 3,
  startDate: undefined
})
const translatorType = ref(0) // 默认不开启翻译
const trainingCourseId = ref() // 用来记录是从日历过来的
const formRules = reactive({
  courseId: [{ required: true, message: t('academy.class.courseRequired'), trigger: 'change' }],
  type: [{ required: true, message: t('academy.class.courseTypeRequired'), trigger: 'change' }],
  trainerId: [{ required: true, message: t('academy.class.trainerRequired'), trigger: 'change' }],
  // duration: [{ required: true, message: t('academy.class.durationRequired'), trigger: 'blur' }],
  // startDate: [{ required: true, message: t('academy.class.startDateRequired'), trigger: 'blur' }],
  trainingDays: [
    { required: true, message: t('academy.course.languageRequired'), trigger: 'blur' }
  ],
  // translator: [{ required: true, message: 'Translation setting cannot be empty', trigger: 'blur' }],
  language: [{ required: true, message: t('academy.course.languageRequired'), trigger: 'change' }],
  classRoomId: [
    { required: true, message: t('academy.class.pleaseSelectClassroom'), trigger: 'change' }
  ],
  maxNum: [{ required: true, message: t('academy.class.maximumAttendanceNum'), trigger: 'blur' }],
  minNum: [{ required: true, message: t('academy.class.minimumAttendanceNum'), trigger: 'blur' }]
})
const languageList = ref()
const formRef = ref() // 表单 Ref
const courseList = ref([]) // 课程信息
const categoryList = ref([]) // 分类信息
const trainerList = ref() // 老师信息
const classroomRef = ref()
const checkClassroomInfo = ref({
  name: '',
  id: undefined
}) // 选择的教室信息

// 发布form表单
const publishForm = ref({
  ids: [],
  publishStatus: ClassPublishStatusEnum.PUBLISHED,
  notification: 1 // 默认发送提醒
})

// 获取当前年月日
const getCurrentDateObject = () => {
  const now = new Date()
  const year = now.getFullYear()
  const month = (now.getMonth() + 1).toString().padStart(2, '0') // 月份从0开始，需要+1
  const day = now.getDate().toString().padStart(2, '0')

  return `${year}-${month}-${day}`
}

const handleDelete = (index: number, row: any) => {
  // Remove the item at the specified index from the classTimeTable array
  classTimeTable.value.splice(index, 1)
}

/** 打开弹窗 */
const open = async (type: string, id?: number, courseId?: number, categoryId?: number) => {
  // 当编辑进来的时候点击发布操作进行赋值
  publishForm.value.ids = [id]
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 当从日历页面在课程后面的日期后新增class，此时是存在课程id
  if (courseId) {
    trainingCourseId.value = courseId
    formData.value.courseId = courseId
    changeCourse(courseId as number)
    // formData.value.categoryId = categoryId
  } else if (id) {
    formLoading.value = true
    try {
      const data = await ClassInfoApi.getClassInfo(id)
      // 根据课堂所选择的课程绑定的教师类型 ，进行调用教师分页查询出对应类型的老师
      await getTrainerList(data.courseTrainerType)
      formData.value = data
      // 当编辑操作进来进行点击发布功能
      publishForm.value.ids = [id]
      // 多取一层分类id
      formData.value.categoryId = data.courseDO.categoryId
      const item = courseList.value.find((item) => item.id === data.courseId)
      // 根据课程所绑定的语言动态展示课堂语言
      languageList.value = item.languageList?.map((num, index) => ({
        label: item.languageStr[index],
        value: num
      }))
      translatorType.value = data.translator !== 0 ? 1 : 0
      checkClassroomInfo.value.name = data.classRoomName
      formData.value.duration = [data.startTime, data.endTime]
      classTimeTable.value = [
        {
          startDate: data.startDate,
          duration: [data.startTime, data.endTime]
        }
      ]
    } finally {
      formLoading.value = false
    }
  }
}

// 切换清空数据
const changeType = (val: number) => {
  if (val === 2) {
    checkClassroomInfo.value.name = ''
    formData.value.translator = 0
    translatorType.value = 0
    formData.value.classRoomId = undefined
    formData.value.maxNum = undefined
    formData.value.minNum = 0
  } else {
    formData.value.minNum = 3
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success', 'successClass']) // 定义 success 事件，用于操作成功后的回调

const addClassTimeList = () => {
  // 创建新的对象副本，避免引用问题
  classTimeTable.value.push({
    startDate: formData.value.startDate,
    duration: [...formData.value.duration] // 复制时间范围数组
  })
}

const classTimeTable: any = ref([])
let classIdList: number[] = []

const loopSubmitForm = async (type: number) => {
  // 自定义校验：检查是否添加了时间到表格中
  if (classTimeTable.value.length === 0) {
    message.error(t('At least one time period must be filled in!'))
    return
  }
  console.log('classTimeTable', classTimeTable)
  let randomNum = 1001
  for (const item of classTimeTable.value) {
    // 创建当前迭代的表单数据副本
    const course = courseList.value.find((item) => item.id === formData.value.courseId)
    await getClassCode(formData.value.courseId as unknown as number, randomNum)
    formData.value.name = `${course.title} - ${formData.value.code}`
    const currentFormData: ClassInfoRespVO = ref({
      ...formData.value,
      startDate: item.startDate,
      duration: [item.duration[0], item.duration[1]]
    })
    console.log('currentFormData', currentFormData)
    await submitForm(type, currentFormData)
  }
  // 校验表单
  await formRef.value.validate()
  if (!trainingCourseId.value) {
    const targetId =
      formType.value === 'create' ? classIdList[classIdList.length - 1] : formData.value.id
    router.push({
      name: 'Manage',
      query: {
        id: targetId
      }
    })
  }
}

const submitForm = async (type: number, formData: ClassInfoRespVO) => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as ClassInfoRespVO
    data.startTime = formData.value.duration[0]
    data.endTime = formData.value.duration[1]
    // data.startDate = getCurrentDateObject()
    if (formType.value === 'create') {
      const classId = await ClassInfoApi.createClassInfo(data)
      classIdList.push(classId)
      publishForm.value.ids = [classId]
      if (type === 1) {
        message.success(t('common.createSuccess'))
      } else {
        // 发布课堂
        await ClassInfoApi.publishClass(publishForm.value)
        message.success(t('academy.class.publishedSuccessfully'))
      }
      if (!trainingCourseId.value) {
        // router.push({
        //   name: 'Manage',
        //   query: {
        //     id: classId
        //   }
        // })
      } else {
        emit('successClass')
      }
    } else {
      await ClassInfoApi.updateClassInfo(data)
      if (type === 1) {
        message.success(t('common.updateSuccess'))
      } else {
        await ClassInfoApi.publishClass(publishForm.value)
        message.success(t('academy.class.publishedSuccessfully'))
      }
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

const changeTranslator = (val: number | string) => {
  if (val === 0) {
    // 是否翻译(0.未开启 1.英文 2.阿拉伯文 3.中文)
    // 切换清空语言
    formData.value.translator = 0
  }
}

const translatorProp = computed(() => {
  return formData.value.type === ClassTypeEnum.OFFLINE_CLASS ||
    formData.value.type === ClassTypeEnum.HYBRID_CLASS
    ? 'translator'
    : ''
})
const classRoomIdProp = computed(() => {
  return formData.value.type === ClassTypeEnum.OFFLINE_CLASS ||
    formData.value.type === ClassTypeEnum.HYBRID_CLASS
    ? 'classRoomId'
    : ''
})
const maxNumProp = computed(() => {
  return formData.value.type === ClassTypeEnum.OFFLINE_CLASS ||
    formData.value.type === ClassTypeEnum.HYBRID_CLASS
    ? 'maxNum'
    : ''
})
const minNumProp = computed(() => {
  return formData.value.type === ClassTypeEnum.OFFLINE_CLASS ||
    formData.value.type === ClassTypeEnum.HYBRID_CLASS
    ? 'minNum'
    : ''
})

/** 获取课程信息 */
const getCourseList = async () => {
  const data = await CourseApi.getCoursePage({ pageNo: 1, pageSize: -1 })
  courseList.value = data.list
}

/** 获取课堂编号 */
const getClassCode = async (courseId: number, randomNum = 1001) => {
  const data = await ClassInfoApi.getClassCode(courseId)
  // 先拼接字符串再相加
  formData.value.code = Number(getCurrentDateFormatted() + randomNum) + data
}

/** 获取教师信息 */
const getTrainerList = async (type: number) => {
  const data = await TrainerApi.getTrainerPage({ pageNo: 1, pageSize: -1, type })
  trainerList.value = data.list
}

// 选择课程随即把分类id带出来
const changeCourse = async (id: number) => {
  const item = courseList.value.find((item) => item.id === id)
  // 通过课程所选定的教师类型筛选出对应的老师
  await getTrainerList(item.trainerType)
  // 根据课程所绑定的语言动态展示课堂语言
  languageList.value = item.languageList?.map((num, index) => ({
    label: item.languageStr[index],
    value: num
  }))
  if (item) {
    formData.value.courseId = id
    formData.value.categoryId = item.categoryId
    // 新增时获取课堂编号
    await getClassCode(id)
    formData.value.name = `${item.title} - ${formData.value.code}`
  }
}

// 分类列表
const getCategory = async () => {
  const data = await listTopic()
  categoryList.value = handleTree(data)
}

// 开始时间和结束时间选择同一时间，则结束时间加1分钟
function adjustEndTimeIfSame(timeList) {
  const startTime = timeList[0]
  let endTime = timeList[1]
  if (startTime === endTime) {
    // 转换为 Date 对象
    let date = new Date(`1970-01-01T${endTime}`)
    date.setMinutes(date.getMinutes() + 1) // 加1分钟

    // 格式化回 "HH:mm:ss"
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    const seconds = String(date.getSeconds()).padStart(2, '0')
    const newTime = `${hours}:${minutes}:${seconds}`

    return [startTime, newTime]
  } else {
    return timeList
  }
}

// 根据开始时间和结束时间计算出训练天数
const changeDuration = (val) => {
  formData.value.duration = adjustEndTimeIfSame(val)
  if (formData.value.duration) {
    formData.value.trainingDays = calculateTrainingDays(
      formData.value.duration[0],
      formData.value.duration[1]
    )
  }
}
const clearDuration = (val: string | number) => {
  if (val === undefined) {
    formData.value.trainingDays = undefined
  }
}

const changeDate = (date: string) => {
  // 每次选择日期都要将时间段清空
  formData.value.duration = []
}

// 禁止选择当前电脑日期之前的日期(不包括当前日期)
const disabledStartDate = (date: any) => {
  return date.getTime() < Date.now() - 8.64e7
}

// 获取当前时间
const now = new Date()
const currentHour = now.getHours()
const currentMinute = now.getMinutes()
const currentSecond = now.getSeconds()

// 判断选择开始日期是否是在当前时间之后的日期,如果是之后的日期时分秒选择不做任何置灰限制
const isDateAfterNow = (startDateStr: string) => {
  // 获取当前日期和时间
  const now = new Date()

  // 将传入的开始日期字符串转换为日期对象
  const startDate = new Date(startDateStr)

  // 比较两个日期（注意：这里忽略了时分秒）
  if (startDate > now) {
    // 开始日期在当前时间之后
    return true
  } else {
    // 开始日期不在当前时间之后
    return false
  }
}

// 禁止选择当前电脑时间之前的时间
// 禁用小时：比当前小时小的都禁用
const disabledHours = () => {
  const result = isDateAfterNow(formData.value.startDate)
  if (!result) {
    const hours: number[] = []
    for (let i = 0; i < currentHour; i++) {
      hours.push(i)
    }
    return hours
  }
}

// 禁用分钟：如果选中的是当前小时，则禁用小于当前分钟的分钟数
const disabledMinutes = (hour: number) => {
  const result = isDateAfterNow(formData.value.startDate)
  if (!result) {
    if (hour === currentHour) {
      const minutes: number[] = []
      for (let i = 0; i < currentMinute; i++) {
        minutes.push(i)
      }
      return minutes
    }
  }

  return []
}

// 禁用秒：如果选中的是当前小时+分钟，则禁用小于当前秒的秒数
const disabledSeconds = (hour: number, minute: number) => {
  const result = isDateAfterNow(formData.value.startDate)
  if (!result) {
    if (hour === currentHour && minute === currentMinute) {
      const seconds: number[] = []
      for (let i = 0; i < currentSecond; i++) {
        seconds.push(i)
      }
      return seconds
    }
  }

  return []
}

/** 打开教室弹框 */
const openClassroom = () => {
  classroomRef.value.open(formData.value.classRoomId)
}

/** 获取教室信息 */
const classroomConfirm = (data: ClassRoomRespVO) => {
  formData.value.classRoomId = data.id
  formData.value.maxNum = data.totalSeats
  checkClassroomInfo.value = data
}

/** 重置表单 */
const resetForm = () => {
  // 清空老师的下拉列表信息
  trainerList.value = []
  trainingCourseId.value = undefined
  translatorType.value = 0
  classTimeTable.value = []
  formData.value = {
    id: undefined,
    courseId: undefined,
    name: undefined,
    code: undefined,
    type: ClassTypeEnum.OFFLINE_CLASS,
    trainerId: undefined,
    classRoomId: undefined,
    duration: [],
    startTime: undefined,
    endTime: undefined,
    trainingDays: undefined,
    translator: 0,
    language: undefined,
    status: undefined,
    maxNum: undefined,
    minNum: 3
  }
  formRef.value?.resetFields()
  checkClassroomInfo.value.name = ''
  // 将上次选择课程所绑定的语言重置
  languageList.value = []
}

onMounted(() => {
  getCourseList()
  getCategory()
})
</script>
<style scoped lang="scss">
.startDuration {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  align-items: end;

  .el-form-item {
    flex: 1;
    min-width: 200px;
    margin-bottom: 0;
  }

  .el-button {
    flex-shrink: 0;
    margin-top: auto;
  }
}
.pickerStyle {
  margin-left: 0;
}
.no-label-spacing {
  :deep(.el-form-item__content) {
    margin-left: 0 !important;
  }
}
.timeTableColumnStyle {
  padding-left: 20px;
}
.tableTimeStyle {
  margin-bottom: 30px !important;
  padding-left: 30px;
}
</style>
