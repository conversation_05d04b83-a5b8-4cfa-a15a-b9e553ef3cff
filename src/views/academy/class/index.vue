<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="120px"
    >
      <el-form-item :label="t('academy.class.course')" prop="courseId">
        <el-select
          v-model="queryParams.courseId"
          :placeholder="t('academy.course.pleaseSelect')"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="item in courseList"
            :key="item.id"
            :label="item.title"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="t('academy.class.type')" prop="type">
        <el-select
          v-model="queryParams.type"
          :placeholder="t('academy.course.pleaseSelect')"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.CLASS_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="t('academy.class.language')" prop="languages">
        <el-select
          v-model="queryParams.languages"
          :placeholder="t('academy.course.pleaseSelect')"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.TRAINING_COURSE_LANGUAGE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="t('academy.class.publishStatus')" prop="publishStatus">
        <el-select
          v-model="queryParams.publishStatus"
          :placeholder="t('academy.course.pleaseSelect')"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.CLASS_PUBLISH_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="t('academy.class.status')" prop="status">
        <el-select
          v-model="queryParams.status"
          :placeholder="t('academy.course.pleaseSelect')"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.CLASS_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <!--      <el-form-item label="Time">-->
      <!--        <el-select-->
      <!--          v-model="timeType"-->
      <!--          placeholder="Please select"-->
      <!--          clearable-->
      <!--          class="!w-240px"-->
      <!--          @change="changeTime"-->
      <!--        >-->
      <!--          <el-option-->
      <!--            v-for="dict in getIntDictOptions(DICT_TYPE.CLASS_TIME)"-->
      <!--            :key="dict.value"-->
      <!--            :label="dict.label"-->
      <!--            :value="dict.value"-->
      <!--          />-->
      <!--        </el-select>-->
      <!--      </el-form-item>-->
      <el-form-item :label="t('academy.class.date')" prop="startDate">
        <el-date-picker
          v-model="queryParams.startDate"
          value-format="YYYY-MM-DD"
          type="daterange"
          :start-placeholder="t('academy.class.start')"
          :end-placeholder="t('academy.class.end')"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item :label="t('academy.class.title')" prop="name">
        <el-input
          v-model="queryParams.name"
          :placeholder="t('academy.class.pleaseInputTitle')"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"
          ><Icon icon="ep:search" class="mr-5px" />{{ t('academy.class.search') }}</el-button
        >
        <el-button @click="resetQuery"
          ><Icon icon="ep:refresh" class="mr-5px" />{{ t('academy.class.reset') }}</el-button
        >
        <el-button
          v-hasPermi="['academy:class-info:create']"
          type="primary"
          plain
          @click="openForm('create')"
        >
          <Icon icon="ep:plus" class="mr-5px" />{{ t('academy.class.add') }}
        </el-button>
        <el-button
          v-hasPermi="['academy:class-info:publish']"
          type="primary"
          plain
          :disabled="checkClass.length === 0"
          @click="publishClass(undefined, 2)"
        >
          <Icon icon="ep:position" class="mr-5px" />{{ t('academy.class.publish') }}
        </el-button>
        <!--        先不做，产品现场做调研-->
        <!--        <el-button-->
        <!--          type="success"-->
        <!--          plain-->
        <!--        >-->
        <!--          <Icon icon="ep:upload" class="mr-5px" /> Import-->
        <!--        </el-button>-->
      </el-form-item>
    </el-form>
    <div style="float: right; margin-top: 10px">
      <el-button @click="((isTable = true), (isCalendar = false))">
        <Icon icon="ep:menu" />
      </el-button>
      <el-button @click="handleCalendar"><Icon icon="ep:notebook" /></el-button>
    </div>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap v-show="isTable">
    <el-table
      v-loading="loading"
      :data="list"
      :stripe="true"
      :show-overflow-tooltip="true"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="50" fixed="left" />
      <el-table-column
        :label="t('academy.class.courseTitle')"
        align="center"
        prop="courseName"
        min-width="180px"
      />
      <el-table-column
        :label="t('academy.class.classTitle')"
        align="center"
        prop="name"
        min-width="180px"
      />
      <el-table-column
        :label="t('academy.class.classType')"
        align="center"
        prop="type"
        min-width="180px"
      >
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.CLASS_TYPE" :value="scope.row.type" />
        </template>
      </el-table-column>
      <el-table-column
        :label="t('academy.class.trainer')"
        align="center"
        prop="trainerName"
        min-width="180px"
      />
      <el-table-column
        :label="t('academy.class.language')"
        align="center"
        prop="language"
        min-width="180px"
      >
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.TRAINING_COURSE_LANGUAGE" :value="scope.row.language" />
        </template>
      </el-table-column>
      <el-table-column
        :label="t('academy.class.classroom')"
        align="center"
        prop="classRoomName"
        min-width="180px"
      />
      <el-table-column
        :label="t('academy.class.date')"
        align="center"
        prop="startDate"
        min-width="180px"
      />
      <el-table-column
        :label="t('academy.class.duration')"
        align="center"
        prop="duration"
        min-width="180px"
      >
        <template #default="scope"> {{ scope.row.startTime }} - {{ scope.row.endTime }} </template>
      </el-table-column>
      <el-table-column :label="t('academy.class.bookingNumber')" align="center" min-width="180px">
        <!--       报名的人数。前面的数字为当前已经报名的人数，后面数字为该课堂最大的可容纳的学生人数。-->
        <template #default="scope">
          <div>
            {{
              scope.row.type === ClassTypeEnum.VIRTUAL_CLASS
                ? scope.row.assignNum
                : `${scope.row.assignNum} / ${scope.row.maxNum}`
            }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        :label="t('academy.class.publishStatus')"
        align="center"
        prop="publishStatus"
        min-width="180px"
      >
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.CLASS_PUBLISH_STATUS" :value="scope.row.publishStatus" />
        </template>
      </el-table-column>
      <el-table-column
        :label="t('academy.class.status')"
        align="center"
        prop="status"
        min-width="180px"
      >
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.CLASS_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column
        :label="t('academy.class.action')"
        align="center"
        fixed="right"
        min-width="400px"
      >
        <template #default="scope">
          <!--          在Published状态下不可编辑 0.Unpublished 1.Published-->
          <el-button
            v-hasPermi="['academy:class-info:publish']"
            link
            type="primary"
            :disabled="scope.row.publishStatus === ClassPublishStatusEnum.PUBLISHED"
            @click="publishClass(scope.row.id, 1)"
          >
            {{ t('academy.class.publish') }}
          </el-button>
          <el-button
            v-hasPermi="['academy:class-info:manage']"
            link
            type="primary"
            @click="jumpManage(scope.row.id)"
          >
            {{ t('academy.class.manage') }}
          </el-button>
          <el-button
            v-hasPermi="['academy:class-info:copy']"
            link
            type="primary"
            :disabled="scope.row.publishStatus === ClassPublishStatusEnum.PUBLISHED"
            @click="handleCopy(scope.row.id)"
          >
            {{ t('academy.class.copy') }}
          </el-button>
          <el-button
            v-hasPermi="['academy:class-info:postpone']"
            link
            type="primary"
            :disabled="scope.row.isPostpone"
            @click="openClass(scope.row)"
          >
            {{ t('academy.class.postpone') }}
          </el-button>
          <el-button
            v-hasPermi="['academy:class-info:cancel']"
            link
            type="primary"
            :disabled="scope.row.isPostpone"
            @click="handleCancel(scope.row.id)"
          >
            {{ t('academy.class.cancel') }}
          </el-button>
          <el-button
            v-hasPermi="['academy:class-info:delete']"
            link
            type="danger"
            :disabled="scope.row.publishStatus === ClassPublishStatusEnum.PUBLISHED"
            @click="handleDelete(scope.row.id)"
          >
            {{ t('academy.class.delete') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>
  <!--  日历-->
  <ContentWrap v-show="isCalendar">
    <Schedule ref="scheduleRef" />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <ClassForm ref="formRef" @success="getList" />
  <PostponeClass ref="postponeClassRef" @success="getList" />
  <Dialog v-model="showCancel" :title="t('academy.class.cancelClass')" width="500">
    <div>{{ t('academy.class.confirmCancelClass') }}</div>
    <div class="flex justify-between items-center">
      <span class="text-black">{{ t('academy.class.messageNotification') }}</span>
      <el-switch
        v-model="formData.notification"
        size="large"
        :active-value="1"
        :inactive-value="0"
      />
    </div>
    <div class="mt-4">
      <el-form :model="formData">
        <el-form-item label="reason" prop="reason">
          <el-input
            type="textarea"
            v-model="formData.reason"
            placeholder="tell the students, the reason is ..."
            :rows="3"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
      </el-form>
    </div>
    <div>{{ t('academy.class.notificationDescription') }}</div>
    <template #footer>
      <el-button @click="showCancel = false">{{ t('academy.class.cancel') }}</el-button>
      <el-button @click="submitForm" type="primary" :loading="formLoading">{{
        t('academy.class.confirm')
      }}</el-button>
    </template>
  </Dialog>

  <Dialog v-model="showClass" :title="t('academy.class.newClassTitle')">
    <el-form ref="formClassRef" :model="formClassData" :rules="formRules" label-width="120px">
      <el-form-item :label="t('academy.class.className')" prop="name">
        <el-text type="info">
          {{ formClassData.name }}
        </el-text>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="showClass = false">{{ t('academy.class.cancel') }}</el-button>
      <el-button type="primary" :loading="formLoading" @click="submitCopyForm">{{
        t('academy.class.confirm')
      }}</el-button>
    </template>
  </Dialog>

  <Dialog v-model="showPublish" :title="t('academy.class.publishClass')" width="500">
    <div>{{ t('academy.class.confirmPublishClass') }}</div>
    <div class="flex justify-between items-center">
      <span class="text-black">{{ t('academy.class.messageNotification') }}</span>
      <el-switch
        v-model="formPublishData.notification"
        size="large"
        :active-value="1"
        :inactive-value="0"
      />
    </div>
    <div>{{ t('academy.class.notificationDescription') }}</div>
    <template #footer>
      <el-button @click="showPublish = false">{{ t('academy.class.cancel') }}</el-button>
      <el-button @click="submitPublishForm" type="primary" :loading="formLoading">{{
        t('academy.class.confirm')
      }}</el-button>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import { CourseApi } from '@/api/academy/course'
import {
  ClassInfoApi,
  ClassInfoRespVO,
  ClassPublishStatusEnum,
  ClassStatusEnum,
  ClassTypeEnum
} from '@/api/academy/class'
import ClassForm from './ClassForm.vue'
import PostponeClass from './components/PostponeClass.vue'
import Schedule from './components/Schedule.vue'
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import {
  getCurrentDateFormatted,
  getMonthStartAndEnd,
  getNextMonthStartAndEnd,
  getQuarterStartAndEnd,
  getWeekStartAndEnd,
  getYearStartAndEnd
} from '@/utils/formatDate'

/** 课堂信息 列表 */
defineOptions({ name: 'ClassInfo' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const router = useRouter()
const route = useRoute()
const loading = ref(false) // 列表的加载中
const list = ref<ClassInfoRespVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  courseId: undefined,
  name: undefined,
  type: undefined,
  languages: undefined,
  publishStatus: undefined,
  status: undefined,
  startDate: []
})

const formClassData = ref({
  name: undefined
})
const formRules = reactive({
  name: [{ required: true, message: t('academy.class.courseNameRequired'), trigger: 'blur' }]
})
const formClassRef = ref()
const formClassLoading = ref(false)
const showClass = ref(false)
const queryFormRef = ref() // 搜索的表单
const postponeClassRef = ref()
const scheduleRef = ref() // 日历
const courseList = ref([]) // 课程信息
const timeType = ref() // 默认当前月
const checkClass = ref([])
const isTable = ref(true)
const isCalendar = ref(false)
const showCancel = ref(false)
const formLoading = ref(false) // cancel
const showPublish = ref(false) // publish
const classInfo = ref() // 存储当前的课堂详情信息

// cancel 功能表单
const formData = ref({
  id: undefined,
  notification: 1,
  reason: undefined as string | undefined
})

// 发布form
const formPublishData = ref({
  notification: 1,
  ids: [],
  publishStatus: ClassPublishStatusEnum.PUBLISHED
})

// 判断时间是否在最近24小时内
const isWithinNext24Hours = (time: string) => {
  const targetTime = new Date(time)
  const now = new Date()
  const twentyFourHoursLater = new Date(now.getTime() + 24 * 60 * 60 * 1000)

  // 课堂时间在现在和未来24小时之间
  return targetTime >= now && targetTime <= twentyFourHoursLater
}

// 主要判断逻辑 - 返回true时禁用按钮，返回false时启用按钮
const isPostpone = (item: ClassInfoRespVO) => {
  const timeString = `${item.startDate} ${item.startTime}`
  const isWithin24Hours = isWithinNext24Hours(timeString)
  const isValidStatus =
    item.status === ClassStatusEnum.DRAFT || item.status === ClassStatusEnum.NOT_STARTED
  // 禁用条件：状态不是draft/Not Started 或者 距离开课不足24小时
  return !isValidStatus || isWithin24Hours
}

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await ClassInfoApi.getClassInfoPage({
      ...queryParams,
      // 将时间默认不展示在界面时间组件上
      startDate: queryParams.startDate.length === 0 ? getMonthStartAndEnd(1) : queryParams.startDate
    })
    // 延期动作在课堂开始前24小时 关于Postpone操作
    list.value = data.list?.map((item: ClassInfoRespVO) => {
      return {
        ...item,
        isPostpone: isPostpone(item)
      }
    })
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  timeType.value = undefined
  queryFormRef.value.resetFields()
  handleQuery()
}

// 每次切换日历都需要重新刷新数据,保持数据是最新的
const handleCalendar = () => {
  isTable.value = false
  isCalendar.value = true
  scheduleRef.value.handleSubmit()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number, courseId?: number) => {
  formRef.value.open(type, id, courseId)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await ClassInfoApi.deleteClassInfo(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 复制按钮操作 */
const handleCopy = async (id: number) => {
  try {
    formClassData.value.name = ''
    const data = await ClassInfoApi.getClassInfo(id)
    classInfo.value = data
    getClassCode(data.courseName, data.courseId)
    // 删除详情信息中的id值
    delete data.id
    showClass.value = true
  } catch {}
}

/** 获取课堂编号 */
const getClassCode = async (courseName: string, courseId: number) => {
  const data = await ClassInfoApi.getClassCode(courseId)
  // 先拼接字符串再相加
  const code = Number(getCurrentDateFormatted() + 1001) + data
  formClassData.value.name = `${courseName} - ${code}`
  classInfo.value.code = code
}

const submitCopyForm = async () => {
  // 校验表单
  await formClassRef.value.validate()
  // 提交请求
  formClassLoading.value = true
  try {
    classInfo.value.name = formClassData.value.name
    await ClassInfoApi.createClassInfo(classInfo.value)
    message.success(t('common.copySuccess'))
    // 刷新列表
    await getList()
    showClass.value = false
  } finally {
    formClassLoading.value = false
  }
}

/** 发布按钮操作 */
const publishClass = async (id: number, type: number) => {
  // 默认发送提醒
  formPublishData.value.notification = 1
  showPublish.value = true
  if (type === 1) {
    formPublishData.value.ids = [id]
  } else {
    formPublishData.value.ids = checkClass.value
  }
  showPublish.value = true
}

// 最终发布提交
const submitPublishForm = async () => {
  try {
    formLoading.value = true
    await ClassInfoApi.publishClass(formPublishData.value)
    message.success(t('academy.class.publishedSuccessfully'))
    // 刷新列表
    await getList()
    formPublishData.value.ids = []
    showPublish.value = false
  } finally {
    formLoading.value = false
  }
}

/** postpone按钮操作 */
const openClass = (item: ClassInfoRespVO) => {
  postponeClassRef.value.open(item)
}

/** 打开cancel按钮操作 */
const handleCancel = (id: number) => {
  formData.value.id = id
  // 默认为1,后面让用户自己去选择要不要发送消息
  formData.value.notification = 1
  formData.value.reason = undefined
  showCancel.value = true
}
/** cancel按钮操作 */
const submitForm = async () => {
  formLoading.value = true
  try {
    if (!formData.value.id) {
      throw new Error('Missing class id')
    }
    const payload = {
      classId: Number(formData.value.id),
      reason: String(formData.value.reason || ''),
      notification: Number(formData.value.notification || 0)
    }
    await ClassInfoApi.cancelClass(payload)
    await message.success(t('academy.class.cancelSuccessfully'))
    showCancel.value = false
    await getList()
  } finally {
    formLoading.value = false
  }
}

const jumpManage = (id: number) => {
  router.push({
    name: 'Manage',
    query: { id }
  })
}

// 选择时间
const changeTime = (val: number) => {
  switch (val) {
    case 1:
      // 获取当前天的星期一到星期天的开始时间和结束时间
      queryParams.startDate = getWeekStartAndEnd(1)
      break
    case 2:
      // 获取当前天的月的开始时间和结束时间
      queryParams.startDate = getMonthStartAndEnd(1)
      break
    case 3:
      // 获取当前天的季度的开始时间和结束时间
      queryParams.startDate = getQuarterStartAndEnd(1)
      break
    case 4:
      // 获取当前年的开始时间和结束时间
      queryParams.startDate = getYearStartAndEnd(1)
      break
    case 5:
      // 获取当前天的下个月开始时间和结束时间
      queryParams.startDate = getNextMonthStartAndEnd(1)
      break
    default:
      // 当 val 不是 1, 3, 4, 5 时清除时间, 自定义搜索时间
      queryParams.startDate = []
      break
  }
}

/** 选择条数  */
const handleSelectionChange = (selection: ClassInfoRespVO[]) => {
  checkClass.value = []
  selection.forEach((item) => {
    checkClass.value.push(item.id)
  })
}

/** 获取课程信息 */
const getCourseList = async () => {
  const data = await CourseApi.getCoursePage({ pageNo: 1, pageSize: -1 })
  courseList.value = data.list
}

/** 初始化 **/
onMounted(() => {
  getList()
  getCourseList()
  if (route.query.id) {
    formRef.value.open('update', route.query.id)
  }
})
</script>
