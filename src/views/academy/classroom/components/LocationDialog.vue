<template>
  <Dialog v-model="dialogVisible" title="New Location" width="500" @close="handleClose">
    <el-form :model="formData" ref="formRef" :rules="formRules" label-width="150px">
      <el-form-item label="Location Name" prop="name">
        <el-input v-model="formData.name" placeholder="Please enter location name" clearable />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">Cancel</el-button>
        <el-button type="primary" :loading="formLoading" @click="handleConfirm">
          Confirm
        </el-button>
      </div>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import { ClassRoomApi } from '@/api/academy/classroom'
/** 定义组件名称 */
defineOptions({ name: 'LocationDialog' })

/** 组件参数和事件 */
const emit = defineEmits(['success'])

/** 响应式数据 */
const dialogVisible = ref(false)
const formLoading = ref(false)
const formRef = ref()
const message = useMessage()

const formData = ref({
  name: ''
})

const formRules = reactive({
  name: [
    { required: true, message: 'Location name is required', trigger: 'blur' },
    { min: 2, max: 50, message: 'Location name should be 2-50 characters', trigger: 'blur' }
  ]
})

/** 打开对话框 */
const open = () => {
  dialogVisible.value = true
  resetForm()
}

/** 重置表单 */
const resetForm = () => {
  formData.value.name = ''
  if (formRef.value?.resetFields) {
    formRef.value.resetFields()
  }
}

/** 关闭对话框 */
const handleClose = () => {
  dialogVisible.value = false
  resetForm()
}

/** 确认创建 */
const handleConfirm = async () => {
  try {
    await formRef.value.validate()
    formLoading.value = true

    // 调用 API 创建位置
    await ClassRoomApi.createLocation(formData.value)

    message.success('Location created successfully')
    dialogVisible.value = false
    resetForm()

    // 通知父组件刷新数据
    emit('success')
  } catch (error) {
    console.error('Create location failed:', error)
    message.error('Failed to create location')
  } finally {
    formLoading.value = false
  }
}

/** 暴露方法给父组件 */
defineExpose({ open })
</script>
