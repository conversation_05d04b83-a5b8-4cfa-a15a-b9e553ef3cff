<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item :label="t('academy.classroom.location')" prop="location">
        <div class="flex gap-2 items-center">
          <el-select
            v-model="queryParams.location"
            :placeholder="t('academy.classroom.pleaseSelect')"
            clearable
            class="!w-240px"
          >
            <el-option
              v-for="location in locationList"
              :key="location.id"
              :label="location.name"
              :value="location.id"
            />
          </el-select>
          <el-button type="primary" plain size="small" @click="openLocationDialog">
            <Icon icon="ep:plus" class="mr-1" /> Location
          </el-button>
        </div>
      </el-form-item>

      <el-form-item :label="t('academy.classroom.status')" prop="status">
        <el-select
          v-model="queryParams.status"
          :placeholder="t('academy.classroom.pleaseSelect')"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.CLASSROOM_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="t('academy.classroom.time')" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          :start-placeholder="t('academy.classroom.startDate')"
          :end-placeholder="t('academy.classroom.endDate')"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-220px"
        />
      </el-form-item>
      <el-form-item :label="t('academy.classroom.name')" prop="name">
        <el-input
          v-model="queryParams.name"
          :placeholder="t('academy.classroom.pleaseInput')"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"
          ><Icon icon="ep:search" class="mr-5px" /> {{ t('academy.classroom.search') }}</el-button
        >
        <el-button @click="resetQuery"
          ><Icon icon="ep:refresh" class="mr-5px" /> {{ t('academy.classroom.reset') }}</el-button
        >
        <el-button
          v-hasPermi="['academy:class-room:create']"
          type="primary"
          plain
          @click="openForm('create')"
        >
          <Icon icon="ep:plus" class="mr-5px" /> {{ t('academy.classroom.add') }}
        </el-button>
        <!--        暂时注释,随用随解-->
        <!--        <el-button-->
        <!--          type="primary"-->
        <!--          plain-->
        <!--          :disabled="checkClassroom.length === 0"-->
        <!--          @click="handleDelete(undefined,2)"-->
        <!--        >-->
        <!--          <Icon icon="ep:delete" class="mr-5px" /> Delete-->
        <!--        </el-button>-->
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table
      v-loading="loading"
      :data="list"
      :stripe="true"
      :show-overflow-tooltip="true"
      @selection-change="handleSelectionChange"
    >
      <!--      <el-table-column type="selection" width="50" fixed="left" />-->
      <el-table-column
        :label="t('academy.classroom.name')"
        align="center"
        prop="name"
        min-width="180px"
      />
      <el-table-column
        :label="t('academy.classroom.location')"
        align="center"
        prop="location"
        min-width="180px"
      >
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.CLASSROOM_LOCATION" :value="scope.row.location" />
        </template>
      </el-table-column>
      <el-table-column
        :label="t('academy.classroom.classroomNumber')"
        align="center"
        prop="roomNumber"
        min-width="180px"
      />
      <el-table-column
        :label="t('academy.classroom.status')"
        align="center"
        prop="status"
        min-width="180px"
      >
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.CLASSROOM_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column
        :label="t('academy.classroom.totalSeats')"
        align="center"
        prop="totalSeats"
        min-width="180px"
      />
      <el-table-column :label="t('academy.classroom.action')" align="center" min-width="180px">
        <template #default="scope">
          <el-button
            v-hasPermi="['academy:class-room:update']"
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
          >
            {{ t('academy.classroom.edit') }}
          </el-button>
          <el-button link type="primary" @click="openView('view', scope.row.id)">
            {{ t('academy.classroom.view') }}
          </el-button>
          <el-button
            v-hasPermi="['academy:class-room:delete']"
            link
            type="danger"
            @click="handleDelete(scope.row.id, 1)"
          >
            {{ t('academy.classroom.delete') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <ClassRoomForm ref="formRef" @success="getList" />
  <ClassRoomDetail ref="classRoomRef" />

  <!-- Location 对话框组件 -->
  <LocationDialog ref="locationDialogRef" @success="handleLocationSuccess" />
</template>

<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import {
  ClassRoomApi,
  type ClassRoomRespVO,
  type ClassLocationRespVO
} from '@/api/academy/classroom'

import ClassRoomForm from './ClassRoomForm.vue'
import ClassRoomDetail from './Detail/index.vue'
import LocationDialog from './components/LocationDialog.vue'

/** 教室信息 列表 */
defineOptions({ name: 'ClassRoom' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(false) // 列表的加载中
const list = ref<ClassRoomRespVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const locationList = ref<ClassLocationRespVO[]>([]) // 位置列表数据
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  name: undefined,
  status: undefined,
  location: undefined,
  createTime: []
})
const queryFormRef = ref() // 搜索的表单
const formRef = ref()
const classRoomRef = ref()
const locationDialogRef = ref() // Location 对话框 ref

/**  已选择的教室ID */
const checkClassroom = ref([])
/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await ClassRoomApi.getClassRoomPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 获取位置列表 */
const getLocationList = async () => {
  try {
    const response = await ClassRoomApi.getLocationList()
    // 从分页响应中提取 list 数据
    locationList.value = response?.list || []
  } catch (error) {
    console.error('获取位置列表失败:', error)
    locationList.value = []
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

const openView = (type: string, id?: number) => {
  classRoomRef.value.open(type, id)
}

/** 选择条数  */
const handleSelectionChange = (selection: ClassRoomRespVO[]) => {
  checkClassroom.value = []
  selection.forEach((item) => {
    checkClassroom.value.push(item.id)
  })
}

/** 删除按钮操作 */
const handleDelete = async (id: number, type?: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    let idStr
    if (type === 1) {
      idStr = id
    } else {
      idStr = checkClassroom.value.join(',')
    }
    // 发起删除
    await ClassRoomApi.deleteClassRoom(idStr)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** Location 对话框相关方法 */
const openLocationDialog = () => {
  locationDialogRef.value?.open()
}

const handleLocationSuccess = () => {
  // Location 创建成功后刷新位置列表
  getLocationList()
  console.log('Location created successfully')
}

/** 初始化 **/
onMounted(() => {
  getList()
  getLocationList()
})
</script>
