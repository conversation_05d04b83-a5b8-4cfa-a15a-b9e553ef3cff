<template>
  <el-drawer :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="140px"
      v-loading="formLoading"
      label-position="top"
    >
      <el-form-item :label="t('academy.classroom.name')" prop="name">
        <el-input v-model="formData.name" :placeholder="t('academy.classroom.pleaseEnterName')" />
      </el-form-item>
      <el-form-item :label="t('academy.classroom.location')" prop="location">
        <el-select
          v-model="formData.location"
          :placeholder="t('academy.classroom.pleaseSelect')"
          @change="changeLocation"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.CLASSROOM_LOCATION)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="t('academy.classroom.cover')">
        <el-row :gutter="20" class="mt-2.5">
          <el-col :span="24">
            <el-radio-group v-model="isCover" @change="changeCover">
              <el-radio :label="1">{{ t('academy.classroom.default') }}</el-radio>
              <el-radio :label="2">{{ t('academy.classroom.customize') }}</el-radio>
            </el-radio-group>
          </el-col>
          <el-col :span="12">
            <ImageUpload
              v-if="isCover === 2"
              v-model="formData.picture"
              class="mt-2.5"
              :limit="1"
              :is-show-tip="true"
              :file-size="500"
              :tip-text="t('category.topic.coverPH')"
              :upload-module="1"
            />
            <el-image
              v-if="isCover === 1"
              src="https://img-s-msn-com.akamaized.net/tenant/amp/entityid/AAOEcdM.img"
              alt=""
              class="w-[150px] h-[150px] mt-2.5"
            />
          </el-col>
        </el-row>
      </el-form-item>
      <el-form-item :label="t('academy.classroom.totalSeats')" prop="totalSeats">
        <el-input-number v-model="formData.totalSeats" :min="4" />
      </el-form-item>
      <el-form-item :label="t('academy.classroom.description')" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          :placeholder="t('academy.classroom.pleaseInputDescription')"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="dialogVisible = false">{{ t('academy.classroom.cancel') }}</el-button>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">{{
        t('academy.classroom.save')
      }}</el-button>
    </template>
  </el-drawer>
</template>
<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { ClassRoomApi, ClassRoomSaveVO, RoomNumberVO } from '@/api/academy/classroom'
import ImageUpload from '@/components/ImageUpload/index.vue'
/** 教室信息 表单 */
defineOptions({ name: 'ClassRoomForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  name: undefined,
  roomNumber: undefined,
  location: undefined,
  picture: undefined,
  totalSeats: undefined,
  description: undefined
})
const formRules = reactive({
  name: [{ required: true, message: t('academy.classroom.nameRequired'), trigger: 'blur' }],
  roomNumber: [
    { required: true, message: t('academy.classroom.roomNumberRequired'), trigger: 'blur' }
  ],
  status: [{ required: true, message: t('academy.classroom.statusRequired'), trigger: 'change' }],
  location: [
    { required: true, message: t('academy.classroom.locationRequired'), trigger: 'change' }
  ],
  totalSeats: [
    { required: true, message: t('academy.classroom.totalSeatsRequired'), trigger: 'blur' }
  ]
})
const formRef = ref() // 表单 Ref
const isCover = ref(1)
const roomNumberOption = ref<RoomNumberVO[]>([
  { label: '1001', value: 1 },
  { label: '1002', value: 2 },
  { label: '1003', value: 3 }
])

const changeLocation = (val: number) => {
  const item = roomNumberOption.value.find((item: RoomNumberVO) => item.value === val)
  formData.value.roomNumber = item.label ? item.label : ''
}
/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await ClassRoomApi.getClassRoom(id)
      changeLocation(formData.value.location)
      isCover.value = formData.value.picture ? 2 : 1
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as ClassRoomSaveVO
    if (formType.value === 'create') {
      await ClassRoomApi.createClassRoom(data)
      message.success(t('common.createSuccess'))
    } else {
      await ClassRoomApi.updateClassRoom(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 切换清空自定义上传封面 */
const changeCover = (val: number | string) => {
  if (val === 1) {
    formData.value.picture = ''
  }
}
/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    name: undefined,
    roomNumber: undefined,
    location: undefined,
    picture: undefined,
    totalSeats: undefined,
    description: undefined
  }
  formRef.value?.resetFields()
}
</script>
