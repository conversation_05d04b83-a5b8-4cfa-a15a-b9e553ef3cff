<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" width="800px">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="140px"
      v-loading="formLoading"
    >
      <el-form-item :label="t('learningCenter.course.course')" prop="courseId">
        <el-select
          v-model="formData.courseId"
          :placeholder="t('select.pleaseSelect')"
          clearable
          class="!w-100%"
        >
          <el-option
            v-for="item in courseList"
            :key="item.id"
            :label="item.title"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="t('sys.user.userName')" prop="userId">
        <div class="w-100% flex">
          <el-input
            :value="
              selectedUser
                ? `${selectedUser.nickname} (${selectedUser.badgeNumber || selectedUser.username})`
                : ''
            "
            :placeholder="'请选择用户'"
            readonly
            class="flex-1"
          />
          <el-button @click="openUserSelector" class="ml-2" type="primary" plain>
            <Icon icon="ep:more" />
          </el-button>
        </div>
      </el-form-item>

      <el-form-item :label="t('academy.class.date')" prop="preferredDateRange">
        <el-date-picker
          v-model="formData.preferredDateRange"
          type="daterange"
          :start-placeholder="t('academy.class.startTime')"
          :end-placeholder="t('academy.class.endTime')"
          value-format="YYYY-MM-DD"
          class="!w-100%"
        />
      </el-form-item>

      <el-form-item :label="'Translator'" prop="translator">
        <el-select
          v-model="formData.translator"
          :placeholder="t('select.pleaseSelect')"
          clearable
          class="!w-100%"
        >
          <el-option
            v-for="option in translatorLanguageOptions"
            :key="option.key"
            :label="option.label"
            :value="option.key"
          />
        </el-select>
      </el-form-item>

      <el-form-item :label="t('academy.course.language')" prop="language">
        <el-select
          v-model="formData.language"
          :placeholder="t('select.pleaseSelect')"
          clearable
          class="!w-100%"
        >
          <el-option
            v-for="option in languageOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="dialogVisible = false">{{ t('common.cancel') }}</el-button>
      <el-button type="primary" @click="submitForm" :loading="formLoading">
        {{ t('global.confirm') }}
      </el-button>
    </template>
  </Dialog>

  <EmployeeSelect ref="employeeSelectRef" @confirm="handleUserSelect" />
</template>

<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { WaitingApi, WaitingCreateVO } from '@/api/academy/waiting'
import { CourseApi, CourseRespVO } from '@/api/academy/course'
import { UserRespVO } from '@/api/system/user'
import EmployeeSelect from '@/components/EmployeeExclusiveSelect/index.vue'

defineOptions({ name: 'AddWaitingList' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中

const formRef = ref() // 表单 Ref
const employeeSelectRef = ref() // 员工选择器 Ref
const courseList = ref<CourseRespVO[]>([]) // 课程列表
const selectedUser = ref<UserRespVO>() // 选中的用户

// 语言选项 - 根据选中的课程动态显示
const languageOptions = computed(() => {
  if (!formData.value.courseId) {
    return []
  }
  const selectedCourse = courseList.value.find((course) => course.id === formData.value.courseId)
  if (!selectedCourse || !selectedCourse.languageList) {
    return []
  }

  // 获取所有语言字典选项
  const allLanguageOptions = getIntDictOptions(DICT_TYPE.TRAINING_COURSE_LANGUAGE)
  console.log(
    'aaa',
    allLanguageOptions.filter((option) => selectedCourse.languageList.includes(option.value))
  )
  // 过滤出课程绑定的语言
  return allLanguageOptions.filter((option) => selectedCourse.languageList.includes(option.value))
})

// Translator语言枚举
enum TrainingLanguageEnum {
  No = 0,
  EN = 1,
  AR = 2,
  CN = 3
}

// Translator语言选项
const translatorLanguageOptions = ref([
  {
    label: 'No',
    key: TrainingLanguageEnum.No
  },
  {
    label: 'EN',
    key: TrainingLanguageEnum.EN
  },
  {
    label: 'AR',
    key: TrainingLanguageEnum.AR
  },
  {
    label: 'CN',
    key: TrainingLanguageEnum.CN
  }
])

/**
 * 申请类型枚举
 */
enum WaitingTypeEnum {
  ADD_MYSELF = 1,
  ADD_OTHER = 2
}

// 表单数据
const formData = ref({
  userId: undefined as number | undefined,
  courseId: undefined as number | undefined,
  startPreferredDate: '',
  endPreferredDate: '',
  language: undefined as number | undefined,
  translator: undefined as number | undefined,
  preferredDateRange: [] as string[]
})

// 表单校验规则
const formRules = reactive({
  courseId: [{ required: true, message: t('select.pleaseSelect'), trigger: 'change' }],
  language: [{ required: true, message: t('select.pleaseSelect'), trigger: 'change' }],
  translator: [{ required: true, message: t('select.pleaseSelect'), trigger: 'change' }],
  preferredDateRange: [{ required: true, message: t('select.pleaseSelect'), trigger: 'change' }],
  userId: [{ required: true, message: t('select.pleaseSelect'), trigger: 'change' }]
})

/** 打开弹窗 */
const open = async () => {
  dialogVisible.value = true
  dialogTitle.value = t('action.add') + ' Waiting List'
  resetForm()
  await getCourseList()
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    userId: undefined,
    courseId: undefined,
    startPreferredDate: '',
    endPreferredDate: '',
    language: undefined,
    translator: undefined,
    preferredDateRange: []
  }
  formRef.value?.resetFields()
  selectedUser.value = undefined
}

/** 打开用户选择器 */
const openUserSelector = () => {
  const preSelectedId = formData.value.userId || 0
  console.log('打开用户选择器，预选中ID:', preSelectedId)
  employeeSelectRef.value.open(preSelectedId)
}

/** 处理用户选择 */
const handleUserSelect = (selectedEmployee: any) => {
  console.log('EmployeeSelect返回的数据:', selectedEmployee)
  console.log('数据类型:', typeof selectedEmployee)
  console.log('是否为数组:', Array.isArray(selectedEmployee))

  // 如果返回的是数组，取第一个元素
  let employee = selectedEmployee
  if (Array.isArray(selectedEmployee) && selectedEmployee.length > 0) {
    employee = selectedEmployee[0]
    console.log('从数组中取出的员工:', employee)
  }

  if (employee && employee.id) {
    console.log('选中的员工:', employee)
    selectedUser.value = {
      id: employee.id,
      nickname: employee.nickname,
      username: employee.username || employee.nickname,
      badgeNumber: employee.badgeNumber || ''
    } as UserRespVO
    formData.value.userId = employee.id
    console.log('设置的selectedUser:', selectedUser.value)
    console.log('设置的formData.userId:', formData.value.userId)
  } else {
    console.log('没有有效的员工数据')
    selectedUser.value = undefined
    formData.value.userId = undefined
  }
}

/** 获取课程列表 */
const getCourseList = async () => {
  try {
    console.log('开始获取课程列表...')
    const data = await CourseApi.getCoursePage({
      pageNo: 1,
      pageSize: -1
    })
    console.log('课程API返回数据:', data)
    courseList.value = data.list
    console.log('设置课程列表:', courseList.value)
  } catch (error) {
    console.error('获取课程列表失败:', error)
  }
}

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调

const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()

  // 处理日期范围
  if (formData.value.preferredDateRange && formData.value.preferredDateRange.length === 2) {
    formData.value.startPreferredDate = formData.value.preferredDateRange[0]
    formData.value.endPreferredDate = formData.value.preferredDateRange[1]
  }

  formLoading.value = true
  try {
    const data: WaitingCreateVO = {
      userIds: [formData.value.userId!],
      courseId: formData.value.courseId!,
      startPreferredDate: formData.value.startPreferredDate,
      endPreferredDate: formData.value.endPreferredDate,
      language: formData.value.language!,
      translator: formData.value.translator!,
      type: WaitingTypeEnum.ADD_OTHER
    }

    await WaitingApi.createWaitingList(data)
    message.success(t('global.addSuccess'))
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

// 监听课程选择变化，清空语言选择
watch(
  () => formData.value.courseId,
  (newCourseId, oldCourseId) => {
    if (newCourseId !== oldCourseId) {
      formData.value.language = undefined
    }
  }
)

defineExpose({ open }) // 提供 open 方法，用于打开弹窗
</script>
