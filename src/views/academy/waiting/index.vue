<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="120px"
    >
      <el-form-item :label="t('academy.course.course')" prop="courseId">
        <el-select
          v-model="queryParams.courseId"
          :placeholder="t('academy.classroom.pleaseSelect')"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="item in courseList"
            :key="item.id"
            :label="item.title"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="t('academy.course.language')" prop="language">
        <el-select
          v-model="queryParams.language"
          :placeholder="t('academy.classroom.pleaseSelect')"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.TRAINING_COURSE_LANGUAGE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="t('sys.user.company')" prop="companyId">
        <el-tree-select
          v-model="queryParams.companyId"
          :data="companyList"
          :props="{ value: 'id', label: 'name', children: 'children' }"
          value-key="id"
          :placeholder="t('sys.user.companyPH')"
          check-strictly
          clearable
          filterable
          class="!w-240px"
          @node-click="handleCompanyClick"
        />
      </el-form-item>
      <el-form-item :label="t('sys.user.department')" prop="deptId">
        <el-tree-select
          v-model="queryParams.deptId"
          :data="departOptions"
          :props="{ value: 'id', label: 'name', children: 'children' }"
          value-key="id"
          :placeholder="t('sys.user.departmentPH')"
          check-strictly
          clearable
          filterable
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item :label="t('academy.class.date')" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          :start-placeholder="t('common.startDate')"
          :end-placeholder="t('common.endDate')"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-220px"
        />
      </el-form-item>
      <el-form-item :label="t('academy.classroom.name')" prop="nickname">
        <el-input
          v-model="queryParams.nickname"
          :placeholder="t('academy.classroom.pleaseInput')"
          clearable
          @input="getUserList"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item :label="t('academy.class.badgeNo')" prop="badgeNumber">
        <el-input
          v-model="badgeNumber"
          :placeholder="t('academy.classroom.pleaseInput')"
          clearable
          @input="getUserListByBadge"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"
          ><Icon icon="ep:search" class="mr-5px" /> {{ t('academy.class.search') }}</el-button
        >
        <el-button @click="resetQuery"
          ><Icon icon="ep:refresh" class="mr-5px" /> {{ t('academy.class.reset') }}</el-button
        >
        <el-button type="primary" @click="handleAdd" v-hasPermi="['academy:waiting-list:create']">
          <Icon icon="ep:plus" class="mr-5px" />
          {{ t('action.add') }}
        </el-button>
        <el-button
          type="primary"
          class="ms-3"
          :disabled="checkClass.length === 0"
          @click="changeBatchAction(1)"
        >
          <Icon icon="ep:operation" class="mr-5px" /> {{ t('common.batchAction') }}
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table
      v-loading="loading"
      :data="list"
      :stripe="true"
      :show-overflow-tooltip="true"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="50" fixed="left" />
      <el-table-column :label="t('common.preferredDate')" align="center" min-width="200px">
        <template #default="scope">
          {{ formatDateTimeArray(scope.row.startPreferredDate) }} -
          {{ formatDateTimeArray(scope.row.endPreferredDate) }}
        </template>
      </el-table-column>
      <el-table-column
        :label="t('common.receivedDate')"
        align="center"
        prop="createTime"
        min-width="180px"
        :formatter="dateFormatter"
      />
      <el-table-column
        :label="t('academy.class.fullRefresher')"
        align="center"
        prop="firstStudy"
        min-width="180px"
      >
        <template #default="scope">
          {{
            firstStudyOptions.find((firstStudy) => firstStudy.value === scope.row?.firstStudy)
              ?.label
          }}
        </template>
      </el-table-column>
      <el-table-column
        :label="t('common.courseTitle')"
        align="center"
        prop="courseName"
        min-width="180px"
      />
      <el-table-column
        :label="t('academy.course.language')"
        align="center"
        prop="language"
        min-width="180px"
      >
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.TRAINING_COURSE_LANGUAGE" :value="scope.row.language" />
        </template>
      </el-table-column>
      <el-table-column
        :label="t('academy.classroom.name')"
        align="center"
        prop="nickname"
        min-width="180px"
      />
      <el-table-column
        :label="t('academy.class.badgeNo')"
        align="center"
        prop="badgeNo"
        min-width="180px"
      />
      <el-table-column
        :label="t('sys.user.company')"
        align="center"
        prop="companyName"
        min-width="180px"
      />
      <el-table-column
        :label="t('common.workType')"
        align="center"
        prop="workType"
        min-width="180px"
      >
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.SYSTEM_USER_WORK_TYPE" :value="scope.row.workType" />
        </template>
      </el-table-column>
      <el-table-column
        :label="t('common.workTerms')"
        align="center"
        prop="workTerms"
        min-width="180px"
      >
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.SYSTEM_USER_WORK_TERMS" :value="scope.row.workTerms" />
        </template>
      </el-table-column>
      <el-table-column
        :label="t('academy.class.position')"
        align="center"
        prop="positionName"
        min-width="180px"
      />
      <el-table-column :label="t('common.email')" align="center" prop="email" min-width="180px" />
      <el-table-column
        :label="t('common.phoneNumber')"
        align="center"
        prop="phoneNumber"
        min-width="180px"
      />
      <!--      <el-table-column label="Contract Holder" align="center" prop="name" min-width="180px" />-->
      <el-table-column
        :label="t('sys.user.department')"
        align="center"
        prop="deptName"
        min-width="180px"
      />
      <el-table-column :label="t('table.action')" align="center" fixed="right" min-width="120px">
        <template #default="scope">
          <!--          标注: 此处传递的是课程的id，打开弹框调用课堂接口将课程id传给后台，获取到都是一样的课程信息-->
          <el-button
            v-hasPermi="['academy:waiting-list:create']"
            link
            type="primary"
            @click="handleAssign(scope.row.userId, scope.row.courseId)"
          >
            {{ t('common.assign') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <AssignClass ref="assignClassRef" @success="getList" />
  <AddWaitingList ref="addWaitingListRef" @success="getList" />
</template>

<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { WaitingApi } from '@/api/academy/waiting'
import { listCompany } from '@/api/system/company'
import { DeptRespVO, listDept } from '@/api/system/dept'
import { handlePhaseTree } from '@/utils/tree'
import { getUsers, UserRespVO } from '@/api/system/user'
import AssignClass from './components/AssignClass.vue'
import AddWaitingList from './components/AddWaitingList.vue'
import { useUserStore } from '@/store/modules/user'
import { CourseApi } from '@/api/academy/course'
import { dateFormatter } from '@/utils/formatTime'
import { formatDateTimeArray } from '@/utils/formatDate'

defineOptions({ name: 'Waiting' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const userStore = useUserStore()
const loading = ref(false) // 列表的加载中
const list = ref([]) // 列表的数据
const courseList = ref([]) // 课程信息
const companyList = ref([]) // 公司列表
const departOptions = ref()
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  courseId: undefined,
  language: undefined,
  companyId: undefined,
  deptId: undefined,
  nickname: undefined,
  createTime: [],
  userIds: undefined as string | undefined
})
const queryUserParams = ref({
  pageNo: 1,
  pageSize: 99999,
  nickname: undefined
})
const badgeNumber = ref<string>() // 工号搜索参数
const userList = ref<any>([]) // 存储用户信息
const queryFormRef = ref() // 搜索的表单
const assignClassRef = ref()
const addWaitingListRef = ref()
const checkClass = ref<any[]>([]) // 选择的课堂信息
const actionType = ref()
const actionList = computed(() => [{ label: t('common.batchAssign'), value: 1 }])
interface Options {
  value: boolean
  label: string
}
// 是否为第一次学习课堂(后端返回true或者false)
const firstStudyOptions = computed<Options[]>(() => [
  { label: t('academy.class.full'), value: true },
  { label: t('academy.class.refresher'), value: false }
])
const changeBatchAction = (val: number) => {
  // 取第一个元素的id作为比较基准, 如果其他元素的id与第一个元素id不同，则返回false
  // 批量多选必须是同一个课程
  const isSame = checkClass.value.every((item) => item.courseId === checkClass.value[0].courseId)
  if (!isSame) {
    message.warning(t('common.pleaseSelectSameCourse'))
    actionType.value = undefined
    return
  }
  const ids = checkClass.value.map((item) => item.userId)
  switch (val) {
    case 1:
      handleAssign(ids, checkClass.value[0].courseId)
      break
    default:
      break
  }
}

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await WaitingApi.getWaitingPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 添加waiting list */
const handleAdd = () => {
  addWaitingListRef.value.open()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryUserParams.value = {
    nickname: undefined
  }
  actionType.value = undefined
  queryParams.userIds = undefined
  badgeNumber.value = undefined
  queryFormRef.value.resetFields()
  getDepartmentTree(userStore.user.companyId)
  handleQuery()
}

// 获取公司数据
const getCompanyTree = async () => {
  companyList.value = await listCompany()
}
/** 查询部门下拉树结构 */
const getDepartmentTree = async (companyId: number) => {
  const data = await listDept({ companyId })
  if (data) {
    departOptions.value = handlePhaseTree(data, 'id')
  }
}
/** 点击公司树查询下面的人员 */
const handleCompanyClick = (node: DeptRespVO) => {
  departOptions.value = []
  getDepartmentTree(node.id)
}

/** 查询用户列表  */
const getUserList = async () => {
  queryUserParams.value.nickname = queryParams.nickname
  // 每次调用之前清除一遍用户数据
  queryParams.userIds = undefined
  userList.value = []

  // 如果姓名输入框为空，直接返回，不查询用户
  if (!queryParams.nickname) {
    return
  }

  userList.value = await getUsers(queryUserParams.value)
  if (userList.value.list && userList.value.list.length > 0) {
    // 取第一个用户的id作为查询条件
    queryParams.userIds = String(userList.value.list[0].id)
  }
}

/** 根据工号查询用户列表  */
const getUserListByBadge = async () => {
  if (!badgeNumber.value) {
    queryParams.userIds = undefined
    return
  }

  // 每次调用之前清除一遍用户数据
  queryParams.userIds = undefined
  userList.value = []

  const badgeQueryParams = {
    pageNo: 1,
    pageSize: 99999,
    badgeNumber: badgeNumber.value
  }

  userList.value = await getUsers(badgeQueryParams)
  if (userList.value.list && userList.value.list.length > 0) {
    // 取第一个用户的id作为查询条件
    queryParams.userIds = String(userList.value.list[0].id)
  }
}

/** 分配操作 */
const handleAssign = (userIds: number | number[], courseId: number) => {
  assignClassRef.value.open(userIds, courseId)
}

/** 选择条数  */
const handleSelectionChange = (selection: any[]) => {
  checkClass.value = selection
}

// 获取课程信息
const getCourseList = async () => {
  const data = await CourseApi.getCoursePage({ pageNo: 1, pageSize: -1 })
  courseList.value = data.list
}

/** 初始化 **/
onMounted(() => {
  getList()
  getCourseList()
  getCompanyTree()
  getDepartmentTree(userStore.user.companyId)
})
</script>
