<template>
  <div v-loading="loading">
    <!-- 顶部返回 -->
    <div class="w-full h-[140px] bg-[#E4F4EE] flex rounded-[10px] px-4">
      <div class="flex gap-5 mr-16">
<!--        <el-image :src="formatImgUrl(basicInformation.cover)" class="w-[154px] h-[90px] row-span-2" />-->
        <div class="flex flex-col w-[350px]">
          <span class="text-[#23293A] text-xl break-all my-3">{{ basicInformation?.roomName }} </span>
          <div class="text-[#23293A] break-al text-[#BBBBBB] truncate my-2" >{{ basicInformation?.description }} </div>
        </div>
      </div>
      <div class="ms-auto flex-shrink-0 flex w-[#200px]">
        <div class="mt-3">
<!--          去除后端返回的百分号%-->
          <el-progress type="dashboard" :percentage="basicInformation?.attendanceRate?.replace('%', '')">
            <template #default="{ percentage }">
              <span class="percentage-value">{{ percentage }}%</span>
              <span class="percentage-label">Attendance</span>
            </template>
          </el-progress>
        </div>
        <div class="flex flex-col text-[#BBBBBB] ms-8 text-m mx-auto my-auto">
          <span>Planned attendance: {{ total }}</span>
          <span class="mt-2">Actual attendance: {{ actualAttendanceTotal }}</span>
          <span class="mt-4">No Show: {{ noShowTotal }}</span>
        </div>
      </div>
    </div>
    <ContentWrap class="mt-3">
      <el-form
        class="-mb-15px"
        :model="queryParams"
        ref="queryFormRef"
        :inline="true"
        label-width="110px"
      >
        <el-form-item label="Company" prop="companyId">
          <el-tree-select
            v-model="queryParams.companyId"
            :data="companyList"
            :props="{ value: 'id', label: 'name', children: 'children' }"
            value-key="id"
            :placeholder="t('sys.user.companyPH')"
            check-strictly
            clearable
            filterable
            class="!w-240px"
            @node-click="handleCompanyClick"
          />
        </el-form-item>
        <el-form-item :label="t('sys.user.department')" prop="deptId">
          <el-tree-select
            v-model="queryParams.deptId"
            :data="departOptions"
            :props="{ value: 'id', label: 'name', children: 'children' }"
            value-key="id"
            :placeholder="t('sys.user.departmentPH')"
            check-strictly
            clearable
            filterable
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="Attendance Status" prop="attendance">
          <el-select
            v-model="queryParams.attendance"
            placeholder="Please select"
            clearable
            class="!w-240px"
          >
            <el-option
              v-for="dict in getStrDictOptions(DICT_TYPE.STATISTICS_LIVE_ATTENDANCE)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="Name" prop="nickname">
          <el-input
            v-model="queryParams.nickname"
            placeholder="Please input"
            clearable
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item>
          <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> Search</el-button>
          <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> Reset</el-button>
          <el-button
            type="success"
            plain
            :loading="exportLoading"
            @click="handleExport"
          >
            <Icon icon="ep:download" class="mr-5px" />
            Export
          </el-button>
        </el-form-item>

      </el-form>
    </ContentWrap>
    <!--  列表信息-->
    <ContentWrap>
      <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true" row-key="roomUserId" @selection-change="handleCurrentChange">
        <el-table-column type="selection" :reserve-selection="true" width="55" align="center" />
        <el-table-column label="Name" align="center" prop="nickname" min-width="180px" />
        <el-table-column label="Company" align="center" prop="companyName" min-width="180px" />
        <el-table-column label="Department" align="center" prop="deptName" min-width="180px" />
        <el-table-column label="Badge No." align="center" prop="badgeNumber" min-width="180px" />
        <el-table-column label="Position" align="center" prop="positionNames" min-width="180px" />
        <el-table-column label="Email" align="center" prop="email" min-width="180px" />
        <el-table-column label="Qualifications" align="center" prop="joinType" min-width="180px">
          <template #default="scope">
            <dict-tag :type="DICT_TYPE.STATISTICS_LIVE_JOIN_TYPE" :value="scope.row.joinType" />
          </template>
        </el-table-column>
        <el-table-column label="Attendance Status" align="center" prop="attendance" min-width="180px">
          <template #default="scope">
            {{ scope.row.attendance ? 'Yes' : ' No'}}
          </template>
        </el-table-column>
      </el-table>
    </ContentWrap>
  </div>
</template>
<script setup lang="ts" name="LiveStatisticsDetail">
import { formatImgUrl } from '@/utils'
import { getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { useTagsViewStore } from "@/store/modules/tagsView"
import {LiveDetailRespVO, LiveInfoRespVO, LiveStreamApi} from '@/api/statistics/liveStream'
import download from "@/utils/download"
import { listCompany } from "@/api/system/company"
import { DeptRespVO, listDept } from "@/api/system/dept"
import { getUsers, UserRespVO } from "@/api/system/user"
import { handlePhaseTree } from "@/utils/tree"
import { useUserStore } from "@/store/modules/user"
const { delView } = useTagsViewStore() // 视图操作
const { t } = useI18n()
const userStore = useUserStore()
const message = useMessage() // 消息弹窗
const route = useRoute()
const { router, push, currentRoute } = useRouter() // 路由
const classId = route.query.id
const showWithdraw = ref(false)
const formLoading = ref(false)
const loading = ref(false)
const exportLoading = ref(false)
const basicInformation = ref({})
const queryFormRef = ref()
const list = ref([])
const studentInfo = ref([])
const total = ref(0)
const companyList = ref([])
const departOptions = ref([])
const actualAttendanceTotal = ref(0)
const noShowTotal = ref(0)
const userIds = ref([]) // 存储用户id
const queryParams = reactive({
  companyId: undefined,
  deptId: undefined,
  nickname: undefined,
  id: route.query.id,
  attendance: undefined
})


/** 搜索按钮操作 */
const handleQuery = () => {
  list.value = studentInfo.value.filter(user => {
    let match = true

    // 精确匹配 companyId
    if (queryParams.companyId && user.companyId !== queryParams.companyId) {
      match = false
    }

    // 精确匹配 deptId
    if (queryParams.deptId && user.deptId !== queryParams.deptId) {
      match = false
    }

    // 模糊匹配 nickname
    if (queryParams.nickname) {
      const input = queryParams.nickname.trim().toLowerCase();
      const nickname = user.nickname.toLowerCase();
      if (!nickname.includes(input)) {
        match = false
      }
    }

    // 精确匹配 attendance (布尔值)
    if (queryParams.attendance !== '' && user.attendance !== (queryParams.attendance === 'true')) {
      match = false
    }

    return match
  })
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  getDepartmentTree(userStore.user.companyId)
  list.value = studentInfo.value
}

// 获取直播信息
const getInfo = async (id: number) => {
  basicInformation.value = await LiveStreamApi.getLiveInfo(id)
  if (basicInformation.value?.students) {
    // 用户信息
    list.value = basicInformation.value?.students
    // 单独存储一份
    studentInfo.value = basicInformation.value?.students
    total.value = basicInformation.value?.students?.length
    // 未出席人数
    noShowTotal.value = basicInformation.value?.students?.filter(item => !item.attendance).length
    // 出席人数
    actualAttendanceTotal.value = basicInformation.value?.students?.filter(item => item.attendance).length
  }
}

// 选择直播信息
const handleCurrentChange = (item : LiveDetailRespVO) => {
  userIds.value = item.map(item => item.roomUserId)
}

/** 点击公司树查询下面的人员 */
const handleCompanyClick = (node: DeptRespVO) => {
  departOptions.value = []
  getDepartmentTree(node.id)
}


// 导出直播学生信息
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    exportLoading.value = true
    const data = await LiveStreamApi.exportLiveUser({roomId:  route.query.id, roomUserIds: userIds.value?.join(',')})
    download.excel(data, 'Student.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

// 获取公司数据
const getCompanyTree = async () => {
  companyList.value = await listCompany()
}

/** 查询部门下拉树结构 */
const getDepartmentTree = async (companyId: number) => {
  const data = await listDept({ companyId })
  if (data) {
    departOptions.value = handlePhaseTree(data, 'id')
  }
}

onMounted(() => {
  if (route.query.id) {
    getCompanyTree()
    getDepartmentTree(userStore.user.companyId)
    getInfo(route.query.id as unknown as number)
  }
})
</script>


<style scoped lang="scss">
:deep(.el-divider--vertical) {
  height: 200px;
  margin-left: 40px;
}
.percentage-value {
  display: block;
  margin-top: 10px;
  font-size: 30px;
}
.percentage-label {
  display: block;
  margin-top: 10px;
  font-size: 12px;
}
.demo-progress .el-progress--line {
  margin-bottom: 15px;
  max-width: 600px;
}
.demo-progress .el-progress--circle {
  margin-right: 15px;
}
</style>