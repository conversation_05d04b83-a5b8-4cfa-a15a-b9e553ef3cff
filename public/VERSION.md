# Version Release Notes

## v3.4.10 (09-02-2025)

### ✨ New Features

- Training application approval process optimization with new status selection feature

- Support for selecting "Purchasing" or "In Production" status when approving
- Training application detail page display optimization with badge number field
- Version info page now supports Markdown format changelog display

### 🔧 Improvements

- Approval process UI optimization, replaced dropdown with radio buttons
- Code formatting and structure optimization
- Enhanced approval reason input interaction experience
- Training content display card style optimization

### ⚡ Technical Optimizations

- Vue component performance optimization
- API call logic simplification
- Type definition improvements

### 🐛 Bug Fixes

- Fixed approval status selector display issue
- Fixed template reference errors
- Fixed conditional rendering logic

---

## v3.4.5 (2024-11-20)

No Notes

---

For more historical version information, please check the project Git commit records.